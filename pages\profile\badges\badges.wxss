/* pages/profile/badges.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.categories-container {
  height: calc(100vh - 400rpx);
}

.category-container {
  margin: 32rpx;
}

.category-title {
  font-weight: 600;
  margin-bottom: 32rpx;
  color: #444;
  display: flex;
  align-items: center;
}

.category-title .iconfont {
  color: #a18cd1;
  margin-right: 16rpx;
  font-size: 32rpx;
}

.category-title text {
  font-size: 28rpx;
}

.badges-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.badge-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.badge-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  font-size: 48rpx;
}

.badge-icon.gold {
  background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
  color: white;
}

.badge-icon.silver {
  background: linear-gradient(135deg, #E0E0E0 0%, #BDBDBD 100%);
  color: white;
}

.badge-icon.bronze {
  background: linear-gradient(135deg, #bf8f5a 0%, #ddb892 100%);
  color: white;
}

.badge-icon.purple {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: white;
}

.badge-icon.blue {
  background: linear-gradient(135deg, #90dffe 0%, #38a3d1 100%);
  color: white;
}

.badge-icon.green {
  background: linear-gradient(135deg, #a8edea 0%, #59c173 100%);
  color: white;
}

.badge-icon.locked {
  background: #eee;
  color: #aaa;
}

.badge-icon .iconfont {
  font-size: 48rpx;
}

.badge-name {
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  line-height: 1.3;
  color: #333;
}

.badge-date {
  font-size: 20rpx;
  color: #999;
  margin-top: 12rpx;
}

.badge-locked {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.badge-locked .iconfont {
  font-size: 40rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.progress-pill {
  width: 100%;
  height: 12rpx;
  background-color: #eee;
  border-radius: 6rpx;
  margin-top: 16rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #a18cd1, #fbc2eb);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
  color: #999;
}

.empty-state .iconfont {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  color: #ddd;
}

.empty-text {
  text-align: center;
}

.empty-text text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.empty-text .empty-subtitle {
  font-size: 24rpx;
  color: #999;
}

.badge-detail-popup {
  width: 600rpx;
  background-color: #fff;
  border-radius: 40rpx;
  overflow: hidden;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.detail-header {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
}

.detail-header text {
  font-size: 28rpx;
  color: #333;
}

.detail-header .icon-close {
  font-size: 36rpx;
  color: #999;
  padding: 20rpx;
  margin: -20rpx;
}

.detail-content {
  padding: 40rpx 30rpx;
}

.detail-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.detail-icon.gold {
  background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
}

.detail-icon.silver {
  background: linear-gradient(135deg, #E0E0E0 0%, #BDBDBD 100%);
}

.detail-icon.bronze {
  background: linear-gradient(135deg, #bf8f5a 0%, #ddb892 100%);
}

.detail-icon.purple {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}

.detail-icon.blue {
  background: linear-gradient(135deg, #90dffe 0%, #38a3d1 100%);
}

.detail-icon.green {
  background: linear-gradient(135deg, #a8edea 0%, #59c173 100%);
}

.detail-icon .iconfont {
  font-size: 60rpx;
  color: #fff;
}

.detail-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  display: block;
  margin-bottom: 16rpx;
}

.detail-desc {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  display: block;
  margin-bottom: 30rpx;
}

.detail-progress {
  margin-bottom: 30rpx;
}

.detail-progress .progress-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.detail-progress .progress-pill {
  height: 8rpx;
  background-color: #eee;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.detail-progress .progress-fill {
  height: 100%;
  background: linear-gradient(to right, #a18cd1, #fbc2eb);
  transition: width 0.3s ease;
}

.detail-progress .progress-text {
  font-size: 20rpx;
  color: #999;
  text-align: right;
  display: block;
}

.detail-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.detail-stats .stat-item {
  flex: 1;
  text-align: center;
}

.detail-stats .stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.detail-stats .stat-value {
  font-size: 26rpx;
  color: #333;
  display: block;
}

.detail-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  display: block;
} 