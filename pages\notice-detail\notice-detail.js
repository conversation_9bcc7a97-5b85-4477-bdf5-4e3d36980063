import { getNoticeDetail } from '../../api/home';
import authBehavior from '../../behaviors/auth-behavior';

Page({
  /**
   * 页面的初始数据
   */
  behaviors: [authBehavior],

  data: {
    noticeId: null,
    noticeDetail: null,
    isLoading: true,
    // 导航高度相关
    statusBarHeight: 0,
    navBarHeight: 0,
    totalNavHeight: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const id = options.id;
    if (!id) {
      wx.showToast({
        title: '无效的公告ID',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({ noticeId: id });
    this.loadNoticeDetail(id);
    this.getNavHeight();
  },

  /**
   * 加载公告详情
   */
  async loadNoticeDetail(id) {
    this.setData({ isLoading: true });

    try {
      const result = await getNoticeDetail(id);
      console.log('获取到的公告详情：', result);

      if (result) {
        this.setData({
          noticeDetail: result,
          isLoading: false
        });

        // 标记公告为已读（可选功能）
        this.markAsRead(id);
      } else {
        throw new Error('未获取到数据');
      }
    } catch (error) {
      console.error('获取公告详情失败：', error);
      wx.showToast({
        title: '获取详情失败，请重试',
        icon: 'none'
      });
      this.setData({ isLoading: false });
    }
  },

  /**
   * 标记公告为已读
   */
  async markAsRead(id) {
    try {
      // 这里可以调用标记已读的API
      // await markNoticeAsRead(id);
      console.log('公告已标记为已读:', id);
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    const { noticeDetail } = this.data;
    return {
      title: noticeDetail ? noticeDetail.title : '公告详情',
      path: `/pages/notice-detail/notice-detail?id=${this.data.noticeId}`,
      imageUrl: '' // 可以设置分享图片
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { noticeDetail } = this.data;
    return {
      title: noticeDetail ? noticeDetail.title : '公告详情',
      query: `id=${this.data.noticeId}`,
      imageUrl: '' // 可以设置分享图片
    };
  }
});