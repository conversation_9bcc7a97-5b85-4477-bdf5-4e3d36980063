Component({
  properties: {
    inputContent: {
      type: String,
      value: ''
    },
    isVoiceMode: {
      type: Boolean,
      value: false
    },
    isRecording: {
      type: Boolean,
      value: false
    },
    keyboardHeight: {
      type: Number,
      value: 0
    }
  },
  options: {
    styleIsolation: 'apply-shared'
  },
  
  observers: {
    'inputContent': function(newValue) {
      // 输入内容属性变化时，强制更新内部状态
      // 如果有必要，这里可以进行其他处理
      if (this._inputValue !== newValue) {
        this._inputValue = newValue;

        // 动态调整输入框高度
        this.adjustTextareaHeight(newValue);

        // 检测新内容的行数
        this.checkTextLines(newValue);

        // 如果内容被清空（比如发送消息后），重置输入框状态
        if (!newValue || newValue.trim().length === 0) {
          this.resetInputState();
        }
      }
    }
  },
  
  data: {
    // 内部状态
    recordingTimeout: null,
    maxRecordDuration: 60000, // 最大录音时长 60 秒

    // 输入框展开相关
    showExpandButton: false, // 是否显示展开按钮
    showFullscreenInput: false, // 是否显示全屏输入
    fullscreenInputContent: '', // 全屏输入内容
    maxLines: 2, // 最大显示行数
    lineHeight: 40, // 行高（rpx）
    autoHeight: false, // 是否自动高度，默认关闭避免切换时闪烁
    textareaHeight: 70, // 输入框高度（rpx）
    defaultHeight: 70, // 默认高度（rpx）
    maxHeight: 150 // 最大高度（rpx）
  },
  
  lifetimes: {
    attached() {
      // 组件初始化时，记录初始输入值
      this._inputValue = this.properties.inputContent;
      // 初始化时检测内容行数
      if (this.properties.inputContent) {
        this.checkTextLines(this.properties.inputContent);
      }
    },
    detached() {
      // 组件销毁时，清除定时器
      this.clearRecordingTimeout();
    }
  },
  
  methods: {
    onSendMessage(e) {
      // 优先用事件参数中的输入内容，兼容软键盘发送
      // const content = e && e.detail && typeof e.detail.value === 'string' ? e.detail.value : this.data.inputContent;
      this.triggerEvent('sendMessage', { content:e.detail.value });

      // 发送消息后重置输入框状态
      this.resetInputState();
    },
    
    onInputFocus(e) {
      this.triggerEvent('inputFocus', e.detail);
    },
    
    onInputBlur(e) {
      this.triggerEvent('inputBlur', e.detail);
    },
    
    onToggleInputType() {
      this.triggerEvent('toggleInputType');
    },
    
    onStartRecording(e) {
      this.triggerEvent('startRecording', e);
      
      // 设置超时自动停止录音的定时器
      this.setRecordingTimeout();
    },
    
    // 设置录音超时处理
    setRecordingTimeout() {
      // 清除可能存在的定时器
      this.clearRecordingTimeout();
      
      // 设置新的定时器，在最大录音时长到达时自动触发停止录音
      const recordingTimeout = setTimeout(() => {
        console.log('input-area 录音时长达到上限，自动触发停止录音');
        
        // 创建一个模拟的事件对象
        const timeoutEvent = {
          type: 'timeout',
          timeStamp: Date.now(),
          detail: { source: 'timeout' }
        };
        
        // 触发停止录音事件
        this.onStopRecording(timeoutEvent);
      }, this.data.maxRecordDuration);
      
      this.setData({
        recordingTimeout: recordingTimeout
      });
    },
    
    // 清除录音超时定时器
    clearRecordingTimeout() {
      if (this.data.recordingTimeout) {
        clearTimeout(this.data.recordingTimeout);
        this.setData({
          recordingTimeout: null
        });
      }
    },
    
    onStopRecording(e) {
      // 清除超时定时器
      this.clearRecordingTimeout();
      
      // 创建一个新的事件对象，确保数据能被正确传递
      const eventDetail = {
        timeStamp: e.timeStamp,
        type: e.type,
        detail: e.detail,
        target: e.target,
        currentTarget: e.currentTarget,
        // 确保添加必要的原始事件信息
        originalEvent: e
      };
      
      this.triggerEvent('stopRecording', eventDetail);
    },
    
    onTouchMove(e) {
      this.triggerEvent('touchMove', e);
    },
    
    onCancelRecording(e) {
      // 清除超时定时器
      this.clearRecordingTimeout();
      
      this.triggerEvent('cancelRecording', e);
    },
    
    onInput(e) {
      const value = e.detail.value;

      // 动态调整输入框高度
      this.adjustTextareaHeight(value);

      this.checkTextLines(value);
      this.triggerEvent('inputChange', { value });
    },

    // 动态调整输入框高度
    adjustTextareaHeight(text) {
      if (!text || text.trim().length === 0) {
        // 没有内容时恢复默认高度
        this.setData({
          // textareaHeight: this.data.defaultHeight,
          autoHeight: false
        });
        return;
      }

      // 启用自动高度来计算实际需要的高度
      this.setData({
        autoHeight: true
      });

      // 使用nextTick确保DOM更新后再获取高度
      // wx.nextTick(() => {
      //   this.calculateActualHeight(text);
      // });
    },

    // 计算实际需要的高度
    calculateActualHeight(text) {
      // 简单的行数计算
      const lines = text.split('\n');
      let totalLines = 0;

      lines.forEach(line => {
        if (line.length === 0) {
          totalLines += 1;
        } else {
          // 根据字符数估算行数（每行约25个字符）
          const charsPerLine = 25;
          totalLines += Math.ceil(line.length / charsPerLine);
        }
      });

      // 计算高度：基础高度 + 额外行数 * 行高
      const baseHeight = this.data.defaultHeight;
      const extraLines = Math.max(0, totalLines - 1);
      const calculatedHeight = baseHeight + (extraLines * this.data.lineHeight);

      // 限制最大高度
      const finalHeight = Math.min(calculatedHeight, this.data.maxHeight);

      this.setData({
        // textareaHeight: finalHeight,
        autoHeight: false // 设置固定高度后关闭自动高度
      });
    },

    // 检测文本行数
    checkTextLines(text) {
      if (!text || text.trim().length === 0) {
        this.setData({
          showExpandButton: false,
          autoHeight: false // 没有内容时关闭自动高度
        });
        return;
      }

      // 使用更精确的方法计算行数
      this.calculateTextLines(text).then(lineCount => {
        this.setData({
          showExpandButton: lineCount > this.data.maxLines
        });
      });
    },

    // 计算文本实际行数
    calculateTextLines(text) {
      return new Promise((resolve) => {
        // 简化的行数计算：基于换行符和估算的字符宽度
        const lines = text.split('\n');
        let totalLines = 0;

        lines.forEach(line => {
          if (line.length === 0) {
            totalLines += 1;
          } else {
            // 根据输入框宽度和字体大小估算每行字符数
            // 假设输入框有效宽度约为300rpx，字体28rpx，每个字符约占20rpx
            const charsPerLine = Math.floor(300 / 20); // 约15个字符
            totalLines += Math.ceil(line.length / charsPerLine);
          }
        });

        // 至少1行
        resolve(Math.max(1, totalLines));
      });
    },

    // 展开全屏输入
    onExpandInput() {
      this.setData({
        showFullscreenInput: true,
        fullscreenInputContent: this.properties.inputContent
      });

    },

    // 全屏输入内容变化
    onFullscreenInput(e) {
      this.setData({
        fullscreenInputContent: e.detail.value
      });
    },


    // 确认全屏输入
    onConfirmFullscreenInput() {
      const content = this.data.fullscreenInputContent;
      this.setData({
        showFullscreenInput: false,
        inputContent:content
      });

      // 触发输入变化事件
      this.triggerEvent('inputChange', { value: content });

      // 检测新内容的行数
      this.checkTextLines(content);
    },

    // 阻止弹窗滚动穿透
    preventTouchMove() {
      return false;
    },

    // 重置输入框状态
    resetInputState() {
      this.setData({
        autoHeight: false, // 关闭自动高度
        showExpandButton: false, // 隐藏展开按钮
        showFullscreenInput: false, // 关闭全屏输入
        fullscreenInputContent: '', // 清空全屏输入内容
        textareaHeight: this.data.defaultHeight // 重置为默认高度
      });
    },
  }
}) 