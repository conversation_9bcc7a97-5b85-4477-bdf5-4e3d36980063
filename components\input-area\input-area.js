Component({
  properties: {
    inputContent: {
      type: String,
      value: ''
    },
    isVoiceMode: {
      type: Boolean,
      value: false
    },
    isRecording: {
      type: <PERSON>olean,
      value: false
    },
    keyboardHeight: {
      type: Number,
      value: 0
    }
  },
  options: {
    styleIsolation: 'apply-shared'
  },
  
  observers: {
    'inputContent': function(newValue) {
      // 输入内容属性变化时，强制更新内部状态
      // 如果有必要，这里可以进行其他处理
      if (this._inputValue !== newValue) {
        this._inputValue = newValue;
      }
    }
  },
  
  data: {
    // 内部状态
    recordingTimeout: null,
    maxRecordDuration: 60000 // 最大录音时长 60 秒
  },
  
  lifetimes: {
    attached() {
      // 组件初始化时，记录初始输入值
      this._inputValue = this.properties.inputContent;
      console.log('input-area组件已初始化，初始值:', this._inputValue);
    },
    detached() {
      // 组件销毁时，清除定时器
      this.clearRecordingTimeout();
    }
  },
  
  methods: {
    onSendMessage(e) {
      // 优先用事件参数中的输入内容，兼容软键盘发送
      // const content = e && e.detail && typeof e.detail.value === 'string' ? e.detail.value : this.data.inputContent;
      this.triggerEvent('sendMessage', { content:e.detail.value });
    },
    
    onInputFocus(e) {
      this.triggerEvent('inputFocus', e.detail);
    },
    
    onInputBlur(e) {
      this.triggerEvent('inputBlur', e.detail);
    },
    
    onToggleInputType() {
      this.triggerEvent('toggleInputType');
    },
    
    onStartRecording(e) {
      this.triggerEvent('startRecording', e);
      
      // 设置超时自动停止录音的定时器
      this.setRecordingTimeout();
    },
    
    // 设置录音超时处理
    setRecordingTimeout() {
      // 清除可能存在的定时器
      this.clearRecordingTimeout();
      
      // 设置新的定时器，在最大录音时长到达时自动触发停止录音
      const recordingTimeout = setTimeout(() => {
        console.log('input-area 录音时长达到上限，自动触发停止录音');
        
        // 创建一个模拟的事件对象
        const timeoutEvent = {
          type: 'timeout',
          timeStamp: Date.now(),
          detail: { source: 'timeout' }
        };
        
        // 触发停止录音事件
        this.onStopRecording(timeoutEvent);
      }, this.data.maxRecordDuration);
      
      this.setData({
        recordingTimeout: recordingTimeout
      });
    },
    
    // 清除录音超时定时器
    clearRecordingTimeout() {
      if (this.data.recordingTimeout) {
        clearTimeout(this.data.recordingTimeout);
        this.setData({
          recordingTimeout: null
        });
      }
    },
    
    onStopRecording(e) {
      // 清除超时定时器
      this.clearRecordingTimeout();
      
      // 创建一个新的事件对象，确保数据能被正确传递
      const eventDetail = {
        timeStamp: e.timeStamp,
        type: e.type,
        detail: e.detail,
        target: e.target,
        currentTarget: e.currentTarget,
        // 确保添加必要的原始事件信息
        originalEvent: e
      };
      
      this.triggerEvent('stopRecording', eventDetail);
    },
    
    onTouchMove(e) {
      this.triggerEvent('touchMove', e);
    },
    
    onCancelRecording(e) {
      // 清除超时定时器
      this.clearRecordingTimeout();
      
      this.triggerEvent('cancelRecording', e);
    },
    
    onInput(e) {
      this.triggerEvent('inputChange', { value: e.detail.value });
    },
  }
}) 