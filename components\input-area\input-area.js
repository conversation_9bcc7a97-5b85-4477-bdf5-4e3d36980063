Component({
  properties: {
    inputContent: {
      type: String,
      value: ''
    },
    isVoiceMode: {
      type: Boolean,
      value: false
    },
    isRecording: {
      type: Boolean,
      value: false
    },
    keyboardHeight: {
      type: Number,
      value: 0
    }
  },
  options: {
    styleIsolation: 'apply-shared'
  },
  
  observers: {
    'inputContent': function(newValue) {
      // 输入内容属性变化时，强制更新内部状态
      // 如果有必要，这里可以进行其他处理
      if (this._inputValue !== newValue) {
        this._inputValue = newValue;
        // 检测新内容的行数
        this.checkTextLines(newValue);
      }
    }
  },
  
  data: {
    // 内部状态
    recordingTimeout: null,
    maxRecordDuration: 60000, // 最大录音时长 60 秒

    // 输入框展开相关
    showExpandButton: false, // 是否显示展开按钮
    showFullscreenInput: false, // 是否显示全屏输入
    fullscreenInputContent: '', // 全屏输入内容
    maxLines: 3, // 最大显示行数
    lineHeight: 40, // 行高（rpx）
    autoHeight: true, // 是否自动高度
  },
  
  lifetimes: {
    attached() {
      // 组件初始化时，记录初始输入值
      this._inputValue = this.properties.inputContent;
      // 初始化时检测内容行数
      if (this.properties.inputContent) {
        this.checkTextLines(this.properties.inputContent);
      }
      // this.$nextTick(() => {
      //   this.setData({
      //     autoHeight: true
      //   });
      // });
    },
    detached() {
      // 组件销毁时，清除定时器
      this.clearRecordingTimeout();
    }
  },
  
  methods: {
    onSendMessage(e) {
      // 优先用事件参数中的输入内容，兼容软键盘发送
      // const content = e && e.detail && typeof e.detail.value === 'string' ? e.detail.value : this.data.inputContent;
      this.triggerEvent('sendMessage', { content:e.detail.value });
    },
    
    onInputFocus(e) {
      this.triggerEvent('inputFocus', e.detail);
    },
    
    onInputBlur(e) {
      this.triggerEvent('inputBlur', e.detail);
    },
    
    onToggleInputType() {
      this.triggerEvent('toggleInputType');
    },
    
    onStartRecording(e) {
      this.triggerEvent('startRecording', e);
      
      // 设置超时自动停止录音的定时器
      this.setRecordingTimeout();
    },
    
    // 设置录音超时处理
    setRecordingTimeout() {
      // 清除可能存在的定时器
      this.clearRecordingTimeout();
      
      // 设置新的定时器，在最大录音时长到达时自动触发停止录音
      const recordingTimeout = setTimeout(() => {
        console.log('input-area 录音时长达到上限，自动触发停止录音');
        
        // 创建一个模拟的事件对象
        const timeoutEvent = {
          type: 'timeout',
          timeStamp: Date.now(),
          detail: { source: 'timeout' }
        };
        
        // 触发停止录音事件
        this.onStopRecording(timeoutEvent);
      }, this.data.maxRecordDuration);
      
      this.setData({
        recordingTimeout: recordingTimeout
      });
    },
    
    // 清除录音超时定时器
    clearRecordingTimeout() {
      if (this.data.recordingTimeout) {
        clearTimeout(this.data.recordingTimeout);
        this.setData({
          recordingTimeout: null
        });
      }
    },
    
    onStopRecording(e) {
      // 清除超时定时器
      this.clearRecordingTimeout();
      
      // 创建一个新的事件对象，确保数据能被正确传递
      const eventDetail = {
        timeStamp: e.timeStamp,
        type: e.type,
        detail: e.detail,
        target: e.target,
        currentTarget: e.currentTarget,
        // 确保添加必要的原始事件信息
        originalEvent: e
      };
      
      this.triggerEvent('stopRecording', eventDetail);
    },
    
    onTouchMove(e) {
      this.triggerEvent('touchMove', e);
    },
    
    onCancelRecording(e) {
      // 清除超时定时器
      this.clearRecordingTimeout();
      
      this.triggerEvent('cancelRecording', e);
    },
    
    onInput(e) {
      const value = e.detail.value;
      this.checkTextLines(value);
      this.triggerEvent('inputChange', { value });
    },

    // 检测文本行数
    checkTextLines(text) {
      if (!text) {
        this.setData({ showExpandButton: false });
        return;
      }

      // 使用更精确的方法计算行数
      this.calculateTextLines(text).then(lineCount => {
        this.setData({
          showExpandButton: lineCount > this.data.maxLines
        });
      });
    },

    // 计算文本实际行数
    calculateTextLines(text) {
      return new Promise((resolve) => {
        // 创建查询对象
        const query = this.createSelectorQuery();

        // 先设置文本内容，然后测量
        const tempText = text || 'A'; // 至少有一个字符用于测量

        // 简化的行数计算：基于换行符和估算的字符宽度
        const lines = text.split('\n');
        let totalLines = 0;

        lines.forEach(line => {
          if (line.length === 0) {
            totalLines += 1;
          } else {
            // 根据输入框宽度和字体大小估算每行字符数
            // 假设输入框有效宽度约为300rpx，字体28rpx，每个字符约占20rpx
            const charsPerLine = Math.floor(300 / 20); // 约15个字符
            totalLines += Math.ceil(line.length / charsPerLine);
          }
        });

        // 至少1行
        resolve(Math.max(1, totalLines));
      });
    },

    // 展开全屏输入
    onExpandInput() {
      this.setData({
        showFullscreenInput: true,
        fullscreenInputContent: this.properties.inputContent
      });

    },

    // 全屏输入内容变化
    onFullscreenInput(e) {
      this.setData({
        fullscreenInputContent: e.detail.value
      });
    },


    // 确认全屏输入
    onConfirmFullscreenInput() {
      const content = this.data.fullscreenInputContent;
      this.setData({
        showFullscreenInput: false,
        inputContent:content
      });

      // 触发输入变化事件
      this.triggerEvent('inputChange', { value: content });

      // 检测新内容的行数
      this.checkTextLines(content);
    },

    // 阻止弹窗滚动穿透
    preventTouchMove() {
      return false;
    },
  }
}) 