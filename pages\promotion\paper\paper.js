import { getExamReport } from '../../../api/promotion';
const authBehavior = require('../../../behaviors/auth-behavior');
Page({
  /**
   * 页面的初始数据
   */
  behaviors: [authBehavior],

  data: {
    id: null,
    isLoading: true,
    messages: [], // 存储消息数组
    // 导航高度相关
    statusBarHeight: 0,
    navBarHeight: 0,
    totalNavHeight: 0,
    practiceInfo: {
      date: '',
      duration: '',
      total:0
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

    this.setData({ 
      examId:options.examId,
    });
    this.loadRecordDetail(this.data.examId);
    this.getNavHeight();
  },

  /**
   * 加载练习记录详情
   */
  async loadRecordDetail(id) {
    this.setData({ isLoading: true });
    
    try {
      const result = await getExamReport({
        exam_id:this.data.examId,
        isEarlySubmission:false
      });
      
      if (result) {
        
        // 将details数组转换为messages格式
        const messages = this.convertDetailsToMessages(result.exam || []);
        // 筛选messages中 type为user的item数量
        const userCount=messages.filter(item=>item.type==='user').length;
        // 将messages中 type为user的item数量设置为userCount
        // 获取练习基本信息，但优先使用URL传入的日期和时长
        const practiceInfo = {
          // date: result.examTime,
          total:`${userCount}/${result.questionCount}题`,
          // 使用formatDuration函数将分钟转换为时分秒格式
          duration: `时长：${this.formatDuration(result.usedDuration || 0)}分钟`
          // duration: result.usedDuration
        };
        this.setData({
          messages,
          practiceInfo,
          isLoading: false
        });
        console.log('messages',messages);
      } else {
        throw new Error('未获取到数据');
      }
    } catch (error) {
      console.error('获取练习记录详情失败：', error);
      wx.showToast({
        title: '获取详情失败，请重试',
        icon: 'none'
      });
      this.setData({ isLoading: false });
    }
  },

  /**
   * 将分钟转换为时分秒格式 (hh:mm:ss)
   */
  formatDuration(minutes) {
    if (!minutes || isNaN(minutes)) {
      return '00:00';
    }
    
    // 将分钟转换为秒
    let totalSeconds = Math.floor(minutes * 60);
    
    // 计算时、分、秒
    // const hours = Math.floor(totalSeconds / 3600);
    // totalSeconds %= 3600;
    const mins = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    
    // 格式化为两位数
    const formatNumber = num => num.toString().padStart(2, '0');
    
    // 返回格式化的时间字符串
    return `${formatNumber(mins)}:${formatNumber(seconds)}`;
  },

  /**
   * 将details数组转换为消息格式
   */
  convertDetailsToMessages(details) {
    const messages = [];
    
    details.forEach(item => {
      let type = 'system';
      
      if (item.type === 'question') {
        type = 'ai';
      } else if (item.type === 'answer') {
        type = 'user';
      } else if (item.type === 'analysis') {
        type = 'ai';
      }
      
      messages.push({
        type: 'ai',
        content: item.question,
        time: item.timestamp || ''
      })
      if(item.userAnswer){
        messages.push({
          type: 'user',
          content: item.userAnswer,
          time: item.answerTime || ''
        });
      }
      if(item.analysis){
        messages.push({
          type: 'ai',
          content: item.analysis,
          answerType:'analysis',
          result:item.result
          // time: item.analysisTime || ''
        });
      }

    });
    
    return messages;
  },
  

  /**
   * 滚动到顶部加载更多记录
   */
  onScrollToUpper() {
    // 本页面暂不需要加载更多历史记录
    console.log('已滚动到顶部');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadRecordDetail(this.data.id);
    wx.stopPullDownRefresh();
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

}) 