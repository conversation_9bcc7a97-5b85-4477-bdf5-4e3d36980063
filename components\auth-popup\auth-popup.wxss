/* components/auth-popup/auth-popup.wxss */

/* 授权弹窗样式 */
.auth-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.35s ease;
}

.auth-popup.visible {
  opacity: 1;
  visibility: visible;
}

.auth-popup-content {
  width: 75%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
}

.auth-popup-header {
  padding: 36rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.auth-popup-header::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -2rpx;
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #a18cd1, #fbc2eb);
  transform: translateX(-50%);
  border-radius: 2rpx;
}

.auth-popup-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 16rpx;
}

.auth-popup-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 6rpx;
  height: 28rpx;
  background: linear-gradient(180deg, #a18cd1, #fbc2eb);
  transform: translateY(-50%);
  border-radius: 3rpx;
}

.auth-popup-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
  height: 40rpx;
  width: 40rpx;
  line-height: 40rpx;
  text-align: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.auth-popup-close:active {
  background-color: #f5f5f5;
}

.auth-popup-body {
  padding: 36rpx;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 微信头像相关样式 */
.wechat-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  border-radius: 50%;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content:flex-end;
  background-color: #f5f7fa;
}

.avatar-container .error-message {
  position: absolute;
  top: 100%;
  width: 200rpx;
  text-align: center;
  margin-top: 10rpx;
  white-space: nowrap;
}

.default-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-button {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  border: none;
  opacity: 0;
}

.avatar-button::after {
  border: none;
}

.avatar-button-text {
  font-size: 20rpx;
  color: #fff;
  text-align: center;
}

.nickname-container {
  width: 100%;
  margin-top: 10rpx;
  position: relative;
}

/* 手机号相关样式 */
.phone-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.phone-display {
  font-size: 26rpx;
  color: #333;
  height: 70rpx;
  line-height: 70rpx;
  padding:0 24rpx;
  background-color: #f9f9fa;
  border-radius: 12rpx;
  width: 100%;
  display: block;
  box-sizing: border-box;
  border: 1rpx solid #eee;
}

.wechat-phone-button {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  border-radius: 12rpx;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(161, 140, 209, 0.3);
  transition: all 0.3s ease;
}

.wechat-phone-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
  transform: skewX(-25deg) translateX(-100%);
  transition: all 0.5s ease;
}

.wechat-phone-button:active::after {
  transform: skewX(-25deg) translateX(100%);
}

.wechat-phone-button.hidden {
  display: none;
}


.form-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  position: relative;
  padding-left: 14rpx;
}

.form-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 4rpx;
  height: 16rpx;
  background: linear-gradient(180deg, #a18cd1, #fbc2eb);
  transform: translateY(-50%);
  border-radius: 2rpx;
}

.form-input {
  width: 100%;
  height: 70rpx;
  background-color: #f9f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  font-size: 26rpx;
  color: #333;
  border: 1rpx solid #eee;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #a18cd1;
  box-shadow: 0 0 0 2rpx rgba(161, 140, 209, 0.1);
}

.form-item.has-error .form-input {
  border: 1rpx solid #ff6b6b;
  background-color: rgba(255, 107, 107, 0.05);
}

.error-message {
  display: block;
  font-size: 24rpx;
  color: #ff6b6b;
  margin-top: 10rpx;
  padding-left: 10rpx;
}

.error-container {
  margin-top: 24rpx;
  padding: 16rpx;
  background-color: #fff2f0;
  border-radius: 8rpx;
  border: 1rpx solid #ffccc7;
}

.global-error {
  font-size: 26rpx;
  color: #ff6b6b;
  text-align: center;
  display: block;
}

.agreement-container {
  margin-top: 16rpx;
}

.agreement-label {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 8rpx;
}

.agreement-link {
  font-size: 26rpx;
  color: #a18cd1;
  margin-left: 4rpx;
}

.auth-popup-footer {
  padding: 20rpx 36rpx 36rpx;
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.auth-btn {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.auth-cancel {
  background-color: #f5f7fa;
  color: #666;
  border: 1rpx solid #eee;
}

.auth-cancel:active {
  background-color: #eee;
}

.auth-submit {
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(161, 140, 209, 0.3);
  position: relative;
  overflow: hidden;
}

.auth-submit::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
  transform: skewX(-25deg) translateX(-100%);
  transition: all 0.5s ease;
}

.auth-submit:active::after {
  transform: skewX(-25deg) translateX(100%);
}

.auth-submit[disabled] {
  background: linear-gradient(135deg, #d8d0e8, #fde9f6);
  box-shadow: none;
} 