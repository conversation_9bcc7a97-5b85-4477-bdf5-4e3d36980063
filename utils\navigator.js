/**
 * 页面导航管理器
 * 负责实现全局导航守卫功能
 */
import { isAuthorized, pageRequiresAuth } from './auth';

// 全局导航实例
let globalNavInstance = null;

/**
 * 导航守卫类
 */
class Navigator {
  constructor() {
    // 存储原始导航方法
    this.originalNavigateTo = wx.navigateTo;
    this.originalRedirectTo = wx.redirectTo;
    this.originalSwitchTab = wx.switchTab;
    this.originalReLaunch = wx.reLaunch;
    
    // 绑定方法
    this._intercept();
  }
  
  /**
   * 拦截并重写导航方法
   */
  _intercept() {
    // 拦截 navigateTo
    wx.navigateTo = (options) => {
      this._checkNavigation(options, this.originalNavigateTo);
    };
    
    // 拦截 redirectTo
    wx.redirectTo = (options) => {
      this._checkNavigation(options, this.originalRedirectTo);
    };
    
    // 拦截 switchTab
    wx.switchTab = (options) => {
      this._checkNavigation(options, this.originalSwitchTab);
    };
    
    // 拦截 reLaunch
    wx.reLaunch = (options) => {
      this._checkNavigation(options, this.originalReLaunch);
    };
  }
  
  /**
   * 检查导航权限
   * @param {Object} options - 导航选项
   * @param {Function} originalMethod - 原始导航方法
   */
  _checkNavigation(options, originalMethod) {
    const url = options.url;
    // 特殊处理switchTab，允许直接切换Tab
    if (originalMethod === this.originalSwitchTab) {
      originalMethod.call(wx, options);
      return;
    }
    
    // 检查该页面是否需要授权
    if (pageRequiresAuth(url)) {
      // 检查是否已授权
      if (!isAuthorized()) {
        console.log('需要授权，显示授权弹窗', url);
        
        // 显示授权弹窗
        // 这里使用全局事件总线触发事件
        const eventChannel = getApp().globalData.eventBus;
        if (eventChannel) {
          eventChannel.emit('showAuthPopup', { nextPath: url });
        } else {
          console.error('全局事件总线未初始化');
        }
        
        // 如果有失败回调，执行
        if (typeof options.fail === 'function') {
          options.fail({ 
            errMsg: 'navigateTo:fail auth required' 
          });
        }
        
        return;
      }
    }
    
    // 授权检查通过，执行原始导航方法
    originalMethod.call(wx, options);
  }
  
  /**
   * 恢复原始导航方法
   */
  restore() {
    wx.navigateTo = this.originalNavigateTo;
    wx.redirectTo = this.originalRedirectTo;
    wx.switchTab = this.originalSwitchTab;
    wx.reLaunch = this.originalReLaunch;
  }
}

/**
 * 获取全局导航实例
 * @returns {Navigator} 导航实例
 */
export const getNavigator = () => {
  if (!globalNavInstance) {
    globalNavInstance = new Navigator();
  }
  return globalNavInstance;
};

/**
 * 初始化导航守卫
 */
export const initNavigationGuard = () => {
  getNavigator();
};

export default {
  getNavigator,
  initNavigationGuard
}; 