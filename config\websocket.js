// config/websocket.js
// WebSocket 配置文件

import request from '../utils/request';
console.log(request.config.wsURL)
const config = {
  // 开发环境配置
  development: {
    // 练习模块流式处理 WebSocket 地址
    practiceStreamUrl: `${request.config.wsURL}/ws/practice/analysis`,

    // 连接超时时间（毫秒）
    connectTimeout: 10000,

    // 重连配置
    reconnect: {
      maxAttempts: 3,
      delay: 1000
    },

    // 心跳配置
    heartbeat: {
      interval: 30000,
      timeout: 5000
    }
  },

  // 生产环境配置
  production: {
    // 练习模块流式处理 WebSocket 地址
    practiceStreamUrl: `${request.config.wsURL}/practice/stream`,

    // 连接超时时间（毫秒）
    connectTimeout: 15000,

    // 重连配置
    reconnect: {
      maxAttempts: 5,
      delay: 2000
    },

    // 心跳配置
    heartbeat: {
      interval: 30000,
      timeout: 5000
    }
  }
};

// 获取当前环境
const getCurrentEnv = () => {
  // 可以根据实际需要修改环境判断逻辑
  // 例如通过域名、版本号等判断
  const accountInfo = wx.getAccountInfoSync();
  const envVersion = accountInfo.miniProgram.envVersion;

  // develop: 开发版
  // trial: 体验版
  // release: 正式版
  if (envVersion === 'release') {
    return 'production';
  } else {
    return 'development';
  }
};

// 获取当前环境的配置
const getConfig = () => {
  const env = getCurrentEnv();
  return config[env];
};

// 获取练习模块 WebSocket 地址
const getPracticeStreamUrl = () => {
  return getConfig().practiceStreamUrl;
};


// 获取连接配置
const getConnectionConfig = () => {
  const config = getConfig();
  return {
    connectTimeout: config.connectTimeout,
    reconnect: config.reconnect,
    heartbeat: config.heartbeat
  };
};

module.exports = {
  getConfig,
  getPracticeStreamUrl,
  getConnectionConfig,
  getCurrentEnv
};
