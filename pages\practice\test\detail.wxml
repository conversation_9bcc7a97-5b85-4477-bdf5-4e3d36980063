<!--pages/practice/detail.wxml-->
<view class="container">
  <!-- 聊天内容区域 -->
  <scroll-view 
    class="chat-container" 
    scroll-y="true" 
    scroll-top="{{scrollTop}}"
    scroll-with-animation="true"
    bindscrolltoupper="onScrollToUpper"
    bindscroll="onScroll"
  >
    <view class="chat-list">
      <!-- 系统提示消息 -->
      <!-- <view class="system-message" wx:if="{{showSystemMessage}}">
        <text class="system-text">练习已开始，请根据提示完成练习内容</text>
      </view> -->

      <!-- 消息列表 -->
      <block wx:for="{{messages}}" wx:key="index">
        <view class="message-item {{item.type === 'ai' ? 'ai-message' : 'user-message'}}">
          <!-- AI消息 -->
          <block wx:if="{{item.type === 'ai'}}">
            <view class="avatar-icon">
              <text class="iconfont icon-user"></text>
              <!-- <image src="/static/images/ai-avatar.png" mode="aspectFill"></image> -->
            </view>
            <view class="message-content">
              <view class="message-bubble">
                <text>{{item.content}}</text>
              </view>
              <text class="message-time">{{item.time}}</text>
              <!-- 评估结果展示 -->
              <!-- <view class="evaluation-result" wx:if="{{item.evaluation}}">
                <text class="score {{item.isCorrect ? 'correct' : 'incorrect'}}">{{item.isCorrect ? '回答正确' : '回答不正确'}} {{item.score ? '(' + item.score + '分)' : ''}}</text>
              </view> -->
            </view>
          </block>

          <!-- 用户消息 -->
          <block wx:if="{{item.type === 'user'}}">
            <view class="message-content">
              <view class="message-bubble">
                <text>{{item.content}}</text>
              </view>
              <text class="message-time">{{item.time}}</text>
            </view>
            <view class="avatar-icon">
              <image src="/static/images/user-avatar.png" mode="aspectFill"></image>
            </view>
          </block>
          
          <!-- 语音消息 -->
          <block wx:if="{{item.type === 'voice'}}">
            <view class="message-content">
              <view class="message-bubble voice-bubble" bindtap="playVoice" data-filepath="{{item.content}}">
                <view class="voice-icon">
                  <text class="iconfont icon-yuyin"></text>
                </view>
                <text>语音消息</text>
              </view>
              <text class="message-time">{{item.time}}</text>
            </view>
            <view class="avatar-icon">
              <image src="/static/images/user-avatar.png" mode="aspectFill"></image>
            </view>
          </block>
          
          <!-- 系统消息特殊样式 -->
          <block wx:if="{{item.type === 'system'}}">
            <view class="system-message-item">
              <text>{{item.content}}</text>
              <!-- <text class="message-time">{{item.time}}</text> -->
            </view>
          </block>
        </view>
      </block>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore}}">
        <text>正在加载更多...</text>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-indicator" wx:if="{{isLoading}}">
        <view class="loading-dots">
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部输入区域 -->
  <view class="input-container" style="bottom: {{keyboardHeight}}px">
    <!-- 语音/文字切换按钮 -->
    <view class="input-type-switch" bindtap="toggleInputType">
      <text class="iconfont {{isVoiceMode ? 'icon-paper-plane' : 'icon-yuyin'}}"></text>
    </view>

    <!-- 文本输入框 -->
    <block wx:if="{{!isVoiceMode}}">
      <view class="input-box">
        <textarea
          class="input-textarea"
          model:value="{{inputContent}}"
          adjust-position="{{false}}"
          show-confirm-bar="{{false}}"
          cursor-spacing="20"
          maxlength="-1"
          bindfocus="onInputFocus"
          bindblur="onInputBlur"
          bindconfirm="sendMessage"
          placeholder="请输入回答内容..."
          disable-default-padding="{{true}}"
        ></textarea>
        <!-- <view class="input-tools">
          <view class="send-button {{inputContent.trim() ? 'active' : ''}}" bindtap="sendMessage">
             send
          </view>
        </view> -->
      </view>
    </block>

    <!-- 语音输入按钮 -->
    <block wx:else>
      <view class="voice-input-box">
        <view 
          class="voice-button {{isRecording ? 'recording' : ''}}"
          bindtouchstart="startRecording"
          bindtouchend="stopRecording"
          bindtouchmove="onTouchMove"
          bindtouchcancel="cancelRecording"
        >
          <text>{{isRecording ? '松开发送' : '按住说话'}}</text>
        </view>
      </view>
    </block>
  </view>

  <!-- 录音提示蒙层 -->
  <view class="recording-mask {{isCancelling ? 'cancelling' : ''}}" wx:if="{{isRecording}}">
    <view class="recording-indicator">
      <view class="recording-icon {{isCancelling ? 'cancel-icon' : ''}}">
        <view class="recording-waves" wx:if="{{!isCancelling}}">
          <view class="wave" wx:for="{{3}}" wx:key="index" style="animation-delay: {{index * 0.2}}s"></view>
        </view>
        <view class="cancel-icon-inner" wx:if="{{isCancelling}}">
          <text class="iconfont icon-delete"></text>
        </view>
      </view>
      <text class="recording-text">{{isCancelling ? '松开手指，取消发送' : '录音中...'}}</text>
      <text class="cancel-tip" wx:if="{{!isCancelling}}">↑ 上滑取消发送</text>
      <view class="recording-time" wx:if="{{!isCancelling}}">
        <text>{{recordingTime}}</text>
      </view>
    </view>
  </view>
  
  <!-- 底部安全区域，适配全面屏手机 -->
  <view class="safe-area-bottom"></view>
</view>