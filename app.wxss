/**app.wxss**/
@import './static/css/iconfont/iconfont.wxss';

/* 全局样式从uni-app的App.vue转换而来 */
page {
  background-color: #f8f9fa;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  /* 添加安全区底部高度变量 */
  --safe-bottom: env(safe-area-inset-bottom);
  --safe-top: env(safe-area-inset-top);
}

/* 主色调 - 马卡龙渐变 */
/* 微信小程序不支持:root，改为页面级变量 */
page {
  --primary-gradient: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  --secondary-gradient: linear-gradient(135deg, #90dffe 0%, #38a3d1 100%);
  
  /* 基础颜色 */
  --primary-color: #a18cd1;
  --secondary-color: #fbc2eb;
  --accent-color: #38a3d1;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #ffffff;
  --background-color: #f8f9fa;
  --card-background: #ffffff;
}

/* 内容区域 */
.content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 120rpx; /* tabbar的高度 */
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 32rpx;
  padding: 32rpx;
}

/* 渐变背景卡片 */
.gradient-card {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx;
  margin-bottom: 32rpx;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  border-radius: 16rpx;
  padding: 32rpx 48rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  outline: none;
}

.card-footer {
  /* display: flex;
  justify-content: center; */
  padding: 24rpx 32rpx;
  background-color: #f9f9f9;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  font-size: 26rpx;
  overflow:hidden;
}
.card-btn{
  /* width:35%; */
  text-align: center;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 30rpx rgba(161, 140, 209, 0.4);
  border-radius: 16rpx;
  padding: 10rpx 20rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  outline: none;
  margin-left:20rpx;
  display: inline-block;
  float:right;
}

.btn-primary {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 30rpx rgba(161, 140, 209, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #90dffe 0%, #38a3d1 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 30rpx rgba(56, 163, 209, 0.4);
}

.btn-outline {
  background: transparent;
  border: 2rpx solid #a18cd1;
  color: #a18cd1;
}

/* 头像样式 */
.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.avatar-sm {
  width: 80rpx;
  height: 80rpx;
}

.avatar-lg {
  width: 160rpx;
  height: 160rpx;
}

/* 进度条样式 */
.progress-bar {
  height: 16rpx;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
  overflow: hidden;
  margin: 16rpx 0;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border-radius: 8rpx;
} 

/* 安全区相关 */
.page-container {
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
}

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: calc(150rpx + env(safe-area-inset-bottom)) !important;
}

/* 安全区域适配通用类 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}

.safe-area-inset-top {
  height: env(safe-area-inset-top);
  width: 100%;
}

/* 底部固定元素安全区适配 */
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: env(safe-area-inset-bottom);
}

button {
  background-color: initial;
  margin: 0;
  padding: 0;
  border-radius: 0;
  border: none;
  line-height: normal;
}

button::after {
  border: none;
} 


/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.popup-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  padding: 40rpx;
  text-align: center;
}

.popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.popup-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
}

.popup-icon .iconfont {
  font-size: 60rpx;
  color: #fff;
}

.popup-desc {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.popup-buttons {
  display: flex;
  justify-content: center;
}

.popup-btn {
  /* flex: 1; */
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  width:45%;
  margin: 0 20rpx;
}

.popup-btn.cancel {
  background-color: #f5f5f5;
  color: #999;
}

.popup-btn.confirm {
  background: linear-gradient(90deg, #a18cd1, #fbc2eb);
  color: #fff;
  font-weight: 500;
} 


.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 16rpx;
  padding: 30rpx 40rpx;
}

/* 加载状态指示器样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /* padding: 40rpx 0; */
  /* margin-bottom: 20rpx; */
}

.loading-spinner {
  width: 30rpx;
  height: 30rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #a18cd1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

.page-loading-mask .loading-spinner {
  width: 80rpx;
  /* height: 80rpx; */
  border: 8rpx solid rgba(255, 255, 255, 0.3);
  border-top: 8rpx solid #fff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


/* 没有更多数据提示 */
.no-more-data {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #999;
} 
.loading-text {
  font-size: 26rpx;
  color: #999;
}

.page-loading-mask .loading-text {
  color: white;
  font-size: 28rpx;
  margin-top: 20rpx;
}


/* 抽屉样式 */
.drawer-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.drawer {
  position: fixed;
  background: #fff;
  z-index: 1000;
  top: 0;
  bottom: 0;
  width: 600rpx;
  box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.drawer.drawer-show {
  transform: translateX(0);
}

.drawer-header {
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  background:linear-gradient(135deg, rgba(161, 140, 209, 0.1) 0%, rgba(251, 194, 235, 0.1) 100%)
}
.drawer-header-tab{
  padding:0px;
}

.drawer-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.drawer-header .iconfont {
  font-size: 28rpx;
  color: #666;
  background-color: rgba(0,0,0,0.05);
  border-radius: 50%;
  padding: 10rpx;
}

.drawer-body {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 确保scroll-view在drawer中正常工作 */
scroll-view.drawer-body {
  flex: 1;
  width: 100%;
  padding-bottom: calc(150rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.selector-item.selector-item-active{
  background-color: rgba(109, 74, 189, 0.1);
}
.selector-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 25rpx;
  transition: all 0.3s;
  margin: 24rpx;
  position: relative;
}

.selector-item-active {
  background-color: rgba(126, 87, 194, 0.05);
  border-left: 4rpx solid #7e57c2;
}

.selector-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.selector-icon .iconfont {
  font-size: 40rpx;
  color: #fff;
}

.selector-info {
  flex: 1;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.selector-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
}
.selector-info .current{
  font-size:26rpx;
}
.selector-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 4rpx;
}

.selector-check {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}

.selector-check .iconfont {
  color: #7e57c2;
  font-size: 32rpx;
}

.selector-item-active {
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.15) 0%, rgba(251, 194, 235, 0.15) 100%);
  border-left: 4rpx solid #7e57c2;
}

.selector-item-active .selector-title {
  color: #7e57c2;
  font-weight: 600;
} 
