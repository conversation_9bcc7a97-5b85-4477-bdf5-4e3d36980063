// pages/profile/achievements/achievements.js
import { getUserInfo } from '../../../api/user'
import { getAchievementList } from '../../../api/achievement'
import request from '../../../utils/request'
const authBehavior = require('../../../behaviors/auth-behavior')

Page({
  behaviors: [authBehavior],

  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: {
      avatar: '',
      realName: '',
      nickname: ''
    },
    
    // 勋章统计
    achievementStats: {
      totalCount: 0,
      obtainedCount: 0,
      completionRate: 0
    },
    
    // 勋章列表
    achievements: [],
    
    // 弹出层相关
    showDetailModal: false,
    selectedAchievement: null,
    
    // 加载状态
    isLoading: false,
    isRefreshing: false,
    
    // 图片基础URL
    imgURL: request.config.imgURL
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时重新加载数据
    this.loadData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.onRefresh().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 加载页面数据
   */
  async loadData() {
    this.setData({ isLoading: true });
    
    try {
      await Promise.all([
        this.fetchUserInfo(),
        this.fetchAchievements()
      ]);
    } catch (error) {
      console.error('加载数据失败：', error);
      wx.showToast({
        title: '加载数据失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 获取用户信息
   */
  async fetchUserInfo() {
    try {
      const openId = wx.getStorageSync('openId');
      const result = await getUserInfo(openId);
      
      if (result && result.user) {
        const avatar = result.user.avatar ? 
          request.config.imgURL + result.user.avatar : 
          '';
          
        this.setData({
          userInfo: {
            ...result.user,
            avatar: avatar,
            realName: result.user.realName || result.user.nickname || '未设置'
          }
        });
      }
    } catch (error) {
      console.error('获取用户信息失败：', error);
    }
  },

  /**
   * 获取成就勋章列表
   */
  async fetchAchievements() {
    try {
      const result = await getAchievementList();
      
      if (result) {
        const achievements = result.list || result.data || result;
        const obtainedCount = achievements.filter(item => item.isObtained).length;
        
        this.setData({
          achievements: achievements,
          achievementStats: {
            totalCount: achievements.length,
            obtainedCount: obtainedCount,
            completionRate: achievements.length > 0 ? 
              Math.round((obtainedCount / achievements.length) * 100) : 0
          }
        });
      }
    } catch (error) {
      console.error('获取成就勋章失败：', error);
      // 使用模拟数据
      this.loadMockData();
    }
  },

  /**
   * 加载模拟数据（用于开发测试）
   */
  loadMockData() {
    const mockAchievements = [
      {
        id: 1,
        name: '初学者',
        description: '完成第一次练习',
        icon: '/static/images/achievements/1.png',
        condition: '首次学习一门科目的进度达到100%',
        isObtained: true,
        obtainedTime: '2024-01-15'
      },
      {
        id: 2,
        name: '知识探索',
        description: '连续学习7天',
        icon: '/static/images/achievements/2.png',
        condition: '完成5门科目的进度达到100%',
        isObtained: true,
        obtainedTime: '2024-01-20'
      },
      {
        id: 3,
        name: '学霸模式',
        description: '通过10次考试',
        icon: '/static/images/achievements/3.png',
        condition: '连续学习超过10天',
        isObtained: false,
        obtainedTime: null
      },
      {
        id: 4,
        name: '学无止境',
        description: '获得满分成绩',
        icon: '/static/images/achievements/4.png',
        condition: '累计学习时长超过50小时',
        isObtained: false,
        obtainedTime: null
      },
      {
        id: 5,
        name: '碎片大师',
        description: '获得5张证书',
        icon: '/static/images/achievements/8.png',
        condition: '连续早上10点前学习，并且连续晚上20点之后学习-----0～12点    12～18点   18～24点 三个时间范围都有练习记录',
        isObtained: true,
        obtainedTime: '2024-02-01'
      },
      {
        id: 6,
        name: '全能力者',
        description: '月度学习时长超过100小时',
        icon: '/static/images/achievements/9.png',
        condition: '所有岗位都有练习记录',
        isObtained: false,
        obtainedTime: null
      },
      {
        id: 7,
        name: '金牌毕业生',
        description: '月度学习时长超过100小时',
        icon: '/static/images/achievements/10.png',
        condition: '所有岗位考试都通过----所有考试都是通过率100%',
        isObtained: false,
        obtainedTime: null
      },
      {
        id: 8,
        name: '全能力者',
        description: '月度学习时长超过100小时',
        icon: '/static/images/achievements/11.png',
        condition: '所有岗位都有练习记录',
        isObtained: false,
        obtainedTime: null
      },
      {
        id: 9,
        name: '全能力者',
        description: '月度学习时长超过100小时',
        icon: '/static/images/achievements/12.png',
        condition: '所有岗位都有练习记录',
        isObtained: false,
        obtainedTime: null
      },
    ];

    const obtainedCount = mockAchievements.filter(item => item.isObtained).length;
    
    this.setData({
      achievements: mockAchievements,
      achievementStats: {
        totalCount: mockAchievements.length,
        obtainedCount: obtainedCount,
        completionRate: Math.round((obtainedCount / mockAchievements.length) * 100)
      }
    });
  },

  /**
   * 点击勋章显示详情
   */
  onAchievementTap(e) {
    const { achievement } = e.currentTarget.dataset;
    this.setData({
      selectedAchievement: achievement,
      showDetailModal: true
    });
  },

  /**
   * 关闭详情弹出层
   */
  closeDetailModal() {
    this.setData({
      showDetailModal: false,
      selectedAchievement: null
    });
  },

  /**
   * 阻止弹出层滚动穿透
   */
  preventTouchMove() {
    return false;
  },

  /**
   * 刷新数据
   */
  async onRefresh() {
    this.setData({ isRefreshing: true });
    await this.loadData();
    this.setData({ isRefreshing: false });
  }
});
