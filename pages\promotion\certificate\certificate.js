// 导入证书相关API
import { getCertificateGenerate } from '../../../api/certificate';
import request from '../../../utils/request'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentCert: null,
    certId: null,
    imgURL:request.config.imgURL,
    certificateUrl:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    try {
      // 如果有传入证书ID参数，则获取证书详情
      if (options && options.certId) {
        this.setData({ certId: options.certId });
        this.fetchCertificateDetail(options.certId);
      }
      
      // 如果有传入证书数据，则直接使用
      if (options && options.certData) {
        try {
          const certData = JSON.parse(options.certData);
          this.setData({ currentCert: certData });
        } catch (e) {
          console.error('解析证书数据错误:', e);
          wx.showToast({
            title: '解析证书数据失败',
            icon: 'none'
          });
        }
      }
    } catch (error) {
      console.error('加载证书详情失败:', error);
      wx.showToast({
        title: '获取证书详情失败',
        icon: 'none'
      });
    }
  },

  /**
   * 获取证书详情数据
   */
  fetchCertificateDetail(certId) {
    wx.showLoading({
      title: '加载中...',
    });

    // 调用证书API获取详情
    getCertificateGenerate({certificateId:this.data.certId})
      .then(data => {
        wx.hideLoading();
        // 格式化证书数据
        this.setData({
        certificateUrl:this.data.imgURL+data.certificateUrl,
          currentCert: data
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取证书详情失败:', err);
        wx.showToast({
          title: err.message || '获取证书详情失败',
          icon: 'none'
        });
        
        // 开发环境下使用模拟数据
        if (err.code === -1 || err.code === 404) {
          console.log('使用模拟数据');
          const mockData = {
            id: 4,
            certificateNo: "C2505821209",
            certificateName: "餐饮服务基础技能证书",
            kbId: "9d0728d8-bd4b-4783-8e82-64598aa39b4b",
            obtainTime: "2025-05-08 15:14:42",
            positionName: "服务员",
            positionBelong: null,
            positionLevel: "P1",
            employeeName: "张三",
            employeeId: 21,
            certificateFileUrl: null,
            examRecordId: 7,
            validUntil: "2026-05-08 15:14:42",
            enterpriseId: "8ecca795-c9a0-4cd4-9b82-bf4d190d3f32",
            applicationId: 11,
            openId: "xxxxx",
            positionNameInfo: {
              id: 4,
              name: "服务员",
              code: "T1746610354986_1746610364586"
            },
            positionLevelInfo: {
              id: 18,
              name: "P1",
              code: "P1"
            }
          };
          
          const formattedData = this.formatCertData(mockData);
          this.setData({
            currentCert: formattedData
          });
        }
      });
  },


  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果有证书ID但没有证书数据，重新获取
    if (this.data.certId && !this.data.currentCert) {
      this.fetchCertificateDetail(this.data.certId);
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.certId) {
      this.fetchCertificateDetail(this.data.certId);
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const cert = this.data.currentCert;
    return {
      title: `${cert ? cert.certificateName : '我的证书'} - 餐烤餐考认证`,
      path: `/pages/promotion/certificate/certificate?certId=${this.data.certId}`
    };
  },

  /**
   * 分享证书事件处理
   */
  shareCert: function(e) {
    const cert = e.detail.cert;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  }
});
