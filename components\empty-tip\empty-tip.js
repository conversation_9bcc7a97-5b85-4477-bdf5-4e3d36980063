Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 提示文本
    text: {
      type: String,
      value: '暂无数据'
    },
    // 图标类名
    icon: {
      type: String,
      value: 'icon-zanwushuju'
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    showImg:{
      type:Boolean,
      value:true,
    }
  },
   /**
   * 组件选项
   */
   options: {
    styleIsolation: 'apply-shared'  // 使用apply-shared让组件可以使用app.wxss中的样式
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
}) 