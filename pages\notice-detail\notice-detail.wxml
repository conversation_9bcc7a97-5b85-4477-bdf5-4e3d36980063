<!--pages/notice-detail/notice-detail.wxml-->
<view class="container">

  <!-- 公告详情内容 -->
  <view class="notice-detail-container">
    <view class="notice-detail-content" wx:if="{{noticeDetail}}">
      <!-- 公告标题 -->
      <view class="notice-header">
        <view class="notice-title-wrapper">
          <text class="notice-badge" wx:if="{{noticeDetail.isImportant}}">重要</text>
          <text class="notice-title">{{noticeDetail.title}}</text>
        </view>
        <view class="notice-meta">
          <text class="notice-time">{{noticeDetail.createTime}}</text>
        </view>
      </view>

      <!-- 公告内容 -->
      <view class="notice-content">
        <!-- html格式数据 -->
        <rich-text nodes="{{noticeDetail.content}}" />
      </view>

      <!-- 附加信息 -->
      <view class="notice-footer" wx:if="{{noticeDetail.attachments || noticeDetail.links}}">
        <!-- 附件 -->
        <view class="attachments" wx:if="{{noticeDetail.attachments && noticeDetail.attachments.length > 0}}">
          <text class="section-title">附件</text>
          <view class="attachment-item" wx:for="{{noticeDetail.attachments}}" wx:key="id">
            <text class="iconfont icon-attachment"></text>
            <text class="attachment-name">{{item.name}}</text>
          </view>
        </view>

        <!-- 相关链接 -->
        <view class="links" wx:if="{{noticeDetail.links && noticeDetail.links.length > 0}}">
          <text class="section-title">相关链接</text>
          <view class="link-item" wx:for="{{noticeDetail.links}}" wx:key="id">
            <text class="iconfont icon-link"></text>
            <text class="link-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <empty-tip text="公告不存在或已被删除" />
    </view>
  </view>
</view>