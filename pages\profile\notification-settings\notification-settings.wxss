/* pages/profile/notification-settings.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.header {
  background: #fff;
  padding: 24rpx 32rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.settings-container {
  padding: 32rpx;
}

.settings-intro {
  background: #fff;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  font-size: 28rpx;
  color: #666;
}

.settings-list {
  background: #fff;
  border-radius: 24rpx;
  padding: 16rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.settings-item:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  color: #fff;
}

.item-icon.system {
  background: var( --primary-gradient);
}

.item-icon.activity {
  background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
}

.item-icon.exam {
  background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
}

.item-icon.practice {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.item-icon .iconfont {
  font-size: 40rpx;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.item-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.footer-notes {
  background: #fff;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.footer-btn {
  padding: 32rpx;
}

.save-btn {
  background: var( --primary-gradient);
  color: #fff;
  border-radius: 44rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 30rpx rgba(161, 140, 209, 0.4);
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 6rpx 20rpx rgba(161, 140, 209, 0.3);
} 