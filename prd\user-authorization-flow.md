# 授权流程功能需求文档

## 1. 功能概述

### 1.1 需求背景
当前小程序中，用户可以直接访问各级页面，但缺乏必要的用户授权机制，导致无法收集用户基本信息，影响后续的个性化服务和数据分析。

### 1.2 功能描述
实现一个授权流程控制机制，当未授权用户尝试访问二级页面时，自动弹出授权窗口，要求用户提供昵称和手机号进行授权；对于已完成授权的用户，则直接跳转至目标页面。

### 1.3 功能目标
- 提升用户身份识别率，完善用户画像
- 增强用户粘性和归属感
- 为后续个性化功能和精准营销提供数据支持
- 规范小程序内的页面访问流程

## 2. 详细需求

### 2.1 授权判断
- 在用户点击任何通往二级页面的链接或按钮时触发授权判断
- 判断本地是否存在用户授权信息（token 及用户基本信息）
- 若已授权，直接跳转至目标页面
- 若未授权，显示授权弹窗

### 2.2 授权弹窗
- 弹窗需包含以下元素：
  - 标题：请先完成授权
  - 输入框：昵称获取微信昵称
  - 输入框：手机号微信授权获取手机号
- 弹窗设计应简洁明了，突出主要输入项
- 弹窗显示时，背景页面应当半透明，不可操作

### 2.3 授权流程
1. 用户点击进入二级页面的链接/按钮
2. 判断用户授权状态
3. 未授权用户显示授权弹窗
4. 用户填写昵称、手机号信息
5. 点击授权按钮提交信息
6. 服务器验证并保存用户信息
7. 保存授权状态到本地存储
8. 跳转至用户原来要访问的页面

### 2.4 授权状态管理
- 授权成功后，在本地存储中保存授权标识和用户基本信息
- 使用全局状态管理用户的授权状态
- 提供检查授权状态的全局方法，便于各页面调用

## 3. 技术实现

### 3.1 新增接口
- 用户授权提交接口
  ```
  POST /api/user/authorize
  请求参数：
  {
    "nickname": "用户昵称",
    "phone": "手机号码",
    "code": "验证码"（可选）
  }
  响应数据：
  {
    "code": 200,
    "data": {
      "token": "授权令牌",
      "userInfo": {
        "id": "用户ID",
        "nickname": "用户昵称",
        "phone": "手机号码",
        "avatar": "头像地址"
      }
    },
    "message": "授权成功"
  }
  ```

### 3.2 全局导航守卫
在全局导航守卫中实现授权检查逻辑：
```javascript
// 示例代码，实际实现可能需要调整
const checkAuthorization = (to, from, next) => {
  const hasAuthorized = uni.getStorageSync('token') && uni.getStorageSync('userInfo');
  
  if (!hasAuthorized && to.meta.requiresAuth) {
    // 显示授权弹窗
    uni.$emit('showAuthPopup', { nextPath: to.path });
    return false;
  }
  return true;
};
```

### 3.3 授权弹窗组件
创建一个全局授权弹窗组件，可在任何页面调用

## 4. 界面设计

### 4.1 授权弹窗设计要点
- 遵循小程序整体设计风格
- 简洁明了，突出主要输入项
- 确保表单验证清晰可见
- 弹窗大小适中，不占据过多屏幕空间
- 统一使用项目风格

## 5. 注意事项

### 5.1 用户体验考虑
- 授权过程应尽可能简单，减少用户输入负担
- 清晰说明收集信息的目的和用途
- 授权失败时提供友好的错误提示和重试选项
- 考虑网络异常情况下的用户体验

### 5.2 技术实现注意点
- 确保授权状态的一致性，避免重复授权请求
- 授权信息应进行安全存储
- 考虑授权信息过期的处理机制
- 遵循数据最小化原则，只收集必要的用户信息

## 6. 测试要点
- 测试未授权用户访问二级页面的弹窗触发
- 测试已授权用户访问二级页面的直接跳转
- 测试各种输入场景下的表单验证
- 测试网络异常情况下的错误处理
- 测试用户取消授权后的行为
- 测试授权过程中返回操作的处理

## 7. 上线计划
- 开发周期：3人天
- 测试周期：1人天
- 预计上线时间：下一版本迭代 