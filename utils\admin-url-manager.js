// utils/admin-url-manager.js
import request from './request'
import { getAdminAccessUrl } from '../api/user'

/**
 * 管理员访问URL管理器
 * 用于统一管理和更新adminAccessUrl
 */
class AdminUrlManager {
  constructor() {
    this.isInitialized = false;
    this.initPromise = null;
  }

  /**
   * 初始化adminAccessUrl
   * 在应用启动时调用，获取最新的adminAccessUrl
   * @returns {Promise} 初始化结果
   */
  async initialize() {
    if (this.isInitialized) {
      return Promise.resolve();
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._doInitialize();
    return this.initPromise;
  }

  async _doInitialize() {
    try {
      console.log('开始初始化AdminAccessUrl...');
      
      // 尝试获取最新的adminAccessUrl
      const result = await getAdminAccessUrl();
      
      if (result && result.adminAccessUrl) {
        console.log('AdminAccessUrl初始化成功:', result.adminAccessUrl);
        this.isInitialized = true;
        return result.adminAccessUrl;
      } else {
        console.warn('未获取到有效的AdminAccessUrl，使用默认配置');
        this.isInitialized = true;
        return null;
      }
    } catch (error) {
      console.error('初始化AdminAccessUrl失败:', error);
      // 即使失败也标记为已初始化，避免重复尝试
      this.isInitialized = true;
      throw error;
    } finally {
      this.initPromise = null;
    }
  }

  /**
   * 手动设置adminAccessUrl
   * @param {string} url - 新的adminAccessUrl
   */
  setAdminAccessUrl(url) {
    request.setAdminAccessUrl(url);
    this.isInitialized = true;
  }

  /**
   * 获取当前有效的baseURL
   * @returns {string} 当前有效的baseURL
   */
  getCurrentBaseURL() {
    return request.getEffectiveBaseURL();
  }

  /**
   * 清除adminAccessUrl
   */
  clearAdminAccessUrl() {
    request.clearAdminAccessUrl();
    this.isInitialized = false;
  }

  /**
   * 刷新adminAccessUrl
   * 重新从服务器获取最新的adminAccessUrl
   * @returns {Promise} 刷新结果
   */
  async refresh() {
    this.isInitialized = false;
    this.initPromise = null;
    return this.initialize();
  }

  /**
   * 检查是否已初始化
   * @returns {boolean} 是否已初始化
   */
  isReady() {
    return this.isInitialized;
  }
}

// 创建单例实例
const adminUrlManager = new AdminUrlManager();

export default adminUrlManager;
