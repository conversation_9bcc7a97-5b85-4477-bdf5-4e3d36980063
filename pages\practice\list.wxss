/* pages/practice/list.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 筛选状态栏 */
.filter-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.filter-button {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.1) 0%, rgba(251, 194, 235, 0.1) 100%);
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #7e57c2;
  border: 2rpx dashed rgba(126, 87, 194, 0.3);
  transition: all 0.3s;
  line-height: normal;
  height: 72rpx;
  width: auto;
  min-width: 150rpx;
  justify-content: center;
}

.filter-button::after {
  border: none;
}

.filter-button text {
  margin: 0 8rpx;
}

.filter-button .iconfont {
  font-size: 32rpx;
}

.current-filter-status {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.current-filter-badge {
  background: var( --primary-gradient);
  color: #fff;
  font-size: 22rpx;
  font-weight: normal;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  margin-left: 16rpx;
}

/* 引导提示 */
.guide-tip {
  margin: 32rpx;
  background: rgba(126, 87, 194, 0.08);
  border: 2rpx dashed rgba(126, 87, 194, 0.3);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.guide-tip-icon {
  width: 64rpx;
  height: 64rpx;
  background: var( --primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.guide-tip-icon .iconfont {
  color: #fff;
  font-size: 32rpx;
}

.guide-tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

.guide-close {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  color: #999;
  font-size: 24rpx;
}

/* 餐考分类列表 */
.categories-container {
  padding: 0 32rpx;
}

.content-section-title {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
  margin-bottom: 24rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.content-section-title .iconfont {
  font-size: 32rpx;
  color: #a18cd1;
  margin-right: 16rpx;
}

.course-type-divider {
  display: flex;
  align-items: center;
  color: #ccc;
  margin:20rpx 0px;
}
.course-type-divider::after{
  content: '';
  display: block;
  width: 100%;
  height: 1rpx;
  background-color: rgba(0, 0, 0, 0.1);

}
.course-type-divider::before{
  content: '';
  display: block;
  width: 100%;
  height: 1rpx;
  background-color: rgba(0, 0, 0, 0.1);
}

.course-type-divider-text {
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: -webkit-fill-available;
}

.course-type-divider-text .iconfont {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.current-position-cards {
  margin-bottom: 16rpx;
}

.practice-courses-container {
  margin-bottom: 16rpx;
}
.practice-courses-container .card-header{
  /* background:linear-gradient(35deg, rgba(161, 140, 209, 0.15) 0%, rgba(155, 90, 184, 0.5) 100%); */
  background:linear-gradient(135deg, rgba(161, 140, 209, 0.15) 0%, rgba(161, 140, 209, 0.8) 100%);


}
.practice-courses-container .practice-badge {
  background:linear-gradient(-35deg, rgba(161, 140, 209, 0.15) 0%, #a18cd1 100%);
}


.position-drawer {
  left: 0;
}

.drawer-header .iconfont{
  margin-right:20rpx;
}
.tab-item {
  flex: 1;
  text-align: center;
  border-bottom: 2rpx solid transparent; 
  padding:32rpx 0;
}

.tab-item.active {
  border-bottom: 2rpx solid #7e57c2;
}





.level-drawer {
  right: 0;
  left: auto;
  transform: translateX(100%);
}
