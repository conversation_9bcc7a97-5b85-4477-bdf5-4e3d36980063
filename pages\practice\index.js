const app = getApp()
const authBehavior = require('../../behaviors/auth-behavior')

Page({
  behaviors: [authBehavior],

  data: {
    // 练习分类
    categories: [],
    // 当前选中分类
    currentCategory: 'all',
    // 难度过滤
    difficulties: [
      { value: 'all', label: '全部难度' },
      { value: 'easy', label: '初级' },
      { value: 'medium', label: '中级' },
      { value: 'hard', label: '高级' }
    ],
    currentDifficulty: 'all',
    // 完成状态过滤
    completionStatuses: [
      { value: 'all', label: '全部' },
      { value: 'completed', label: '已完成' },
      { value: 'incomplete', label: '未完成' }
    ],
    currentCompletionStatus: 'all',
    // 搜索关键词
    searchKeyword: '',
    // 练习列表
    practiceList: [],
    // 加载状态
    loading: true,
    // 分页相关
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    // 是否正在进行下拉刷新
    isRefreshing: false,
    // 是否加载更多数据
    loadingMore: false,
    // 是否还有更多数据
    hasMoreData: true
  },

  onLoad: function() {
    this.loadCategories()
    this.loadPracticeList()
  },

  onShow: function() {
    // 每次页面显示时，重新检查授权状态
    this.checkAuthStatus()
  },

  onPullDownRefresh: function() {
    this.setData({
      isRefreshing: true,
      currentPage: 1,
      hasMoreData: true
    }, () => {
      this.loadPracticeList(() => {
        this.setData({ isRefreshing: false })
        wx.stopPullDownRefresh()
      })
    })
  },

  onReachBottom: function() {
    if (this.data.hasMoreData && !this.data.loadingMore) {
      this.loadMorePractices()
    }
  },

  /**
   * 加载分类数据
   */
  loadCategories: function() {
    wx.request({
      url: `${app.globalData.baseUrl}/api/practice/categories`,
      success: (res) => {
        if (res.data && res.data.code === 0) {
          // 添加"全部"选项
          const allCategories = [{ id: 'all', name: '全部' }].concat(res.data.data)
          this.setData({ categories: allCategories })
        }
      }
    })
  },

  /**
   * 加载练习列表
   * @param {Function} callback 回调函数
   */
  loadPracticeList: function(callback) {
    this.setData({ loading: true })

    // 组装请求参数
    const params = {
      page: this.data.currentPage,
      limit: this.data.pageSize
    }

    // 添加分类过滤
    if (this.data.currentCategory !== 'all') {
      params.categoryId = this.data.currentCategory
    }

    // 添加难度过滤
    if (this.data.currentDifficulty !== 'all') {
      params.difficulty = this.data.currentDifficulty
    }

    // 添加完成状态过滤
    if (this.data.currentCompletionStatus !== 'all') {
      params.completed = this.data.currentCompletionStatus === 'completed'
    }

    // 添加搜索关键词
    if (this.data.searchKeyword.trim()) {
      params.keyword = this.data.searchKeyword.trim()
    }

    // 将对象转换为查询字符串
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&')

    wx.request({
      url: `${app.globalData.baseUrl}/api/practice/list?${queryString}`,
      success: (res) => {
        if (res.data && res.data.code === 0) {
          // 如果是第一页，则替换列表；否则追加到列表末尾
          const practiceList = this.data.currentPage === 1 
            ? res.data.data.items 
            : [...this.data.practiceList, ...res.data.data.items]
            
          this.setData({
            practiceList,
            totalItems: res.data.data.total,
            hasMoreData: practiceList.length < res.data.data.total,
            loading: false
          })
        } else {
          this.setData({ loading: false })
          wx.showToast({
            title: '获取练习列表失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        this.setData({ loading: false })
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      },
      complete: () => {
        if (typeof callback === 'function') {
          callback()
        }
      }
    })
  },

  /**
   * 加载更多练习
   */
  loadMorePractices: function() {
    this.setData({
      loadingMore: true,
      currentPage: this.data.currentPage + 1
    }, () => {
      this.loadPracticeList(() => {
        this.setData({ loadingMore: false })
      })
    })
  },

  /**
   * 切换分类
   */
  changeCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id
    if (categoryId !== this.data.currentCategory) {
      this.setData({
        currentCategory: categoryId,
        currentPage: 1,
        hasMoreData: true
      }, () => {
        this.loadPracticeList()
      })
    }
  },

  /**
   * 切换难度
   */
  changeDifficulty: function(e) {
    const difficulty = e.currentTarget.dataset.value
    if (difficulty !== this.data.currentDifficulty) {
      this.setData({
        currentDifficulty: difficulty,
        currentPage: 1,
        hasMoreData: true
      }, () => {
        this.loadPracticeList()
      })
    }
  },

  /**
   * 切换完成状态
   */
  changeCompletionStatus: function(e) {
    const status = e.currentTarget.dataset.value
    if (status !== this.data.currentCompletionStatus) {
      this.setData({
        currentCompletionStatus: status,
        currentPage: 1,
        hasMoreData: true
      }, () => {
        this.loadPracticeList()
      })
    }
  },

  /**
   * 搜索输入变化
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  /**
   * 执行搜索
   */
  doSearch: function() {
    this.setData({
      currentPage: 1,
      hasMoreData: true
    }, () => {
      this.loadPracticeList()
    })
  },

  /**
   * 清除搜索
   */
  clearSearch: function() {
    if (this.data.searchKeyword) {
      this.setData({
        searchKeyword: '',
        currentPage: 1,
        hasMoreData: true
      }, () => {
        this.loadPracticeList()
      })
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    return {
      title: '餐烤餐考-专业练习平台',
      path: '/pages/practice/index'
    }
  }
}) 