// pages/profile/exam-records.js
import {  getUserExamStats, getPositionList } from '../../../api/user'
import { getExamRecordList } from '../../../api/promotion'
const authBehavior = require('../../../behaviors/auth-behavior');

Page({
  behaviors: [authBehavior],

  /**
   * 页面的初始数据
   */
  data: {
    tabs: [{
      name: '全部',
      value: 0,
      key: 'all'
    },{
      name: '已通过',
      value: 1,
      key: 'passed'
    },{
      name: '未通过',
      value: 2,
      key: 'failed'
    }],
    currentTab: {
      value: 0,
      key: 'all'
    },
    isRefreshing: false,
    tabSwitching: false,
    examRecords: [],
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    total: 0,
    examStats: {
      totalExams: 0,
      passedExams: 0,
      passRate: 0,
      passRateFormatted: '0%',
      currentMonthExams: 0
    },
    statsArray: [
      { label: '总考试次数', value: '0' },
      { label: '通过率', value: '0%' },
      { label: '本月考试次数', value: '0' }
    ],
    scrollViewHeight: 0,

    // 岗位抽屉相关
    positions: [],
    positionDrawerVisible: false,
    pageScroll: true,
    currentPosition: '',
    positionValue: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadData();
    setTimeout(() => {
      this.calculateScrollViewHeight();
    }, 1000);
    wx.onWindowResize(this.handleWindowResize);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // this.calculateScrollViewHeight();
  },

  handleWindowResize(size) {
    this.calculateScrollViewHeight();
  },

  calculateScrollViewHeight() {
    const that = this;
    
    const systemInfo = wx.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;
    
    const query = wx.createSelectorQuery().in(this);
    
    query.select('#examStats').boundingClientRect();
    query.select('#tabContainer').boundingClientRect();
    
    query.exec(function(res) {
      if (res && res.length >= 2) {
        const statsHeight = res[0] ? res[0].height : 0;
        const tabContainerHeight = res[1] ? res[1].height : 0;
        
        const otherElementsHeight = statsHeight + tabContainerHeight;
        
        const scrollViewHeight = windowHeight - otherElementsHeight - 20;
        
        that.setData({
          scrollViewHeight: scrollViewHeight > 0 ? scrollViewHeight : 300
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
   
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    wx.offWindowResize(this.handleWindowResize);
    
    // 清理切换timer
    if (this.switchTimer) {
      clearTimeout(this.switchTimer);
      this.switchTimer = null;
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.onRefresh().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  async loadData() {
    try {
      await Promise.all([
        this.getPositions(),
        this.useDefaultPosition(),
        this.fetchExamStats(),
        this.loadRecords()
      ]);
    } catch (error) {
      console.error('加载数据失败：', error);
      wx.showToast({
        title: '加载数据失败，请重试',
        icon: 'none'
      });
    }
  },

  // 加载考试统计数据
  async fetchExamStats() {
    try {
      const result = await getUserExamStats();
      if (result) {
        this.setData({
          examStats: result,
          statsArray: [
            { label: '总考试次数', value: result.totalExams.toString() },
            { label: '通过率', value: result.passRateFormatted },
            { label: '本月考试次数', value: result.currentMonthExams.toString() }
          ]
        });
      }
    } catch (error) {
      console.error('获取考试统计失败：', error);
    }
  },

  // 加载考试记录
  async loadRecords(refresh = false) {
    if (this.data.loading) return;
    
    // 如果没有更多数据且不是刷新操作，则不继续加载
    if (!refresh && !this.data.hasMore) return;
    
    this.setData({
      loading: true
    });
    
    try {
      // 如果是刷新，则重置页码
      const page = refresh ? 1 : this.data.currentPage;
      
      const params = {
        page: page,
        pageSize: this.data.pageSize,
        category: this.data.currentTab.key,
        positionId: this.data.positionValue,
      };
      
      const result = await getExamRecordList(params);
      if (result) {
        const newRecords = this.processExamRecords(result.records || result.data || result);
        
        if (refresh || page === 1) {
          // 刷新或首次加载，替换数据
          this.setData({
            examRecords: newRecords,
            currentPage: 2,
            total: result.total || 0,
            hasMore: newRecords.length >= this.data.pageSize
          });
        } else {
          // 加载更多，追加数据
          this.setData({
            examRecords: [...this.data.examRecords, ...newRecords],
            currentPage: page + 1,
            hasMore: newRecords.length >= this.data.pageSize
          });
        }
      }
    } catch (error) {
      console.error('获取考试记录失败：', error);
      wx.showToast({
        title: '获取记录失败，请重试',
        icon: 'none'
      });
      this.setData({ hasMore: false });
    } finally {
      // 清除切换timer和状态
      if (this.switchTimer) {
        clearTimeout(this.switchTimer);
        this.switchTimer = null;
      }
      
      this.setData({
        loading: false,
        isRefreshing: false,
        loadingMore: false,
        tabSwitching: false
      });
    }
  },

  processExamRecords(records) {
    return records.map(item => {
      let confirmStatus = parseInt(item.confirmStatus);
      let confirmStatusText = '';
      if (confirmStatus === 1) {
        confirmStatusText = '待审核';
      } else if (confirmStatus === 2) {
        confirmStatusText = '已通过';
      } else if (confirmStatus === 3) {
        confirmStatusText = '未通过';
      }

      return {
        ...item,
        confirmStatusText
      };
    });
  },

  switchTab(e) {
    const { value } = e.detail;
    const newTab = this.data.tabs.find(tab => tab.value === value);
    
    // 如果是相同tab，不做任何操作
    if (this.data.currentTab.value === value) return;
    
    this.setData({
      currentTab: {
        value: value,
        key: newTab.key
      },
      currentPage: 1,
      hasMore: true
    });
    
    // 延迟显示切换状态，避免快速切换时的闪烁
    const switchTimer = setTimeout(() => {
      this.setData({
        tabSwitching: true
      });
    }, 200); // 200ms后如果还在加载，则显示切换提示
    
    // 保存timer以便清除
    this.switchTimer = switchTimer;
    
    // 切换tab时重新加载数据
    this.loadRecords(true);
  },
  
  getStatusClass(status) {
    return status === '已通过' ? 'status-pass' : 'status-fail';
  },
  
  getScoreClass(score) {
    if (score >= 80) return 'score-high';
    if (score >= 60) return 'score-medium';
    return 'score-low';
  },
  
  loadMore() {
    if (!this.data.loading && this.data.hasMore) {
      this.loadRecords(false);
    }
  },
  
  async onRefresh() {
    // 清除可能存在的切换timer
    if (this.switchTimer) {
      clearTimeout(this.switchTimer);
      this.switchTimer = null;
    }
    
    this.setData({
      isRefreshing: true,
      tabSwitching: false
    });
    await this.loadRecords(true);
  },
  // 考试记录
  goExamRecord(e) {
    const { record } = e.currentTarget.dataset;
     // 使用授权导航
    wx.navigateTo({
      url: '/pages/promotion/paper/paper?examId='+record.id,
    });
  },  
  // 报告记录
  goReportRecord(e) {
    const { record } = e.currentTarget.dataset;
     // 使用授权导航
    wx.navigateTo({
      url: `/pages/promotion/report?examId=${record.id}&from=examRecords`
    });
  },

  // 获取岗位列表
  getPositions() {
    return getPositionList().then(res => {
      this.setData({
        positions: res.rows
      });
    });
  },

  // 使用用户默认岗位
  useDefaultPosition() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.positionName && userInfo.positionId) {
      this.setData({
        currentPosition: userInfo.positionName,
        positionValue: userInfo.positionId,
      });
    }
  },

  // 显示岗位选择抽屉
  showPositionDrawer() {
    this.setData({
      positionDrawerVisible: true,
      pageScroll: false // 禁用页面滚动
    });

    // 禁用页面滚动
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });

    // 设置页面样式，禁止滚动
    wx.setPageStyle({
      style: {
        overflow: 'hidden'
      }
    });
  },

  // 关闭岗位选择抽屉
  closePositionDrawer() {
    this.setData({
      positionDrawerVisible: false,
      pageScroll: true // 恢复页面滚动
    });

    // 恢复页面滚动
    wx.setPageStyle({
      style: {
        overflow: 'auto'
      }
    });
  },

  // 选择岗位
  async selectPosition(e) {
    const position = e.currentTarget.dataset.position;
    this.setData({
      currentPosition: position.name,
      positionValue: position.id
    });
    this.closePositionDrawer();

    // 重新获取统计数据和记录
    await Promise.all([
      this.fetchExamStats(),
      this.loadRecords(true)
    ]);
  },

  // 防止抽屉中的滑动事件冒泡
  catchTouchMove() {
    return false;
  }

})