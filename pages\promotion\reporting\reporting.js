// pages/promotion/reporting/reporting.js
import { getExamReport } from '../../../api/promotion'
const authBehavior = require('../../../behaviors/auth-behavior')

// 页面配置，设置自定义导航栏
const pageConfig = {
  navigationBarTitleText: '考试报告',
  navigationStyle: 'custom',
  // 启用自定义返回处理
  disableScroll: false,
  usingComponents: {
    'custom-nav-bar': '/components/custom-nav-bar/custom-nav-bar'
  }
}

Page({
  behaviors: [authBehavior],

  /**
   * 页面的初始数据
   */
  data: {
    examId: '',
    reportData: null, // 为null时表示报告未生成，按钮禁用
    // isLoading: false,
    title:'考试报告',
    examInfo:null, // 考试信息
    isBackTriggered: false, // 标记是否已经触发了返回操作
    isEarlySubmission:true, // 是否提前交卷
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      examId: options.examId||this.data.examInfo.examId,
      isEarlySubmission:options.isEarlySubmission=='true'?true:false,
      title:'考试报告',
    });
    this.loadReportData();

    // 启用页面返回提示，防止用户直接返回上一页
    if (wx.enableAlertBeforeUnload) {
      wx.enableAlertBeforeUnload({
        message: '返回将离开考试报告页面',
        success: (res) => {
          console.log('启用返回提示成功', res);
        },
        fail: (err) => {
          console.error('启用返回提示失败', err);
        }
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 页面初次渲染完成
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 页面隐藏
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 如果不是通过我们的返回按钮触发的，则视为物理返回键触发
    if (!this.data.isBackTriggered) {
      console.log('检测到物理返回键退出，重定向到考试列表');
      // 标记为已触发返回
      this.setData({
        isBackTriggered: true
      });
      
      // 通过全局数据或存储，告诉下一个页面需要跳转
      wx.setStorageSync('needRedirectToPromotion', true);
      
      // 使用延迟，确保数据已保存
      if(this.data.from !== 'examRecords'){
        setTimeout(() => {
          // 通过reLaunch方式跳转到考试列表页
          wx.reLaunch({
            url: '/pages/promotion/list'
          });
        }, 50);
      }
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  async loadReportData() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });   
    try {
      // 显示加载提示，表明正在获取报告
      
      // 调用API获取考试报告
      const result = await getExamReport({
        exam_id: this.data.examId,
        isEarlySubmission: this.data.isEarlySubmission
      });

      // 如果成功获取报告数据，更新reportData状态，这将自动启用按钮
      // examTime 数据返回格式是yyyy-mm-dd hh:mm:ss 转换为yyyy-mm-dd hh:mm
            const dateTimeParts = result.examTime.split(' ');
            const datePart = dateTimeParts[0];
            const timePart = dateTimeParts[1].substring(0, 5); // Get only HH:MM part
            const examTime = `${datePart} ${timePart}`;



      if (result) {
        this.setData({
          reportData: {
            ...result,
            examTime:examTime
          },
        });
        wx.hideLoading();
        
      }
    } catch (error) {
      console.error('获取考试报告失败：', error);
      
      // 显示错误提示
      wx.showToast({
        title: '报告生成失败，请重试',
        icon: 'none',
        duration: 2000
      });
      
      wx.hideLoading(); 
      // 5秒后自动重试
      setTimeout(() => {
        this.loadReportData();
      }, 5000);
    }
  },

  gotoReport() {
    if(this.data.reportData){
      this.navigateToAuthRequiredPage(`/pages/promotion/report?examId=${this.data.examId}`)
    }else{
      wx.showToast({
        title: '报告正在生成中，预计1-2分钟，请稍后再试',
        icon: 'none',
        duration: 2000
      });
    }
  },
  gotoIndex(){
    wx.switchTab({
      url: '/pages/promotion/list'
    });
  }
})