.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: env(safe-area-inset-bottom);
}

.result-banner {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #fff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin: 20rpx;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.result-container{
  line-height: 88rpx;
  display: flex;
  justify-content: space-between; 
  background-color: #f8f9fa;
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
}
.item{
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 60rpx;
}
.item.correctItem .num{
  color: #009900;
}
.item.errorItem .num{
  color: #FF0000;
}
.item .unit{
  font-size: 22rpx;
}
.line{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.num{
  font-size: 35rpx;
  text-align: center;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 20rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  position: absolute;
  right: 20rpx;
  top: 20rpx;
}

.status-badge .iconfont {
  margin-right: 8rpx;
}

.status-badge.status-confirmed {
  background: #19b151;
}

.status-badge.status-pending {
  background: rgb(255, 189, 8);
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  margin: 20rpx 0 10rpx;
}

.result-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
}

.score-circle {
  width: 240rpx;
  height: 240rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx auto;
  border: 8rpx solid rgba(255, 255, 255, 0.3);
}

.score-value {
  font-size: 72rpx;
  font-weight: bold;
  line-height: 1;
}

.score-label {
  font-size: 28rpx;
  margin-top: 20rpx;
}

.pass-badge {
  display: inline-block;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: bold;
  margin-top: 20rpx;
}
.pass-badge-pass{
  background: #19b151;
}
.pass-badge-fail{
  background: #ff2222;
}
.pass-badge .iconfont{
  margin-right: 8rpx;
}

.section-title {
  font-weight: bold;
  margin: 48rpx 32rpx 24rpx 32rpx;
  display: flex;
  align-items: center;
}

.section-title .iconfont {
  margin-right: 16rpx;
  color: #a18cd1;
}

.score-card {
  background: #fff;
  border-radius: 32rpx;
  /* padding: 32rpx; */
  margin: 0 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
box-sizing: border-box;

  height: 450rpx;
  position: relative;
  z-index: 0;
  
}

.score-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx dashed rgba(0, 0, 0, 0.1);
}

.score-row:last-child {
  border-bottom: none;
}

.category-name {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.category-name .iconfont {
  margin-right: 16rpx;
  color: #a18cd1;
  width: 40rpx;
  text-align: center;
}

.category-score {
  font-weight: bold;
  color: #333;
}

.chart-card {
  background: #fff;
  border-radius: 32rpx;
  padding: 32rpx;
  margin: 0 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.chart-canvas {
  width: 700rpx;
  height: 500rpx;
}

.radar-container {
  width: 100%;
  height: 450rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.radar-canvas {
  width: 100%;
  height: 100%;
  display: block;
  z-index:0;
}

.feedback-card {
  background: #fff;
  border-radius: 32rpx;
  padding: 32rpx;
  margin: 0 32rpx 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.feedback-item {
  margin-bottom: 32rpx;
}

.feedback-item:last-child {
  margin-bottom: 0;
}

.feedback-title {
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #38a3d1;
  display: flex;
  align-items: center;
}

.feedback-title .iconfont {
  margin-right: 16rpx;
}

.feedback-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
}

.feedback-point {
  margin-bottom: 10rpx;
}

.next-steps {
  display: flex;
  justify-content: center;
  margin: 48rpx 32rpx;
  position:fixed;
  bottom:0px;
  left:0px;
  width:90%;
}

.btn {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #fff;
  border-radius: 44rpx;
  box-shadow: 0 8rpx 30rpx rgba(161, 140, 209, 0.4);
  padding:0px;
}
.btn.record-btn{
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  font-weight: bold;
  padding: 0 40rpx;
  margin:20rpx auto 0px auto;
  width:fit-content;
  display: block;

}
.back-btn{
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  font-weight: bold;
}


.text-green {
  color: #19b151;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-card, .feedback-card {
  animation: fadeInUp 0.5s ease-out;
} 