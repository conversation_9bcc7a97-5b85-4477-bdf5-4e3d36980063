<!-- 身份认证弹窗组件 -->
  <view class="identity-verify-popup {{visible ? 'visible' : ''}}">
    <view class="auth-modal" catchtap="preventBubble">
      <view class="auth-header">
        <view class="auth-icon">
          <text class="iconfont icon-user-shield"></text>
        </view>
        <view class="auth-title">企业员工身份认证</view>
        <view class="auth-subtitle">请完成企业员工身份认证以使用全部功能</view>
      </view>
      <view class="auth-form">
        <view class="form-group">
          <text class="form-label">手机号</text>
          <input 
            type="number" 
            wx:if="{{visible}}"
            class="form-input {{errors.realPhone ? 'error' : ''}}"
            value="{{form.realPhone}}" 
            bindinput="handleInputChange"
            data-field="realPhone"
            placeholder="请输入您的手机号" 
            maxlength="11"
          />
          <text class="form-error" wx:if="{{errors.realPhone}}">{{errors.realPhone}}</text>
        </view>
        <view class="form-group">
          <text class="form-label">姓名</text>
          <input 
            wx:if="{{visible}}"
            type="text" 
            class="form-input {{errors.realName ? 'error' : ''}}"
            value="{{form.realName}}" 
            bindinput="handleInputChange"
            data-field="realName"
            placeholder="请输入您的真实姓名"
          />
          <text class="form-hint">{{errors.realName}}</text>
        </view>
        <view class="form-group">
          <text class="form-label">身份证号</text>
          <input 
            wx:if="{{visible}}"
            type="idCard" 
            class="form-input {{errors.idNumber ? 'error' : ''}}"
            value="{{form.idNumber}}" 
            bindinput="handleInputChange"
            data-field="idNumber"
            placeholder="请输入您的身份证号码"
          />
          <text class="form-error" wx:if="{{errors.idNumber}}">{{errors.idNumber}}</text>
        </view>
        <view class="auth-actions">
          <button class="auth-cancel-btn" bindtap="cancelVerify">取消</button>
          <button class="auth-submit-btn" bindtap="submitVerify" loading="{{loading}}" disabled="{{loading}}">提交认证</button>
        </view>
      </view>
    </view>
  </view> 