// pages/practice/detail.js
import {
  getPromotionQuestion,
  answerPromotion,
  examReport,
  getContinueExam,
  getExamReport
  // evaluatePracticeAnswer ,
} from '../../api/promotion';
import { getFormattedTime, timeToSeconds } from '../../utils/dateFormat';
const authBehavior = require('../../behaviors/auth-behavior')


// 引入微信同声传译插件
const plugin = requirePlugin("WechatSI");
// 获取全局唯一的语音合成管理器
const manager = plugin.getRecordRecognitionManager();

Page({
  behaviors: [authBehavior],

  /**
   * 页面的初始数据
   */
  data: {
    practiceId: '', // 练习ID
    title: '', // 练习标题
    startTime: null, // 练习开始时间
    timer: null, // 计时器
    formatTime: '00:00', // 格式化的时间显示
    scrollTop: 0, // 滚动位置
    keyboardHeight: 0, // 键盘高度
    isVoiceMode: false, // 是否为语音模式
    isRecording: false, // 是否正在录音
    isCancelling: false, // 是否正在取消录音
    wasCancelled: false, // 是否已取消录音（用于onStop回调）
    inputContent: '', // 输入内容
    showSystemMessage: true, // 是否显示系统消息
    hasMore: false, // 是否有更多历史消息
    messages: [], // 消息列表
    isLoading: false, // 是否正在加载
    currentQuestion: null, // 当前题目信息
    questionId: '', // 当前题目ID
    question: '', // 当前题目内容
    completedQuestions: 0, // 已完成题目数量
    recordingTime: '00:00', // 录音时间
    recordTimer: null, // 录音计时器
    startY: 0, // 触摸开始位置Y坐标
    recorderManager: null, // 录音管理器
    innerAudioContext: null, // 音频播放器
    isPlaying: false, // 是否正在播放语音
    content: '', // 识别内容
    timeUpdateChecked: false, // 用于检查音频时长
    isRecovering: false, // 用于防止无限循环恢复
    timeUpdateCount: 0, // 用于检查音频时长
    hasRecordAuth: false, // 是否已获取录音权限
    navHeight: 0, // 导航栏高度
    chatPaddingTop: 0, // 聊天区域的paddingTop
    isIOS: false, // 是否为iOS设备
    desc: '', // 描述
    isPracticeStarted: false, // 是否已开始练习
    preloadedQuestion: null, // 预加载的题目
    windowWidth: 0 ,// 窗口宽度
    isSendDisabled:false, // 是否禁用发送按钮
    examId:0,
    knowledgeBaseId:0,
    countdownTime: "00:00", // 倒计时格式化显示
    countdownTimer: null, // 倒计时定时器
    isCountdownWarning: false, // 是否显示倒计时警告
    isLastQuestion:false, // 是否是最后一道题
    examInfo:null, // 考试信息
    questionIndex:1, // 当前题目索引
    progressBarWidth:0, // 进度条宽度
    status:0,
    endTime:0,
    isPageUnloading: false, // 标记页面是否正在卸载
    showSubmitPopup:false, // 是否显示提交考试弹窗
    submitPopupContent:'', // 提交考试弹窗内容
    submitPopupShowCancel:false ,// 提交考试弹窗是否显示取消按钮
    loadingText:'出题中',
    examFinished:false,
    examFinishedText:'答题已结束请提交试卷',
    showCountdown: false, // 是否显示倒计时
    countdownNumber: 3, // 倒计时数字
    recordingCountdownTimer: null, // 录音倒计时定时器
    submitAnswering:false,  // 是否提交答题
    remainingMinutes:0,// 剩余时间
    showGetQuestionTip:false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.setData({
      examInfo:wx.getStorageSync('examInfo'),
      examId: options.examId,
      knowledgeBaseId: options.knowledgeBaseId,
      status:options.status
    });

    // 获取考试时长，默认60分钟
    let examDuration = parseInt(this.data.examInfo.examDuration);

    // 确保examDuration至少为1分钟
    examDuration = Math.max(1, examDuration);


    this.setData({
      title:this.data.examInfo.title,
      examDuration: examDuration,
      // countdownTime: this.formatCountdownTime(examDuration * 60) // 转换为秒并格式化
    });

    // 检查并设置音频播放权限
    this.checkAudioPermission();

    // 初始化录音管理器
    this.initRecorderManager();

    // 初始化音频播放器
    this.initAudioContext();


    // 页面加载时就请求录音权限
    this.requestRecordPermission();

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      windowWidth: systemInfo.windowWidth
    });

    if(this.data.status==5){
      await getContinueExam({examId:this.data.examId}).then(res=>{
        this.setData({
          remainingMinutes:res.remainingMinutes,
          questionIndex:res.completedQuestions+1,
          title:res.examSubject,
        progressBarWidth: Math.ceil((res.completedQuestions/this.data.examInfo.questionCount)*100),

        })
        let examContent=res.examContent
        let contentMessage=[]
        for(let i=0;i<examContent.length;i++){
          let item=examContent[i]
          let question=item.question
          let userAnswer=item.userAnswer
          let questionId=item.id
          contentMessage.push({
            type:'ai',
            content:question,
            time:item.timeStamp,
            isPlaying:false,
            audioDuration:0,
            audioSrc:'',
            isAudioOnly:false
          })
          if(userAnswer){
            contentMessage.push({
              type:'user',
              content:userAnswer,
              time:item.timeStamp,
              isPlaying:false,
              audioDuration:0,
              audioSrc:'',
              isAudioOnly:false
            })
          }
        }
        let lastQuestion=examContent[examContent.length-1]

        // 如果上次没有答题，则直接调用获取下一题
        if(lastQuestion){
          this.setData({
            // 保存题目信息
              currentQuestion: lastQuestion,
              questionId: lastQuestion.questionId,
              question: lastQuestion.question,
              messages: [...contentMessage],
              endTime:res.endTime,
          });
        }else{
          this.getNextQuestion();
        }
      setTimeout(()=>{
       this.scrollToBottom()
      },1000)

      })
    }else{
      // 加载题目
      this.getNextQuestion();
    }
        // 启动倒计时
        this.startCountdown();
    // 启用页面返回提示，防止用户直接返回上一页
    // if (wx.enableAlertBeforeUnload) {
    //   wx.enableAlertBeforeUnload({
    //     message: '返回将离开考试页面',
    //     success: (res) => {
    //       console.log('启用返回提示成功', res);
    //     },
    //     fail: (err) => {
    //       console.error('启用返回提示失败', err);
    //     }
    //   });
    // }
  },


  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('页面隐藏，直接执行自动交卷');
    this.executeAutoSubmitOnLeave('hide');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('页面卸载，直接执行自动交卷');
    this.executeAutoSubmitOnLeave('unload');
  },

  /**
   * 直接执行自动交卷（页面离开时）
   * @param {string} type - 离开类型：'hide' 或 'unload'
   */
  executeAutoSubmitOnLeave(type) {
    // 如果考试已经结束或已经在处理中，不重复处理
    if (this.data.examFinished || this.data.isPageUnloading) {
      console.log('考试已结束或正在处理中，跳过自动交卷');
      this.clearEvent();
      if (type === 'unload') {
        this.navigateToExamList();
      }
      return;
    }

    // 如果还有未完成的题目，直接执行自动交卷
    const hasUnfinishedQuestions = this.data.questionIndex < this.data.examInfo.questionCount;

    if (hasUnfinishedQuestions) {
      console.log('检测到未完成的考试，直接执行自动交卷');

      // 设置页面正在卸载标志，防止重复处理
      this.setData({
        isPageUnloading: true
      });

      // 显示提交中的提示
      // wx.showToast({
      //   title: '正在自动交卷...',
      //   icon: 'loading',
      //   duration: 3000,
      //   mask: true
      // });

      // 直接执行提交逻辑
      this.executeDirectSubmit(type);
    } else {
      // 没有未完成的题目，正常清理
      this.clearEvent();
      if (type === 'unload') {
        this.navigateToExamList();
      }
    }
  },

  /**
   * 直接执行提交（不显示对话框）
   * @param {string} type - 离开类型
   */
  executeDirectSubmit(type) {
    console.log('开始直接执行提交逻辑');

    try {
      // 清理页面资源
      this.clearEvent();

      // 调用提交考试方法
      // this.submitExam();
      this.executeAutoSubmit()

      // console.log('提交方法已调用');

      // 等待提交完成
      setTimeout(() => {
        console.log('自动交卷完成');

        // 如果是页面卸载，跳转到考试列表
        if (type === 'unload') {
          this.navigateToExamList();
        }
      }, 1000); // 等待2秒确保提交完成

    } catch (error) {
      console.error('自动交卷失败:', error);

      // 即使失败也要跳转，避免用户卡在页面
      if (type === 'unload') {
        this.navigateToExamList();
      }
    }
  },
  /**
   * 跳转到考试列表页面
   */
  navigateToExamList() {
    wx.reLaunch({
      url: '/pages/promotion/list'
    });
  },

  clearEvent(){
    // 设置页面正在卸载标志
    this.setData({
      isPageUnloading: true
    });

    this.clearTimer();
    // 提交练习记录
    // this.submitPracticeRecord();

    // 释放音频资源
    if (this.innerAudioContext) {
      try {
        this.innerAudioContext.stop();
        // 移除所有事件监听
        this.innerAudioContext.offEnded();
        this.innerAudioContext.offError();
        this.innerAudioContext.offTimeUpdate();
        this.innerAudioContext.offCanplay();
        this.innerAudioContext.offPlay();
        this.innerAudioContext.destroy();
      } catch (error) {
        console.error('销毁音频上下文失败:', error);
      }
      this.innerAudioContext = null;
    }

    // 重置状态
    this.timeUpdateChecked = false;

    // 释放录音资源
    if (manager) {
      try {
        manager.stop();
      } catch (error) {
      }
    }

    // 清除倒计时定时器
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }

    // 清除录音倒计时定时器
    this.clearRecordingCountdownTimer();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 播放消息音频
   */
  playMessageAudio(e) {
    // 注意：e 可能是页面原生事件，也可能是组件自定义事件
    let index, type, isAudio;

    // 如果是组件事件，获取detail中的数据
    if (e.detail && (e.detail.index !== undefined || e.detail.type)) {
      index = e.detail.index;
      type = e.detail.type;
      isAudio = e.detail.isAudio;
    } else {
      // 如果是原生事件，从dataset获取数据
      index = e.currentTarget.dataset.index;
      type = e.currentTarget.dataset.type;
      isAudio = e.currentTarget.dataset.isAudio;
    }

    const messages = this.data.messages;
    const message = messages[index];

    if (!message || !message.content) return;

    // 如果当前消息正在播放，则停止播放
    if (message.isPlaying) {
      this.stopSpeech();
      this.updateMessagePlayingState(index, false);
      return;
    }

    // 停止所有正在播放的音频
    this.stopAllAudio();

    // 根据消息类型和内容类型执行不同的播放逻辑
    if (type === 'user' && isAudio && message.audioSrc) {
      // 用户消息是录音，直接播放原始录音
      console.log('播放用户录音音频:', message.audioSrc);
      this.playAudioFile(message.audioSrc, index, true);
    } else {
      // AI消息或用户文本消息，使用TTS转换为语音
      console.log('播放文本消息的TTS:', message);
      this.playTextToSpeech(message.content, index, true);
    }
  },

  // 更新消息的播放状态
  updateMessagePlayingState(index, isPlaying) {
    if (index < 0 || index >= this.data.messages.length) return;

    this.setData({
      [`messages[${index}].isPlaying`]: isPlaying
    });
  },

  // 停止所有正在播放的音频
  stopAllAudio() {
    const messages = this.data.messages;
    if (messages && messages.length > 0) {
      const updatedMessages = messages.map(msg => {
        if (msg.isPlaying) {
          msg.isPlaying = false;
        }
        return msg;
      });

      this.setData({
        messages: updatedMessages,
        isPlaying: false
      });
    }

    // 停止当前播放
    this.stopSpeech();
  },

  /**
   * 初始化音频播放器
   */
  initAudioContext() {
    // 确保先销毁之前的实例
    this.releaseAudioContext();

    // 创建内部音频上下文
    const innerAudioContext = wx.createInnerAudioContext({useWebAudioImplement: false});

    // 设置音量为最大值
    innerAudioContext.volume = 1.0;
    innerAudioContext.useWebAudioImplement=true


    // 音频播放开始事件
    innerAudioContext.onPlay(() => {
      console.log('音频播放开始');
      this.setData({
        isPlaying: true
      });
    });

    // 音频播放结束事件
    innerAudioContext.onEnded(() => {
      this.setData({
        isPlaying: false
      });
    });

    // 音频播放错误事件
    innerAudioContext.onError((res) => {
      console.error('音频播放错误:', res);
      this.setData({
        isPlaying: false
      });

    });

    // 监听音频加载中事件
    innerAudioContext.onWaiting(() => {
      console.log('音频加载中...');
    });

    // 监听音频可以播放事件
    innerAudioContext.onCanplay(() => {
    });

    this.innerAudioContext = innerAudioContext;
  },

  /**
   * 释放音频上下文资源
   */
  releaseAudioContext() {
    if (this.innerAudioContext) {
      try {
        this.innerAudioContext.stop();
        // 移除所有事件监听
        this.innerAudioContext.offEnded();
        this.innerAudioContext.offError();
        this.innerAudioContext.offTimeUpdate();
        this.innerAudioContext.offCanplay();
        this.innerAudioContext.offPlay();
        this.innerAudioContext.destroy();
      } catch (error) {
        console.error('销毁音频上下文失败:', error);
      }
      this.innerAudioContext = null;
    }
  },

  /**
   * 统一的文本转语音播放方法
   * @param {string} text - 要转换为语音的文本
   * @param {number} messageIndex - 消息在列表中的索引，用于更新状态
   * @param {boolean} autoPlay - 是否自动播放
   */
  playTextToSpeech(text, messageIndex = -1, autoPlay = false) {
    if (!text) return;

    // 停止当前正在播放的音频
    this.stopSpeech();

    // 重置timeUpdate检查状态
    this.timeUpdateChecked = false;
    // 重置timeUpdate计数器
    this.timeUpdateCount = 0;

    // 如果指定了消息索引，则更新其播放状态
    if (messageIndex >= 0) {
      // 停止其他正在播放的音频的状态
      this.stopAllAudio();

      // 设置当前消息为播放状态
      if(autoPlay){
        this.updateMessagePlayingState(messageIndex, true);
      }

      // 如果消息已经有语音文件，直接播放
      const message = this.data.messages[messageIndex];
      if (message && message.audioSrc && !message.isAudio) {
        console.log('使用已缓存的TTS语音文件播放:', message.audioSrc);
        this.playAudioFile(message.audioSrc, messageIndex,autoPlay);
        return;
      }
    }else {
      // 通用播放状态
      this.setData({
        isPlaying: true
      });
    }

    // 确保先销毁之前的实例
    this.releaseAudioContext();

    // 创建新的音频实例
    this.innerAudioContext = wx.createInnerAudioContext({useWebAudioImplement: false});

    // 设置音频事件处理
    this.setupAudioEvents(messageIndex);

    // 使用插件进行语音合成
    const plugin = requirePlugin("WechatSI");
    plugin.textToSpeech({
      lang: 'zh_CN',
      tts: true,
      content: text,
      success: (res) => {
        // 获取当前时间
        const currentTime = getFormattedTime();
        console.log('语音合成成功:', currentTime,res);

        if (!res.filename) {
          this.updateMessagePlayingState(messageIndex, false);
          return;
        }

        // 保存语音文件URL到消息对象
      if (messageIndex >= 0) {
        this.setData({
            [`messages[${messageIndex}].audioSrc`]: res.filename
          });
        }

        // 播放合成的音频
        this.playAudioSource(res.filename,autoPlay);
      },
      fail: (res) => {
        console.error('语音合成失败:', res);
        this.updateMessagePlayingState(messageIndex, false);

        // wx.showToast({
        //   title: '语音合成失败',
        //   icon: 'none'
        // });
      }
    });
  },

  /**
   * 设置音频事件处理
   * @param {number} messageIndex - 消息索引
   */
  setupAudioEvents(messageIndex = -1) {
    if (!this.innerAudioContext) return;

    // 设置音频结束事件
    this.innerAudioContext.onEnded(() => {
      this.updateMessagePlayingState(messageIndex, false);
    });

    // 设置音频错误事件
    this.innerAudioContext.onError((res) => {
      console.error('音频播放错误', res);
      this.updateMessagePlayingState(messageIndex, false);

      // 尝试恢复
      if (messageIndex >= 0 && this.data.messages[messageIndex]) {
      setTimeout(() => {
          this.tryRecoverAudio(this.data.messages[messageIndex].content, messageIndex);
      }, 1000);
      }
    });

    // 设置音频加载完成事件，获取实际时长
    this.innerAudioContext.onCanplay(() => {
      console.log('音频加载完成:', this.innerAudioContext);
      this.checkAudioDuration(messageIndex);
    });

    // 当播放开始时也检查duration，此时通常已经有值
    this.innerAudioContext.onPlay(() => {
      this.checkAudioDuration(messageIndex);
    });

    // 使用timeUpdate事件作为最后的保障，确保能获取到duration
    this.innerAudioContext.onTimeUpdate(() => {
      // 只检查前5次timeUpdate事件，增加获取duration的机会
      if (messageIndex >= 0 && (!this.timeUpdateCount || this.timeUpdateCount < 5)) {
        // 初始化或递增计数器
        this.timeUpdateCount = (this.timeUpdateCount || 0) + 1;
        if (!this.data.messages[messageIndex]?.audioDuration ||
            this.data.messages[messageIndex].audioDuration === '00:00') {
        this.checkAudioDuration(messageIndex);
          } else {
          // 已经获取到时长，重置计数器
          this.timeUpdateCount = 5;
        }
      }
    });
  },

  /**
   * 播放音频源
   * @param {string} src - 音频文件路径
   */
  playAudioSource(src,autoPlay = false) {
    if (!src || !this.innerAudioContext) return;

    try {
      // 设置音频源
      this.innerAudioContext.src = src;

      // 添加延迟后自动播放，确保src设置完成
      setTimeout(() => {
        try {
          // 先判断是否已被销毁
          if (this.innerAudioContext&&autoPlay) {
            this.innerAudioContext.play();
          }
        } catch (error) {
          // console.error('播放音频失败:', error);
            this.setData({
              isPlaying: false
            });
          }
      }, 200);
    } catch (error) {
      console.error('设置音频源失败:', error);
          this.setData({
            isPlaying: false
          });
        }
  },

  /**
   * 直接播放语音文件
   * @param {string} audioSrc - 语音文件URL
   * @param {number} messageIndex - 消息索引
   */
  playAudioFile(audioSrc, messageIndex,autoPlay = false) {
    if (!audioSrc) return;

    // 重置timeUpdate检查状态
    this.timeUpdateChecked = false;
    this.timeUpdateCount = 0;

    // 确保先销毁之前的实例
    this.releaseAudioContext();

    // 创建新的音频实例
    this.innerAudioContext = wx.createInnerAudioContext({useWebAudioImplement: false});

    // 设置音频事件处理
    this.setupAudioEvents(messageIndex);

    // 更新消息状态为播放中
    this.updateMessagePlayingState(messageIndex, true);

    // 播放音频
    this.playAudioSource(audioSrc,autoPlay);
  },

  /**
   * 停止语音播放
   */
  stopSpeech() {
    if (this.innerAudioContext) {
      try {
        // 先暂停再停止，有助于解决某些机型上的问题
        try {
          this.innerAudioContext.pause();
        } catch (e) {
          console.error('暂停音频失败:', e);
        }

        // 停止音频播放
        this.innerAudioContext.stop();
      } catch (error) {
        console.error('停止音频失败:', error);
      }

      // 重置状态
      this.timeUpdateChecked = false;
    }
  },

  /**
   * 尝试恢复音频播放
   */
  tryRecoverAudio(text, messageIndex) {
    console.log('尝试恢复音频播放');

    // 确保音频实例被重新创建
    this.releaseAudioContext();

    // 如果有缓存的音频文件，尝试直接播放
    const message = messageIndex >= 0 ? this.data.messages[messageIndex] : null;
    if (message && message.audioSrc) {
      console.log('使用缓存的音频文件恢复播放:', message.audioSrc);
      this.playAudioFile(message.audioSrc, messageIndex);
    } else if (text) {
      // 否则尝试重新合成
      // 设置一个标志避免无限循环
      if (!this.isRecovering) {
        this.isRecovering = true;
        this.playTextToSpeech(text, messageIndex);
        setTimeout(() => {
          this.isRecovering = false;
        }, 3000);
      }
    }
  },

  // 获取下一道题目
  async getNextQuestion(e) {
   // 检查按钮是否已禁用
    if (e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.disabled) {
      return;
    }

    this.scrollToBottom()
    try {
      const params = {
        "knowledgeBaseId":this.data.knowledgeBaseId,
        "examId":this.data.examId
      };
      // 最后一题 提交报告
      if(this.data.isLastQuestion){
        return;
      }else{
        this.setData({ isLoading: true,isSendDisabled:true });
      }
      const result = await getPromotionQuestion(params);
      if (result) {
          this.setData({
            isLastQuestion:result.isLastQuestion||false,
          })
        const  outputs  = result;

        // 保存题目信息
        this.setData({
          currentQuestion: outputs,
          questionId: outputs.questionId,
          question: outputs.question,
        });

        // 添加AI消息
        const aiMessage = {
          type: 'ai',
          content: outputs.question,
          time: getFormattedTime(),
          isPlaying: false,
          audioDuration: 0,
          audioSrc: '',
          isAudioOnly: false
        };
        this.setData({
          messages: [...this.data.messages, aiMessage],

        });

        // 获取当前时间
        const currentTime = getFormattedTime();
        // 将问题转换为语音并播放
        this.playTextToSpeech(outputs.question, this.data.messages.length - 1);
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      }
    } catch (error) {
      console.error('获取题目失败:', error);
      wx.showToast({
        title: '获取题目失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false,
        isSendDisabled:false,
       });
    }
  },

  startTimer() {
    const startTime = Date.now();
    this.setData({
      startTime: startTime
    });

    const timer = setInterval(() => {
      const diff = Math.floor((Date.now() - this.data.startTime) / 1000);
      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');
      const seconds = (diff % 60).toString().padStart(2, '0');
      const formatTime = `${minutes}:${seconds}`;

      this.setData({
        formatTime: formatTime
      });
    }, 1000);

    this.timer = timer;
  },

  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.switchTab({
      url: '/pages/promotion/list'
    });
  },

  toggleInputType() {
    if(!this.data.isSendDisabled){
      this.setData({
        isVoiceMode: !this.data.isVoiceMode,
        // inputContent: ''
      });

      if (this.data.isVoiceMode) {
        wx.hideKeyboard();
      }
    }
  },

  onInputFocus(e) {
    this.setData({
      keyboardHeight: e.detail.height || 0
    });
    console.log('键盘高度',e.detail.height);
    // 键盘弹出后，滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 300);
  },

  onInputBlur(e) {
    this.setData({
      keyboardHeight: 0,
      inputContent:e.detail.value
    });
    // 键盘收起后，重新滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 300);
  },


  onScrollToUpper() {
    // 暂不实现加载更多历史消息
  },

  /**
   * 更新问题索引和进度条
   * @param {boolean} isAnswered
   */
  updateQuestionIndexAndProgress(isAnswered) {
    if (isAnswered) {
      let questionIndex = this.data.examInfo.questionCount == this.data.questionIndex ?
        this.data.examInfo.questionCount : this.data.questionIndex + 1;
      this.setData({
        questionIndex: questionIndex,
        progressBarWidth: Math.ceil((this.data.questionIndex/this.data.examInfo.questionCount)*100)
      });
    }
  },

  // 发送用户回答并获取解析
  async sendMessage(e) {
    // 判断是否已考试完成
    if(this.data.examFinished){
      return
    }
    if(!this.data.isSendDisabled){
    const content=e.detail.content||this.data.inputContent
    if (!this.data.inputContent.trim()) {
      wx.showToast({
        title: '请输入回答内容',
        icon: 'none'
      });
      return
    }
    if (!this.data.questionId || !this.data.question ) {
      wx.showToast({
        title: '未获取到题目，请重试',
        icon: 'none'
      });
      return;
    }

    // 停止当前语音播放
    this.stopSpeech();

    // 构造用户消息
    const userMessage = {
      type: 'user',
      content: content,
      time: getFormattedTime(),
      isAudio: false, // 标记为非音频消息
      audioSrc: '' // 文本消息没有音频源
    };

    // 添加消息到列表
    const newMessages = [...this.data.messages, userMessage];

    this.scrollToBottom();
    // this.playTextToSpeech(userMessage.content, this.data.messages.length - 1, true);

    // 更新问题索引和进度条
    this.updateQuestionIndexAndProgress(true);

    // 设置消息列表
    this.setData({
      messages: newMessages
    });

    // 清空输入框内容
    setTimeout(() => {
      this.setData({
        inputContent: ''
      });
    }, 50);
     // 一问一答解析
      this.parseOneQuestionOneAnswer(content);
  }else{
    wx.showToast({
      title: '出题中请稍后...',
      icon: 'none'
    });
  }

  },
  // 重新获取试题
  onGetQuestion(){
    this.setData({
      showGetQuestionTip:false
    })
    this.getNextQuestion();
  },
  // 用户回答解析
  async parseOneQuestionOneAnswer(content) {
    this.setData({
      isSendDisabled:true,
      submitAnswering:true,
      // isLoading:true,
    })
    // if(this.data.questionIndex!=this.data.examInfo.questionCount){
    //   this.setData({
    //     isLoading:true,
    //   })
    // }else{
    //  // TODO: 最后一题 examFinished   true 显示答题已结束，但answer接口慢可能会出现最后一道题目未提交情况

    // }
    // 获取message 中type 为ai的数量
    const aiMessageCount=this.data.messages.filter(item=>item.type=='ai').length
    if(aiMessageCount<this.data.examInfo.questionCount){
      this.setData({
        isLoading:true,
      })
    }else{
      this.setData({
        isLoading:false,
        examFinished:true,
        isSendDisabled:true,
      })
    }

    try {
      const params={
          "questionId":this.data.questionId,
          "examId":this.data.examId,
          "userAnswer":content
      }
      let result = await answerPromotion(params)
       // 延迟一段时间后，获取下一道题目
          this.scrollToBottom();
          this.getNextQuestion();
          if(result){
            this.setData({
              submitAnswering:false,
            })
            // 考试已完成
            if(result&&result.examFinished){
              this.setData({
                examFinished:result.examFinished,
                isSendDisabled:true,
              })
            }else{
              this.setData({
                // 禁用发送按钮
                isSendDisabled: false,
              });
            }
          }

      // }
    } catch (error) {
      console.error('解析回答失败:', error);
      if(!this.data.examFinished){
        this.setData({
          isLoading: false,
          // 禁用发送按钮
          isSendDisabled: false,
          // 显示获取试题提示
          showGetQuestionTip:true,
        });
        wx.showToast({
          title: '获取试题失败，请重新获取',
          icon: 'none'
        });
      }
    } finally {
      this.setData({
        isLoading: false,
        // 禁用发送按钮
        // isSendDisabled: false,
       });
    }
  },

  scrollToBottom() {
    setTimeout(() => {
      const chatContainer = this.selectComponent('#chatContainer');
      if (chatContainer && typeof chatContainer.scrollToBottom === 'function') {
        chatContainer.scrollToBottom();
      }
    }, 100);
  },

  // 初始化录音管理器
  initRecorderManager() {
    const that=this;
    manager.onError((res) => {
      console.log('识别错误：', res);
      // 出错时也需要重置录音状态
      that.setData({
        isRecording: false,
        isCancelling: false,
        isLoading: false
      });

      wx.showToast({
        title: '录音出错，请重试',
        icon: 'none'
      });
    });

    manager.onStart=function(res) {
      console.log('识别开始：', res);
    };

    // 添加录音中断事件处理
    manager.onInterruptionBegin = function() {
      console.log('录音被中断');
      // 如果录音被中断，也应该停止录音并重置状态
      that.stopRecordTimer();
      that.setData({
        isRecording: false,
        isCancelling: false
      });
    };

    // 添加录音被强制停止事件处理
    manager.onStop=function(res){
      console.log('识别结束onstop：', res);

      // 检查是否是被取消的录音
      if (that.data.wasCancelled) {
        console.log('录音已被用户取消，不处理录音结果');
        // 重置取消标志
        that.setData({
          wasCancelled: false,
          isLoading:false,
        });
        return;
      }
      console.log('结束data',that.data)

      // 语音识别结束，获取文本内容和临时录音文件
      const text = res.result || '';
      const tempFilePath = res.tempFilePath || '';

      // 防止二次判断，此处保留原有逻辑以兼容之前的实现
      if (that.data.isCancelling) {
        // 如果是取消录音，重置状态
        that.setData({
          isRecording: false,
          isCancelling: false,
          isLoading:false,
        });
        return; // 如果是取消录音，不进行处理
      }

      // 添加用户录音消息

      if (tempFilePath) {
        if(!text){
          wx.showToast({
            title: '语音录入未识别，请重新回答问题',
            icon: 'none'
          });
          return;
        }
        // 构造用户消息
        const userMessage = {
          type: 'user',
          content: text, // 显示识别的文本内容
          time: getFormattedTime(),
          isAudio: true, // 标记为音频消息
          audioSrc: tempFilePath, // 保存录音文件路径
          audioDuration: that.data.recordingTime || '00:00' // 保存录音时长
        };

        // 添加消息到列表并更新输入框
        const newMessages = [...that.data.messages, userMessage];

        // 更新问题索引和进度条
        that.updateQuestionIndexAndProgress(true);

        that.setData({
          messages: newMessages,
          inputContent: text, // 将识别的文本设置到输入框
          isLoading:true,
        });

        that.scrollToBottom();

        // 如果识别出文本，则解析答案
        if (text && text.trim()) {
            that.parseOneQuestionOneAnswer(text);
        }

      } else {
        // 如果没有录音文件但有识别文本，设置为输入框内容
        if (text && text.trim()) {
          that.setData({
            inputContent: text
          });
          // 不自动发送，等用户确认后发送
        }
      }

      that.setData({
        isRecording: false,
        isCancelling: false
      });
    };

    // 添加最大录音时长限制监听
    manager.onRecognize = function(res) {
      console.log('边录边识别：', res);
    };
  },

  // 开始录音计时
  startRecordTimer() {
    // 清除可能存在的计时器
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    const startTime = Date.now();
    const recordTimer = setInterval(() => {
      const diff = Math.floor((Date.now() - startTime) / 1000);
      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');
      const seconds = (diff % 60).toString().padStart(2, '0');
      const recordingTime = `${minutes}:${seconds}`;

      this.setData({
        recordingTime: recordingTime
      });

      // 如果录音时长超过60秒，自动停止
      if (diff >= 58) {  // 设置为58秒，留出2秒的处理时间
        console.log('录音时长超过限制，自动停止');
        this.stopRecording({
          type: 'timeout',
          timeStamp: Date.now()
        });
      }
    }, 1000);

    this.setData({
      recordTimer: recordTimer
    });
  },

  // 停止录音计时
  stopRecordTimer() {
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
      this.setData({
        recordTimer: null,
        recordingTime: '00:00'
      });
    }
  },

  // 手指触摸移动事件
  onTouchMove(e) {
    if (!this.data.isRecording) return;

    try {
      // 阻止页面滚动
      e.preventDefault?.(); // 尝试阻止默认事件（可能不支持）

      // 获取当前触摸点的Y坐标
      const currentY = e.detail.touches[0].clientY;
      // 计算移动距离
      const moveDistance = this.data.startY - currentY;

      // 如果向上移动超过50像素，标记为取消状态
      if (moveDistance > 50) {
        if (!this.data.isCancelling) {
          this.setData({
            isCancelling: true
          });
        }
      } else {
        if (this.data.isCancelling) {
          this.setData({
            isCancelling: false
          });
        }
      }
    } catch (error) {
      console.error('处理触摸移动事件失败:', error);
      // 出错时重置录音取消状态，防止页面卡住
      this.setData({
        isCancelling: false
      });
    }

    // 返回false阻止事件冒泡和默认行为
    return false;
  },

  startRecording(e) {
    if(this.data.examFinished){
      // wx.showToast({
      //   title: this.data.examFinishedText,
      //   icon: 'none'
      // });
      return
    }
    if(!this.data.isSendDisabled){

      // 获取触摸的起始Y坐标
      const startY = e.detail.touches[0].clientY;
      this.setData({
        startY: startY,
        isCancelling: false,
        wasCancelled: false // 重置取消标志
      });

      // 检查是否已有录音权限
      if (this.data.hasRecordAuth) {
        // 已有权限，开始倒计时
        this.startRecordingCountdown();
      } else {
        // 没有权限，提示用户并重置录音状态
          this.setData({
            isRecording: false
          });

          wx.showModal({
            title: '提示',
          content: '需要您授权录音权限才能使用语音功能',
            confirmText: '去授权',
            success: (res) => {
              if (res.confirm) {
              wx.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.record']) {
                    this.setData({
                      hasRecordAuth: true
                    });
              }
            }
          });
            }
        }
      });
      }
    }else{
      wx.showToast({
        title: '出题中请稍后...',
        icon: 'none'
      });
    }
  },

  // 开始录音倒计时
  startRecordingCountdown() {
    console.log('开始录音倒计时');

    this.setData({
      showCountdown: true,
      countdownNumber: 3,
      isRecording: true
    });

    // 清除可能存在的倒计时定时器
    this.clearRecordingCountdownTimer();

    // 开始倒计时
    let count = 3;
    const recordingCountdownTimer = setInterval(() => {
      count--;

      if (count > 0) {
        this.setData({
          countdownNumber: count
        });
      } else {
        // 倒计时结束，开始录音
        this.setData({
          showCountdown: false,
          countdownNumber: 3
        });

        // 清除倒计时定时器
        this.clearRecordingCountdownTimer();

        // 开始实际录音
        this.startActualRecording();
      }
    }, 300);

    this.setData({
      recordingCountdownTimer: recordingCountdownTimer
    });
  },

  // 开始实际录音
  startActualRecording() {
    console.log('倒计时结束，开始实际录音');

    // 开始录音
    console.log('开始录音', manager);
    manager.start({
      lang: 'zh_CN',
    });

    // 开始录音计时
    this.startRecordTimer();
  },

  // 清除录音倒计时定时器
  clearRecordingCountdownTimer() {
    if (this.data.recordingCountdownTimer) {
      clearInterval(this.data.recordingCountdownTimer);
      this.setData({
        recordingCountdownTimer: null
      });
    }
  },

  stopRecording(e) {
    console.log('stopRecording触发', e);

    // 如果正在倒计时，取消倒计时
    if (this.data.showCountdown) {
      console.log('倒计时期间松开，取消倒计时');
      this.cancelRecordingCountdown();
      return;
    }

    if (!this.data.isRecording) return;

    // 如果正在取消录音，则调用取消录音方法
    if (this.data.isCancelling) {
      this.cancelRecording();
      return;
    }

    // 停止录音计时
    this.stopRecordTimer();

    // 停止录音
    manager.stop();

    this.setData({
      isRecording: false,
    });

    // 判断录音时长是否小于2秒
    // if (totalSeconds < 1) {
    //   wx.showToast({
    //     title: '录音时间过短',
    //     icon: 'none'
    //   });
    //   return;
    // }
  },

  cancelRecording() {
    // 如果正在倒计时，取消倒计时
    if (this.data.showCountdown) {
      this.cancelRecordingCountdown();
      return;
    }

    this.setData({
      wasCancelled: true // 设置已取消标志
    })
    // 停止录音计时
    this.stopRecordTimer();

    // 重置录音状态，并设置取消标志
    this.setData({
      isRecording: false,
      isCancelling: false,
    });

    // 停止录音
    manager.stop();
    if (!this.data.isRecording) return;

  },

  // 取消录音倒计时
  cancelRecordingCountdown() {
    console.log('取消录音倒计时');

    // 清除倒计时定时器
    this.clearRecordingCountdownTimer();

    // 隐藏倒计时遮罩
    this.setData({
      showCountdown: false,
      countdownNumber: 3,
      isRecording: false
    });
  },

  // 处理组件触发的取消倒计时事件
  onCancelCountdown() {
    console.log('收到组件取消倒计时事件');
    this.cancelRecordingCountdown();
  },

  // 处理上滑状态变化
  onSlideChange(e) {
    console.log('收到上滑状态变化事件', e);
    const { isMovingUp } = e.detail;

    // 更新取消状态
    this.setData({
      isCancelling: isMovingUp
    });
  },

  // 处理录音结束事件
  onRecordingEnd(e) {
    console.log('收到录音结束事件', e);
    const { isCancel } = e.detail;

    if (isCancel) {
      // 取消录音
      this.cancelRecording();
    } else {
      // 正常结束录音
      this.stopRecording();
    }
  },

  /**
   * 检查并设置音频播放权限
   */
  checkAudioPermission() {
    // 音频播放不需要显式授权，但可以检查设备静音状态
    wx.getSystemInfo({
      success: (res) => {
        console.log('系统信息:', res);
        // 提示用户确保设备未静音
        if (!res.microphoneAuthorized) {
          wx.showToast({
            title: '请确保已授予麦克风权限',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });

    // 尝试播放一个空音频以初始化音频引擎
    const testAudio = wx.createInnerAudioContext();
    testAudio.autoplay = true;
    testAudio.volume = 0;
    testAudio.playbackRate = 5;
    testAudio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA';
    testAudio.useWebAudioImplement=true

    // 短暂播放后销毁
    setTimeout(() => {
      testAudio.destroy();
    }, 500);
  },


  // 检查音频时长
  checkAudioDuration(messageIndex) {
    if (messageIndex < 0 || messageIndex >= this.data.messages.length) {
      console.error('无效的消息索引:', messageIndex);
      return;
    }

    const message = this.data.messages[messageIndex];
    if (message.type !== 'audio' && message.type !== 'tts' && message.type !== 'ai' && message.type !== 'user') {
      console.error('非音频消息类型:', message.type);
      return;
    }

    // 设置初始加载状态
    if (!message.audioDuration) {
      this.setData({
        [`messages[${messageIndex}].isAudioLoading`]: true
      });
    }

    // 添加重试机制
    let retryCount = 0;
    const maxRetries = 5;
    const retryIntervals = [100, 300, 500, 1000, 2000]; // 递增的等待时间

    const checkDuration = () => {
      if (this.innerAudioContext && this.innerAudioContext.duration &&
          !isNaN(this.innerAudioContext.duration) && this.innerAudioContext.duration > 0) {
        // 成功获取到时长
        console.log(`成功获取音频时长: ${this.innerAudioContext.duration}秒`);
        this.formatAndSetAudioDuration(messageIndex, this.innerAudioContext.duration);
      } else if (retryCount < maxRetries) {
        // 重试
        console.log(`尝试获取音频时长，第${retryCount + 1}次尝试`);
        setTimeout(() => {
          retryCount++;
          checkDuration();
        }, retryIntervals[retryCount]);
      } else {
        // 无法获取时长，使用估算值
        console.warn('无法获取音频时长，使用估算值');
        const estimatedDuration = this.estimateAudioDuration(message.content);
        this.formatAndSetAudioDuration(messageIndex, estimatedDuration);
      }
    };

    // 初始检查
    setTimeout(() => {
      checkDuration();
    }, 300);
  },

  /**
   * 根据文本内容估算音频时长
   * @param {string} content - 文本内容
   * @returns {number} - 估计的时长（秒）
   */
  estimateAudioDuration(content) {
    if (!content) return 5; // 默认最少5秒

    // 中文朗读大约每分钟300字，每字平均0.2秒
    // 英文朗读大约每分钟150词，每词平均0.4秒
    const chineseCharCount = (content.match(/[\u4e00-\u9fa5]/g) || []).length;
    const otherCharsCount = content.length - chineseCharCount;

    // 估算总时长（秒）
    let estimatedDuration = chineseCharCount * 0.2 + otherCharsCount * 0.1;

    // 确保最少有5秒的时长，并处理极端情况
    return Math.max(5, Math.min(estimatedDuration, 300)); // 最少5秒，最多5分钟
  },

  /**
   * 格式化并设置音频时长
   * @param {number} index - 消息索引
   * @param {number} duration - 音频时长（秒）
   */
  formatAndSetAudioDuration(index, duration) {
    // 格式化时长为 mm:ss 格式
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    const formattedDuration = `${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`;

    // 设置波形宽度和时长
    this.setWaveformWidth(index, duration);

    // 更新数据
        this.setData({
      [`messages[${index}].audioDuration`]: formattedDuration,
      [`messages[${index}].rawDuration`]: duration,
      [`messages[${index}].isAudioLoading`]: false
    });
  },

  /**
   * 设置音频波形宽度
   * @param {number} index - 消息索引
   * @param {number} duration - 音频时长（秒）
   */
  setWaveformWidth(index, duration) {
    // 根据音频时长计算波形宽度，但要考虑屏幕宽度的限制
    const baseWidth = this.data.windowWidth * 0.6; // 基础宽度为屏幕的60%
    const maxWidth = this.data.windowWidth * 0.8; // 最大宽度为屏幕的80%
    const minWidth = this.data.windowWidth * 0.4; // 最小宽度为屏幕的40%

    // 计算波形宽度：基础宽度 + 每秒额外宽度（但有最大/最小限制）
    let waveformWidth = baseWidth + (duration - 10) * 5; // 每秒增加5px宽度
    waveformWidth = Math.max(minWidth, Math.min(waveformWidth, maxWidth));

    this.setData({
      [`messages[${index}].waveformWidth`]: waveformWidth + 'px'
    });
  },

  /**
   * 请求录音权限
   */
  requestRecordPermission() {
    wx.authorize({
      scope: 'scope.record',
      success: () => {
        this.setData({
          hasRecordAuth: true
        });
      },
      fail: () => {
        console.log('未获取录音权限');
        this.setData({
          hasRecordAuth: false
        });

        // 显示获取权限对话框
        wx.showModal({
          title: '提示',
          content: '需要您授权录音权限才能使用语音功能',
          confirmText: '去授权',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.record']) {
                    this.setData({
                      hasRecordAuth: true
                    });
                  }
                }
              });
            }
          }
        });
      }
    });
  },

  // 捕获触摸移动事件，用于阻止页面滚动
  catchTouchMove() {
    // 直接返回false阻止事件冒泡和默认行为
    return false;
  },


  /**
   * 启动倒计时
   */
  startCountdown() {
    // 清除可能存在的定时器
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }

    let endTime=0
    if(this.data.status==5){
      endTime = Date.now() + this.data.remainingMinutes * 60 * 1000;
      // endTime=timeToSeconds(this.data.endTime)
    }else{
    // 计算结束时间（当前时间 + 考试时长）
      endTime = Date.now() + this.data.examDuration * 60 * 1000;
    }

    // 创建定时器，每秒更新一次
    const countdownTimer = setInterval(() => {
      // 计算剩余时间（秒）
      const remainingTime = Math.max(0, Math.floor((endTime - Date.now()) / 1000));
      // 格式化剩余时间
      const formattedTime = this.formatCountdownTime(remainingTime);
      // 判断是否进入警告状态（小于5分钟）
      const isWarning = remainingTime <= 300;

      this.setData({
        countdownTime: formattedTime,
        isCountdownWarning: isWarning
      });

      // 更新倒计时组件的样式
      // if (isWarning) {
      //   wx.nextTick(() => {
      //     const countdownBox = wx.createSelectorQuery().select('.countdown-box');
      //     if (countdownBox) {
      //       countdownBox.addClass('countdown-warning');
      //     }
      //   });
      // }

      // 如果时间到了，停止倒计时并提示
      if (remainingTime <= 0) {
        clearInterval(this.data.countdownTimer);
        // 检查页面是否正在卸载，如果是则不执行handleTimeUp
        if (!this.data.isPageUnloading) {
          this.handleTimeUp(false);
        }
      }
    }, 1000);

    this.setData({
      countdownTimer: countdownTimer
    });
  },

  /**
   * 格式化倒计时时间
   * @param {number} totalSeconds - 总秒数
   * @returns {string} - 格式化后的时间字符串（mm:ss）
   */
  formatCountdownTime(totalSeconds) {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  },

  /**
   * 处理时间到期事件
   */
  handleTimeUp(showCancel=true) {
    // 如果页面正在卸载，不执行后续操作
    if (this.data.isPageUnloading) {
      console.log('页面正在卸载，不执行handleTimeUp');
      return;
    }

    wx.showModal({
      title: '提示',
      content: '考试时间已结束',
      showCancel: false,
      success: (res) => {
        if (res.confirm) {
          // 时间到了自动提交
          this.submitExam();
        }
      }
    });
  },

  // 执行自动交卷
  async executeAutoSubmit(){
    // 调用API获取考试报告
    await getExamReport({
      exam_id: this.data.examId,
      isEarlySubmission: true
    });
    console.log('自动交卷成功')
    // 跳转页面
    this.goBack()
  },

  /**
   * 提交考试
   */
submitExam() {
      setTimeout(() => {
        if(this.data.isLastQuestion){
          this.navigateToAuthRequiredPage(`/pages/promotion/reporting/reporting?examId=${this.data.examId}`)
        }else{
          this.navigateToAuthRequiredPage(`/pages/promotion/reporting/reporting?examId=${this.data.examId}&isEarlySubmission=true`)
        }
      }, 500);
  },

  /**
   * 提交试卷按钮点击事件
   */
  onEndExam(showCancel=true) {
    if(this.data.submitAnswering){
      wx.showToast({
        title: '正在提交答案，请稍后...',
        icon: 'none'
      })
      return;
    }
    // 判断是否已经答完所有题目
    const isAllCompleted = this.data.questionIndex >= this.data.examInfo.questionCount;

    let content = '';
    if (isAllCompleted) {
      content = '您已完成所有题目，确定要提交试卷吗？';
    } else {
      content = `您还有${this.data.examInfo.questionCount - this.data.questionIndex+1}道题未完成，确定要提交试卷吗？提前交卷可能会影响您的成绩。`;
      showCancel=true
    }
    this.setData({
      showSubmitPopup:true,
      submitPopupContent:content,
      submitPopupShowCancel:showCancel
    })
    // wx.showModal({
    //   title: '提交考试',
    //   content: content,
    //   confirmText: '确认提交',
    //   showCancel:showCancel,
    //   // cancelText: '继续答题',
    //   success: (res) => {
    //     if (res.confirm) {
    //       // 用户点击确定，提交考试
    //       this.submitExam();
    //     }
    //     // 用户点击取消，继续答题，不需要额外处理
    //   }
    // });
  },
  closePopup(){
    this.setData({
      showSubmitPopup:false,
      submitPopupContent:'',
    })
  },
  onInputChange(e) {
    this.setData({
      inputContent: e.detail.value
    });
  },
});
