import { getInfoConfig } from '../../api/home';
import request from '../../utils/request'
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    logo: {
      type: String,
      value: ''
    },
    certData: {
      type: Object,
      value: null,
      observer: function(newVal, oldVal) {
        if (newVal) {
          this.validateCertData(newVal);
        }
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isValidCert: false,
    enterpriseLogo:'',
    saving: false, // 是否正在保存图片
    showTips: false, // 是否显示提示信息
    tipsMsg: '' // 提示信息内容
  },

  /**
   * 生命周期函数
   */
  lifetimes: {
    attached: function() {
      this.getInfo();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 获取配置信息
    async getInfo() {
      const result = await getInfoConfig();
      let imgURL=request.config.imgURL
      if (result) {
        this.setData({
          enterpriseLogo:  imgURL + result.enterpriseLogo
        });
      }
    },

    getDateFormat(dateStr) {
      if (!dateStr) return '';
      if (typeof dateStr === 'string' && dateStr.includes(' ')) {
        return dateStr.split(' ')[0];
      }
      return dateStr;
    },
    
    // 验证证书数据
    validateCertData: function(certData) {
      if (!certData) {
        this.setData({ isValidCert: false });
        return;
      }
      
      // 检查必要的证书字段是否存在
      const isValid = certData.title && (certData.date || certData.obtainTime);
      this.setData({ isValidCert: isValid });
      
      if (!isValid) {
        console.warn('证书数据不完整', certData);
      }
    },
    
    // 获取证书过期日期（如果未提供 validUntil，则默认为三年有效期）
    getExpiryDate: function(dateStr) {
      if (!dateStr) return '永久有效';
      
      try {
        const date = new Date(dateStr);
        const expiryDate = new Date(date);
        expiryDate.setFullYear(date.getFullYear() + 3);
        
        return expiryDate.getFullYear() + '-' + 
               this.padZero(expiryDate.getMonth() + 1) + '-' + 
               this.padZero(expiryDate.getDate());
      } catch (e) {
        console.error('计算证书过期日期错误:', e);
        return '永久有效';
      }
    },
    
    // 补零函数
    padZero: function(num) {
      return num < 10 ? '0' + num : num;
    },

    shareCert() {
      this.triggerEvent('share');
    },

    // 长按保存图片
    async handleLongPress() {
      if (this.data.saving) return;
      
      this.setData({ saving: true });
      try {
        // 检查是否有保存到相册的权限
        const { authSetting } = await wx.getSetting();
        if (authSetting['scope.writePhotosAlbum'] === undefined) {
          // 首次使用，需要请求授权
          await this.requestSaveAuth();
        } else if (authSetting['scope.writePhotosAlbum'] === false) {
          // 之前拒绝过授权，提示用户开启权限
          this.showAuthModal();
          this.setData({ saving: false });
          return;
        }
        
        // 开始生成并保存图片
        this.saveImage();
      } catch (error) {
        console.error('保存图片出错:', error);
        this.showTips('保存失败，请重试');
        this.setData({ saving: false });
      }
    },

    // 请求保存到相册的授权
    requestSaveAuth() {
      return new Promise((resolve, reject) => {
        wx.authorize({
          scope: 'scope.writePhotosAlbum',
          success: resolve,
          fail: reject
        });
      });
    },

    // 显示授权提示弹窗
    showAuthModal() {
      wx.showModal({
        title: '提示',
        content: '需要您授权保存图片到相册',
        confirmText: '去授权',
        success: (res) => {
          if (res.confirm) {
            wx.openSetting({
              success: (settingRes) => {
                if (settingRes.authSetting['scope.writePhotosAlbum']) {
                  // 用户在设置页面开启了授权
                  this.showTips('授权成功，请重新长按保存');
                }
              }
            });
          }
        }
      });
    },
    
    // 保存图片
    saveImage() {
      const that = this;
      
      // 首先获取canvas上下文
      const query = wx.createSelectorQuery().in(this);
      query.select('#certCanvas')
        .fields({ node: true, size: true })
        .exec(async (res) => {
          if (!res[0] || !res[0].node) {
            that.showTips('生成图片失败');
            that.setData({ saving: false });
            return;
          }

          try {
            const canvas = res[0].node;
            const ctx = canvas.getContext('2d');
            
            // 设置canvas的尺寸
            canvas.width = 750;  // 根据实际需要调整
            canvas.height = 1200; // 根据实际需要调整
            
            // 绘制白色背景
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制证书边框
            ctx.strokeStyle = '#D4AF37';
            ctx.lineWidth = 4;
            ctx.strokeRect(24, 24, canvas.width - 48, canvas.height - 48);
            
            // 绘制角落装饰
            that.drawCorner(ctx, 20, 20, 80, 80, 'top-left');
            that.drawCorner(ctx, canvas.width - 20, 20, 80, 80, 'top-right');
            that.drawCorner(ctx, 20, canvas.height - 20, 80, 80, 'bottom-left');
            that.drawCorner(ctx, canvas.width - 20, canvas.height - 20, 80, 80, 'bottom-right');
            

             // 绘制logo
             if (that.data.logo) {
              try {
                await that.drawImageOnCanvas(ctx, canvas, that.data.logo, canvas.width/2, 100, 120, 120);
              } catch (error) {
                console.error('绘制logo失败', error);
              }
            }

            // 绘制标题
            ctx.fillStyle = '#1B3F8F';
            ctx.font = 'bold 48px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('餐考认证', canvas.width / 2, 180);
            
            // 绘制副标题
            ctx.fillStyle = '#D4AF37';
            ctx.font = '24px sans-serif';
            ctx.fillText('CERTIFICATION', canvas.width / 2, 220);
            
            // 绘制课程名
            const certName = that.data.certData.certificateName || '证书课程';
            ctx.fillStyle = '#1B3F8F';
            ctx.font = 'bold 30px sans-serif';
            ctx.fillText(`《${certName}》`, canvas.width / 2, 300);
            
            // 绘制级别
            const position = that.data.certData.positionNameInfo ? that.data.certData.positionNameInfo.name : '';
            const level = that.data.certData.positionLevelInfo ? that.data.certData.positionLevelInfo.name : '';
            if (position && level) {
              ctx.fillText(`${position}（${level}级）`, canvas.width / 2, 350);
            }
            
            // 绘制分隔线
            ctx.strokeStyle = '#D4AF37';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(150, 400);
            ctx.lineTo(canvas.width - 150, 400);
            ctx.stroke();
            
            // 绘制姓名
            const name = that.data.certData.employeeName || '学员姓名';
            ctx.fillStyle = '#1B3F8F';
            ctx.font = 'bold 32px sans-serif';
            ctx.fillText(name, canvas.width / 2, 480);
            
            // 绘制证书内容
            ctx.font = '24px sans-serif';
            ctx.fillText(`已完成《${certName}》课程学习，`, canvas.width / 2, 540);
            ctx.fillText('考核成绩合格，特发此证。', canvas.width / 2, 580);
            
            // 绘制分隔线
            ctx.beginPath();
            ctx.moveTo(150, 650);
            ctx.lineTo(canvas.width - 150, 650);
            ctx.stroke();
            
            // 绘制颁发机构
            const org = that.data.certData.organization || '餐烤餐考培训中心';
            ctx.textAlign = 'left';
            ctx.fillStyle = '#1B3F8F';
            ctx.font = 'bold 24px sans-serif';
            ctx.fillText(org, 100, 700);
            
            // 绘制日期
            ctx.font = '20px sans-serif';
            ctx.fillStyle = '#D4AF37';
            const obtainDate = that.data.certData.obtainTime ? 
              that.data.certData.obtainTime.split(' ')[0] : '未知日期';
            const validDate = that.data.certData.validUntil ? 
              that.data.certData.validUntil.split(' ')[0] : '永久有效';
            
            ctx.fillText(`颁发日期：${obtainDate}`, 100, 750);
            ctx.fillText(`有效期至：${validDate}`, 100, 790);
            
            // 绘制印章
            if (that.data.logo) {
              try {
                await that.drawImageOnCanvas(ctx, canvas, that.data.logo, canvas.width - 200, 720, 120, 120);
              } catch (error) {
                console.error('绘制印章失败', error);
              }
            }
            
            // 绘制证书编号
            ctx.fillStyle = '#D4AF37';
            ctx.font = '18px sans-serif';
            ctx.textAlign = 'left';
            ctx.fillText(`证书编号：${that.data.certData.certificateNo || '未知'}`, 100, 860);
            
            // 将Canvas导出为图片
            wx.canvasToTempFilePath({
              x: 0,
              y: 0,
              width: canvas.width,
              height: canvas.height,
              destWidth: canvas.width * 2,  // 提高分辨率
              destHeight: canvas.height * 2,
              canvas: canvas,
              success: function(res) {
                // 将图片保存到相册
                wx.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: function() {
                    that.showTips('证书已保存到相册');
                  },
                  fail: function(err) {
                    console.error('保存图片到相册失败', err);
                    that.showTips('保存失败，请重试');
                  },
                  complete: function() {
                    that.setData({ saving: false });
                  }
                });
              },
              fail: function(err) {
                console.error('Canvas导出图片失败', err);
                that.showTips('生成证书图片失败');
                that.setData({ saving: false });
              }
            });
          } catch (error) {
            console.error('绘制证书失败', error);
            that.showTips('生成证书图片失败');
            that.setData({ saving: false });
          }
        });
    },
    
    // 绘制图片到Canvas上
    drawImageOnCanvas(ctx, canvas, url, x, y, width, height) {
      return new Promise((resolve, reject) => {
        const image = canvas.createImage();
        image.onload = () => {
          ctx.drawImage(image, x, y, width, height);
          resolve();
        };
        image.onerror = (err) => {
          console.error('加载图片失败:', url, err);
          reject(err);
        };
        image.src = url;
      });
    },
    
    // 绘制角落装饰
    drawCorner(ctx, x, y, width, height, position) {
      ctx.strokeStyle = '#D4AF37';
      ctx.lineWidth = 4;
      
      ctx.beginPath();
      
      if (position === 'top-left') {
        ctx.moveTo(x, y + height / 2);
        ctx.lineTo(x, y);
        ctx.lineTo(x + width / 2, y);
      } else if (position === 'top-right') {
        ctx.moveTo(x - width / 2, y);
        ctx.lineTo(x, y);
        ctx.lineTo(x, y + height / 2);
      } else if (position === 'bottom-left') {
        ctx.moveTo(x, y - height / 2);
        ctx.lineTo(x, y);
        ctx.lineTo(x + width / 2, y);
      } else if (position === 'bottom-right') {
        ctx.moveTo(x - width / 2, y);
        ctx.lineTo(x, y);
        ctx.lineTo(x, y - height / 2);
      }
      
      ctx.stroke();
    },
    
    // 显示提示信息
    showTips(msg) {
      this.setData({
        showTips: true,
        tipsMsg: msg
      });
      
      // 3秒后自动隐藏提示
      setTimeout(() => {
        this.setData({
          showTips: false,
          tipsMsg: ''
        });
      }, 3000);
    }
  }
}) 