/* pages/profile/my-certificates.wxss */
.header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;

}

.filter-button {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  /* background: linear-gradient(135deg, rgba(161, 140, 209, 0.1) 0%, rgba(251, 194, 235, 0.1) 100%); */
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
  /* border: 2rpx dashed rgba(126, 87, 194, 0.3); */
  border: 2rpx dashed #fff;
  transition: all 0.3s;
  line-height: normal;
  height: 50rpx;
  width: auto;
  justify-content: center;
  padding:0 10rpx;
}

.filter-button::after {
  border: none;
}

.filter-button text {
  margin: 0 8rpx;
}

.filter-button .iconfont {
  font-size: 32rpx;
}

.summary-box {
  background: var( --primary-gradient);
  border-radius: 32rpx;
  padding: 24rpx;
  margin: 20rpx 32rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(161, 140, 209, 0.3);
}

.summary-box::before {
  content: '';
  position: absolute;
  top: -40rpx;
  right: -40rpx;
  width: 240rpx;
  height: 240rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.summary-box::after {
  content: '';
  position: absolute;
  bottom: -60rpx;
  left: -60rpx;
  width: 300rpx;
  height: 300rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
}

.cert-count {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 12rpx 24rpx;
  margin-bottom: 20rpx;
}

.cert-number {
  font-size: 56rpx;
  font-weight: bold;
  margin-right: 24rpx;
  text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.cert-text {
  display: flex;
  flex-direction: column;
}

.cert-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  margin-top: 4rpx;
}

.promotion-tip {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 24rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.promotion-icon {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.promotion-text {
  flex: 1;
}

.promotion-title {
  font-size: 28rpx;
}

.promotion-subtitle {
  font-size: 24rpx;
  margin-top: 4rpx;
}

.cert-highlight {
  font-weight: bold;
  color: #ffeb3b;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.growth-path {
  padding: 20rpx;
  position: relative;
}

.path-track {
  height: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6rpx;
  position: relative;
  margin: 50rpx 0;
}

.path-track-line {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0%; /* 默认宽度，会被JS动态覆盖 */
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.9), rgba(255, 235, 59, 0.9));
  border-radius: 6rpx;
  box-shadow: 0 0 24rpx rgba(255, 235, 59, 0.4);
  transition: width 0.8s ease-in-out; /* 添加平滑过渡效果 */
}

.path-nodes {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-19%);
  display: flex;
  justify-content: space-between;
}

.path-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: translateY(-50%);
}

.node-dot {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.node-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.path-node.active .node-dot {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.5);
}

.path-node.active .node-label {
  color: white;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.15);
}

.path-node.current .node-dot {
  background: #ffeb3b;
  box-shadow: 0 0 30rpx rgba(255, 235, 59, 0.6);
  border-color: rgba(255, 255, 255, 0.9);
}

.path-node.current .node-label {
  color: #ffeb3b;
  font-weight: 700;
  background: rgba(255, 235, 59, 0.2);
}

.path-label {
  text-align: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 48rpx;
  position: relative;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);
  padding: 10rpx 28rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-section {
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-container {
  flex: 1;
  background: #fff;
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.icon-search {
  font-size: 32rpx;
  color: #999;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.filter-btn {
  width: 80rpx;
  height: 80rpx;
  background: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.icon-filter {
  font-size: 36rpx;
  color: #666;
}

.certificates-container {
  padding: 20rpx 32rpx;
  /* height: calc(100vh - 380rpx); */
  box-sizing: border-box;
  width: 100%;
  overflow: auto;
}

/* 确保scroll-view占满容器 */
.certificates-container scroll-view {
  height: 100% !important;
  width: 100%;
}

.level-section {
  margin-bottom: 32rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  opacity: 0;
  transform: translateY(30rpx);
  animation: fadeInUp 0.5s forwards;
  border: 1px solid rgba(161, 140, 209, 0.1);
  transition: all 0.3s ease;
}

.level-section:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.level-section:nth-child(1) { animation-delay: 0.1s; }
.level-section:nth-child(2) { animation-delay: 0.2s; }
.level-section:nth-child(3) { animation-delay: 0.3s; }
.level-section:nth-child(4) { animation-delay: 0.4s; }
.level-section:nth-child(5) { animation-delay: 0.5s; }
.level-section:nth-child(6) { animation-delay: 0.6s; }

.level-section-header {
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.18) 0%, rgba(251, 194, 235, 0.18) 100%);
  padding: 28rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

.level-section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 12rpx;
  height: 100%;
  background: var( --primary-gradient);
}

.level-section-title {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.level-big-badge {
  background: var( --primary-gradient);
  color: white;
  font-weight: bold;
  font-size: 30rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(161, 140, 209, 0.2);
}

.level-title-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  padding-left: 20rpx;
  position: relative;
}

.level-title-text::after {
  content: "";
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  background-color: #a18cd1;
  border-radius: 50%;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.level-cert-count {
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 40rpx;
  border: 1rpx solid rgba(161, 140, 209, 0.1);
}

.level-description {
  padding: 24rpx 32rpx;
  font-size: 26rpx;
  color: #666;
  background-color: rgba(250, 248, 255, 0.6);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
  line-height: 1.8;
}

.level-description view {
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.level-description view:last-child {
  margin-bottom: 0;
}

.level-description view::before {
  content: "•";
  color: #a18cd1;
  font-weight: bold;
  margin-right: 10rpx;
}

.exam-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #f9f9f9;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  color: #6539dd;
  font-size: 28rpx;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.exam-footer::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #a18cd1 0%, #fbc2eb 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  transform-origin: left;
}

.level-section:hover .exam-footer::after {
  transform: scaleX(1);
}

.exam-footer .icon-right {
  margin-left: 8rpx;
  font-size: 24rpx;
  transition: transform 0.3s ease;
}

.level-section:active .exam-footer .icon-right {
  transform: translateX(5rpx);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}

.loading-state .iconfont {
  font-size: 64rpx;
  margin-bottom: 24rpx;
  color: #a18cd1;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 禁止页面滚动 */
.no-scroll {
  height: 100vh;
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* 证书详情弹窗遮罩 */
.cert-detail-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9998;
}

/* 证书详情弹窗 */
.cert-detail-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.cert-detail-popup.show {
  opacity: 1;
  visibility: visible;
}

.cert-container {
  width: 90%;
  /* height: 92vh; */
  background-color: #fff;
  border-radius: 16rpx;
  /* overflow: auto; */
  /* display: flex;
  flex-direction: column; */
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
  display: relative;
  position:relative;
}


.cert-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
  position:absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  box-sizing: border-box;
  z-index: 100;
}

.cert-detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.cert-detail-header .icon-close {
  font-size: 40rpx;
  color: #999;
}

.cert-detail-popup-content{
  margin-top: 100rpx;
  /* overflow: auto; */
  /* height:calc(100% - 100rpx); */
}
.certificateUrl{
  width:100%;
  cursor: pointer;
  transition: transform 0.2s ease;
  position: relative;
}

/* 长按提示效果 */
.certificateUrl:active {
  transform: scale(0.98);
}

/* 添加一个提示文本的容器 */
.cert-detail-popup-content {
  position: relative;
}

.cert-detail-popup-content::after {
  content: "长按保存图片";
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  opacity: 0.8;
  pointer-events: none;
  z-index: 10;
}

.filter-drawer {
  right: 0;
  left: auto;
  transform: translateX(100%);
}


.filter-section {
  padding: 20rpx 32rpx;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.selector-item {
  color: #666;
}

.selector-item-active {
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.15) 0%, rgba(251, 194, 235, 0.15) 100%);
  color: #7e57c2;
  font-weight: 600;
}

.drawer-footer {
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f0f0;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
}

.reset-btn, .apply-btn {
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.apply-btn {
  background: var( --primary-gradient);
  color: #fff;
  font-weight: 500;
}

.cert-ribbon {
  position: absolute;
  top: 0;
  left: 0;
  width: 12rpx;
  height: 100%;
  background: var( --primary-gradient);
}
.cert-type-tag {
  background: rgba(161, 140, 209, 0.15);
  color: #6539dd;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
  border: 1rpx solid rgba(161, 140, 209, 0.2);
}

.cert-time-info, .cert-expire-info {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.cert-time-info:last-child, .cert-expire-info:last-child {
  margin-bottom: 0;
}

.cert-time-info::before, .cert-expire-info::before {
  display: none;
}

.cert-time-info .iconfont, .cert-expire-info .iconfont {
  font-size: 28rpx;
  color: #a18cd1;
  margin-right: 10rpx;
}

