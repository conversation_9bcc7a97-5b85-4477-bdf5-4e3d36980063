// pages/profile/badges.js
import { getBadgeList, getBadgeCategories, getBadgeDetail, getBadgeStats } from '../../../api/badge'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    currentTab: 'all',
    tabs: [
      { label: '全部', value: 'all' },
      { label: '已获得', value: 'earned' },
      { label: '未解锁', value: 'locked' },
    ],
    badgeCategories: [],
    currentBadge: null,
    isRefreshing: false,
    loading: false,
    showBadgeDetailPopup: false,
    badgeStats: {
      totalEarned: 0,
      unlockPercentage: '0%',
      highestLevel: ''
    },
    statsArray: [],
    filteredBadges: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // this.loadData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果需要在每次页面显示时刷新数据，可以在这里调用loadData
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.onRefresh().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 计算属性的替代方法，在数据变化时更新
   */
  updateComputedData() {
    // 更新统计数组
    const statsArray = [
      { label: '获得徽章', value: this.data.badgeStats.totalEarned.toString() },
      { label: '解锁比例', value: this.data.badgeStats.unlockPercentage },
      { label: '最高级别', value: this.data.badgeStats.highestLevel }
    ];
    
    // 过滤徽章分类
    let filteredBadges = this.data.badgeCategories;
    
    if (this.data.currentTab !== 'all') {
      // 过滤分类，每个分类也需要过滤内容
      const result = [];
      
      this.data.badgeCategories.forEach(category => {
        const filteredBadges = category.badges.filter(badge => {
          return this.data.currentTab === 'earned' ? badge.unlocked : !badge.unlocked;
        });
        
        if (filteredBadges.length > 0) {
          result.push({
            ...category,
            badges: filteredBadges
          });
        }
      });
      
      filteredBadges = result;
    }
    
    this.setData({
      statsArray,
      filteredBadges
    });
  },

  async loadData() {
    try {
      await Promise.all([
        this.fetchBadgeStats(),
        this.fetchBadgeCategories()
      ]);
    } catch (error) {
      console.error('加载数据失败：', error);
      wx.showToast({
        title: '加载数据失败，请重试',
        icon: 'none'
      });
    }
  },
  
  // 获取徽章统计数据
  async fetchBadgeStats() {
    try {
      const result = await getBadgeStats();
      if (result) {
        this.setData({
          badgeStats: result
        });
        this.updateComputedData();
      }
    } catch (error) {
      console.error('获取徽章统计失败：', error);
    }
  },
  
  // 获取徽章分类及徽章列表
  async fetchBadgeCategories() {
    this.setData({
      loading: true
    });
    
    try {
      const result = await getBadgeCategories();
      if (result) {
        this.setData({
          badgeCategories: result
        });
        this.updateComputedData();
      }
    } catch (error) {
      console.error('获取徽章分类失败：', error);
      wx.showToast({
        title: '获取徽章分类失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        isRefreshing: false
      });
    }
  },
  
  // 获取徽章详情
  async showBadgeDetail(e) {
    const badge = e.currentTarget.dataset.badge;
    
    if (badge.description) {
      this.setData({
        currentBadge: badge,
        showBadgeDetailPopup: true
      });
    } else {
      try {
        wx.showLoading({ title: '加载中...' });
        const result = await getBadgeDetail(badge.id);
        
        if (result) {
          // 使用返回的详情数据更新徽章信息
          this.setData({
            currentBadge: result,
            showBadgeDetailPopup: true
          });
          
          // 在分类中更新这个徽章的详细信息，以便下次不需要再请求
          const updatedCategories = this.data.badgeCategories.map(category => {
            const updatedBadges = category.badges.map(b => {
              if (b.id === badge.id) {
                return { ...b, ...result };
              }
              return b;
            });
            return { ...category, badges: updatedBadges };
          });
          
          this.setData({
            badgeCategories: updatedCategories
          });
          this.updateComputedData();
        }
      } catch (error) {
        console.error('获取徽章详情失败：', error);
        wx.showToast({
          title: '获取徽章详情失败',
          icon: 'none'
        });
      } finally {
        wx.hideLoading();
      }
    }
  },
  
  closeBadgeDetail() {
    this.setData({
      showBadgeDetailPopup: false
    });
  },
  
  handleTabChange(e) {
    const { value } = e.detail;
    this.setData({
      currentTab: this.data.tabs[value].value
    });
    this.updateComputedData();
  },
  
  loadMore() {
    // 如果有分页加载需求可以在这里实现
  },
  
  async onRefresh() {
    this.setData({
      isRefreshing: true
    });
    await this.loadData();
  }
})