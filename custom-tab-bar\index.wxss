.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  display: flex;
  flex-direction: column;
  z-index: 999;
  box-shadow: 0 -2rpx 10rpx 0 rgba(0, 0, 0, 0.05);
  /* height: 120rpx; */
  /* 基础高度 */
  box-sizing: border-box;
  /* 增加对全面屏的兼容 */
  /* padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);  */
}

.tab-bar-border {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1rpx;
  background: #f0f0f0;
}

.tab-bar-content {
  display: flex;
  width: 100%;
  margin: 20rpx 0;
}

.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.icon-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 56rpx;
}
.icon-wrapper .iconfont{
  font-size: 48rpx;
  color: #696969;
}
.icon-wrapper .iconfont.selected{
  color: #a18cd1;
}

.icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 4rpx;
}

.text-wrapper {
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-bar-item-text {
  font-size: 24rpx;
  color: #999999;
}

.tab-bar-item.active .tab-bar-item-text {
  color: #a18cd1;
  font-weight: 500;
}

/* 安全区域占位区块 */
.safe-area-block {
  width: 100%;
  background: white;
  border: none;
  padding: 0;
  margin: 0;
}