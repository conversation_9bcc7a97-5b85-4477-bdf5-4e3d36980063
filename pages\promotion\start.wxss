.container {
  /* min-height: 100vh; */
  background-color: #f8f9fa;
  padding-bottom: 180rpx;
}

/* 考试横幅 */
.promotion-banner {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  margin: 30rpx;
  padding: 40rpx;
  border-radius: 32rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
}

.promotion-banner::before,
.promotion-banner::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.promotion-banner::before {
  width: 240rpx;
  height: 240rpx;
  bottom: -30rpx;
  right: -30rpx;
}

.promotion-banner::after {
  width: 300rpx;
  height: 300rpx;
  top: -60rpx;
  left: -60rpx;
}

.level-badge {
  display: inline-block;
  padding: 8rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  font-size: 24rpx;
  margin-bottom: 16rpx;
}

.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.banner-desc {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

/* 考试信息 */
.exam-info {
  margin: 30rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  align-items: center;
  padding-bottom: 30rpx;
  margin-bottom: 30rpx;
  border-bottom: 2rpx dashed rgba(0, 0, 0, 0.1);
}

.info-item:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}

.info-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.2) 0%, rgba(251, 194, 235, 0.2) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.info-icon .iconfont {
  color: #a18cd1;
  font-size: 36rpx;
}

.info-content {
  flex: 1;
}

.info-title {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.info-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
}

/* 考试规则 */
.rule-card {
  margin: 30rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.rule-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.rule-title .iconfont {
  margin-right: 16rpx;
  color: #a18cd1;
  font-size: 36rpx;
}

.rule-list {
  padding: 10rpx 0;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.rule-item:last-child {
  margin-bottom: 0;
}

.rule-item .iconfont {
  color: #a18cd1;
  font-size: 28rpx;
  margin-right: 20rpx;
  margin-top: 8rpx;
}

.rule-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 开始按钮 */
.start-button {
  position: fixed;
  left: 30rpx;
  right: 30rpx;
  bottom: 60rpx;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  background: linear-gradient(90deg, #a18cd1, #fbc2eb);
  border-radius: 50rpx;
  box-shadow: 0 8rpx 30rpx rgba(161, 140, 209, 0.3);
}

.start-button .iconfont {
  margin-right: 16rpx;
} 