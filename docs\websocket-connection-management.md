# WebSocket 连接管理优化

## 🎯 优化目标
- 离开页面时自动关闭 WebSocket 连接
- 避免多个连接同时存在
- 防止资源泄漏和内存占用

## 🔧 实现方案

### 1. 页面生命周期管理

#### onShow() - 页面显示时
```javascript
onShow() {
  console.log('📱 页面显示，检查 WebSocket 连接状态');
  
  // 如果 WebSocket 连接不存在或已断开，重新初始化
  if (!this.data.streamingSocket || !this.data.streamingSocket.isConnected) {
    console.log('🔄 重新初始化 WebSocket 连接');
    this.initStreamingConnection();
  }
}
```

#### onHide() - 页面隐藏时
```javascript
onHide() {
  console.log('📱 页面隐藏，清理 WebSocket 连接');
  this.cleanupStreamingConnection();
}
```

#### onUnload() - 页面卸载时
```javascript
onUnload() {
  console.log('🚪 页面卸载，清理所有资源');
  
  // 清理流式连接
  this.cleanupStreamingConnection();
  
  // 停止所有定时器
  if (this.data.recordTimer) {
    clearInterval(this.data.recordTimer);
  }
  if (this.data.countdownTimer) {
    clearInterval(this.data.countdownTimer);
  }
  
  // 停止语音播放
  if (typeof this.stopSpeech === 'function') {
    this.stopSpeech();
  }
}
```

### 2. 连接清理方法

#### cleanupStreamingConnection()
```javascript
cleanupStreamingConnection() {
  console.log('🧹 清理流式连接');
  
  // 停止打字机效果
  if (this.data.typewriterEffect) {
    this.data.typewriterEffect.stop();
    console.log('⏹️ 打字机效果已停止');
  }

  // 关闭 WebSocket 连接
  if (this.data.streamingSocket) {
    this.data.streamingSocket.close();
    console.log('🔌 WebSocket 连接已关闭');
  }

  // 清理状态
  this.setData({
    streamingSocket: null,
    typewriterEffect: null,
    isStreaming: false,
    streamingMessageIndex: -1
  });
  
  console.log('✅ 流式连接清理完成');
}
```

## 📊 连接生命周期

### 正常使用流程
```
页面加载 (onLoad)
    ↓
初始化 WebSocket (initStreamingConnection)
    ↓
用户正常使用
    ↓
页面隐藏 (onHide) → 清理连接 ✅
    ↓
页面显示 (onShow) → 重新初始化连接 ✅
    ↓
页面卸载 (onUnload) → 彻底清理所有资源 ✅
```

### 多页面切换场景
```
练习页面 A
    ↓
切换到其他页面 → 清理 A 的 WebSocket ✅
    ↓
返回练习页面 A → 重新初始化 WebSocket ✅
    ↓
切换到练习页面 B → 清理 A 的 WebSocket ✅
    ↓
B 页面初始化自己的 WebSocket ✅
```

## 🛡️ 防护机制

### 1. 避免重复连接
- 页面显示时检查连接状态
- 只在连接不存在或已断开时才重新初始化
- 避免创建多个并发连接

### 2. 完整资源清理
- WebSocket 连接关闭
- 打字机效果停止
- 定时器清理
- 语音播放停止
- 状态重置

### 3. 错误处理
- 连接失败时的清理
- 异常情况下的资源释放
- 防止内存泄漏

## 🔍 调试日志

### 正常流程日志
```
📱 页面显示，检查 WebSocket 连接状态
🔄 重新初始化 WebSocket 连接
初始化流式连接
设置 WebSocket 事件监听器
✅ 流式连接初始化完成

... 用户使用过程 ...

📱 页面隐藏，清理 WebSocket 连接
🧹 清理流式连接
⏹️ 打字机效果已停止
🔌 WebSocket 连接已关闭
✅ 流式连接清理完成
```

### 页面卸载日志
```
🚪 页面卸载，清理所有资源
🧹 清理流式连接
⏹️ 打字机效果已停止
🔌 WebSocket 连接已关闭
✅ 流式连接清理完成
✅ 页面资源清理完成
```

## ✅ 优化效果

### 1. 资源管理
- **无连接泄漏**：每次离开页面都会关闭连接
- **内存优化**：及时释放不需要的资源
- **性能提升**：避免多个连接同时存在

### 2. 用户体验
- **快速响应**：页面显示时自动重新连接
- **无感知切换**：用户切换页面时体验流畅
- **稳定性提升**：减少连接冲突和异常

### 3. 系统稳定性
- **防止资源耗尽**：避免大量无用连接
- **减少服务器压力**：及时释放服务器端资源
- **提高可靠性**：完善的错误处理机制

## 🚀 使用场景

### 1. 单页面使用
- 进入页面 → 自动初始化连接
- 正常使用 → 连接保持活跃
- 离开页面 → 自动清理连接

### 2. 多页面切换
- 在多个练习页面间切换
- 每个页面都有独立的连接管理
- 不会相互干扰

### 3. 后台切换
- 小程序切换到后台 → 清理连接
- 重新进入小程序 → 自动重连
- 保证连接的有效性

## 📞 注意事项

1. **连接检查**：页面显示时会检查连接状态
2. **自动重连**：断开的连接会自动重新建立
3. **资源清理**：离开页面时会彻底清理资源
4. **状态同步**：连接状态与页面状态保持同步

这样的设计确保了 WebSocket 连接的正确管理，避免了资源泄漏和多连接问题。
