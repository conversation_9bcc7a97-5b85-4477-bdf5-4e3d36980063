/* pages/profile/join-enterprise/join-enterprise.wxss */

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 0 0 40rpx 0;
}

.form-container {
  padding: 40rpx 30rpx;
}

/* 页面说明 */
.page-intro {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.intro-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 表单内容 */
.form-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 表单组 */
.form-group {
  margin-bottom: 40rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  flex: 1;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  transition: border-color 0.3s;
}

.form-input:focus {
  border-color: #a18cd1;
}

.form-input::placeholder {
  color: #999;
}

.loading-icon {
  position: absolute;
  right: 24rpx;
  color: #a18cd1;
}

.loading-text {
  font-size: 24rpx;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* 显示框样式 */
.form-display {
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #f8f9fa;
  display: flex;
  align-items: center;
}

.display-text {
  font-size: 28rpx;
  color: #333;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

/* 选择器样式 */
.form-picker {
  display: block;
}

.picker-content {
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: border-color 0.3s;
}

.picker-content:active {
  border-color: #a18cd1;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-placeholder {
  font-size: 28rpx;
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  transform: rotate(90deg);
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #a18cd1 0%, #8b7bc7 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.submit-button:not([disabled]):active {
  transform: scale(0.98);
  opacity: 0.9;
}

.submit-button[disabled] {
  background: #e8e8e8;
  color: #999;
  transform: none;
}

/* 温馨提示 */
.tips-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tips-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.tips-title .iconfont {
  font-size: 32rpx;
  color: #a18cd1;
  margin-right: 12rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .form-container {
    padding: 30rpx 20rpx;
  }
  
  .form-content {
    padding: 30rpx 20rpx;
  }
  
  .form-label {
    font-size: 26rpx;
  }
  
  .form-input {
    font-size: 26rpx;
  }
}
