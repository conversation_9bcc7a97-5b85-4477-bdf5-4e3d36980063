# 练习模块语音聊天功能需求文档

## 1. 功能概述

### 1.1 需求背景
在餐饮培训教育领域，传统的文本练习方式往往难以模拟真实场景下的互动问答，难以满足实际应用中的技能训练需求。通过引入AI大模型驱动的语音聊天功能，可以为用户提供更接近实际工作场景的练习体验，提高学习效果。

### 1.2 功能描述
在练习模块中，实现基于AI大模型的智能语音聊天功能，支持文字输入和语音输入两种交互方式。系统会通过调用Dify工作流获取练习题目，AI以语音和文字形式提出问题，用户可通过语音或文字作答，系统会自动评估用户回答并给出反馈。整个过程以对话形式进行，实现一问一答的练习模式。

### 1.3 功能目标
- 提供更接近实际工作的交互式练习体验
- 支持多种输入方式（语音/文字），满足不同场景需要
- 通过AI实时评估用户回答，提供即时反馈
- 增强用户培训过程的趣味性和互动性
- 通过语音交互降低用户输入门槛

## 2. 详细需求

### 2.1 聊天界面设计
- 整体采用类似聊天软件的界面布局，消息气泡形式展示对话内容
- 界面主要分为三部分：顶部导航栏、中部消息展示区、底部输入控制区
- 消息区域需要区分AI消息和用户消息，采用不同的气泡样式和位置
- 支持消息滚动查看历史记录，并在有新消息时自动滚动到最新位置
- 支持下拉加载更多历史消息

### 2.2 语音交互功能
- AI消息支持自动语音播放，使用系统TTS引擎
- 提供语音模式切换按钮，可在语音输入和文字输入间切换
- 语音录制时提供醒目的视觉反馈，包括录音波形动画
- 支持按住说话、松开发送的语音输入模式
- 支持上滑取消发送的手势操作
- 支持语音识别结果的文字展示和纠错机会

### 2.3 AI交互流程
- **出题工作流**：
  - 用户进入练习页面时，调用Dify出题工作流获取单个练习题和标准答案
  - 系统以AI消息形式展示题目，同时播放语音
  - 将题目和标准答案缓存在本地，用于后续评估

- **练习工作流**：
  - 用户通过语音或文字提交答案
  - 系统将题目、标准答案和用户答案一起提交给Dify练习工作流
  - 工作流返回评估结果和反馈
  - 系统以AI消息形式展示反馈内容，同时播放语音
  - 完成一轮问答后，自动进入下一轮出题流程

### 2.4 消息类型
- 系统消息：练习开始、结束等提示性内容
- AI消息：包括题目、反馈等由AI生成的内容
- 用户消息：用户通过语音或文字输入的回答内容

### 2.5 状态管理
- 记录当前练习进度，包括已完成题目数、正确率等
- 维护消息历史记录，支持页面重新加载后恢复对话
- 管理语音播放状态，包括播放、暂停、停止等
- 管理录音状态，包括录音中、暂停、取消等

## 3. 技术实现

### 3.1 Dify工作流集成
- dify baseurl
  http://dify-upgrade.local.xmsxb.com/v1
    Authorization: Bearer app-984Dk017ZgStE9gABV5gVnYW

- **出题工作流接口**
  

- **练习工作流接口**
 

### 3.2 语音功能实现
- 录音功能使用微信小程序原生录音API：`wx.getRecorderManager()`
- 语音识别使用腾讯云语音识别API（ASR） 参考文档：https://cloud.tencent.com/document/product/1093/48982
- 语音合成使用腾讯云流式文本语音合成API（TTS） 参考文档：https://cloud.tencent.com/document/product/1073/108595
- 语音播放使用微信小程序原生API：`wx.createInnerAudioContext()`
- 集成腾讯云SDK，进行配置和鉴权

### 3.3 UI组件实现
```javascript
// 语音波形动画组件示例
Component({
  properties: {
    isRecording: {
      type: Boolean,
      value: false
    },
    volume: {
      type: Number,
      value: 0
    }
  },
  // 实现动态波形效果
})
```

## 4. 界面设计

### 4.1 聊天界面布局
- 符合项目整体设计风格
- 整体采用白色背景，提供干净清爽的视觉体验
- AI消息气泡靠左
- 用户消息气泡靠右
- 底部输入区固定在屏幕底部，支持键盘弹出时自动调整位置
- 消息内容区域支持自动换行，字体大小适中易读

### 4.2 语音交互视觉反馈
- 录音按钮在按下状态下有明显的视觉变化
- 录音过程中显示动态波形动画，反映音量大小
- 录音取消操作有清晰的手势指引提示
- 录音时间有可视化计时器

## 5. 注意事项

### 5.1 用户体验考虑
- 确保语音识别的准确性，对于识别不准的情况提供修改机会
- 考虑网络不稳定情况下的体验，增加重试和恢复机制
- 语音播放应提供暂停/继续控制选项
- 针对不同网络环境优化语音数据大小
- 考虑隐私和安全问题，在录音前征得用户同意

### 5.2 技术实现注意点
- 处理微信小程序录音权限问题
- 申请和配置腾讯云语音服务的密钥
- 优化语音识别和合成的网络请求，增加超时和重试机制
- 使用临时文件存储和管理录音文件
- 考虑不同设备下的兼容性问题
- 优化语音数据传输和处理速度
- 实现消息防抖和节流，避免重复发送
- 添加音频缓存机制，减少流量消耗
- 处理语音服务调用次数限制和计费问题

## 6. 测试要点
- 测试不同网络环境下的语音识别和合成效果
- 测试长时间对话的内存占用和性能表现
- 测试各种用户输入情况，包括空输入、极长输入等边界条件
- 测试语音交互的用户体验流畅度
- 测试AI回复内容的准确性和相关性
- 测试在噪音环境下的语音识别效果

## 7. 上线计划
- 设计与开发周期：5人天
- UI实现与集成：3人天
- 测试与优化：2人天
- 预计上线时间：下一版本迭代 