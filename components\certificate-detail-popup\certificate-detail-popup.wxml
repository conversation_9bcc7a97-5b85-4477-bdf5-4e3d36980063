<!-- 证书详情弹框组件 -->
<view class="certificate-detail-popup">
  <!-- 遮罩层 -->
  <view class="cert-detail-popup-mask" wx:if="{{visible}}" bindtap="onMaskTap"></view>
  
  <!-- 弹框内容 -->
  <view class="cert-detail-popup {{visible ? 'show' : ''}}">
    <view class="cert-container">
      <!-- 头部 -->
      <view class="cert-detail-header">
        <text class="cert-detail-title">{{title || '证书详情'}}</text>
        <text class="iconfont icon-close" bindtap="onClose"></text>
      </view>
      
      <!-- 内容区域 -->
      <view class="cert-detail-popup-content">
        <!-- 证书图片 -->
        <image
          wx:if="{{certificateUrl}}"
          src="{{certificateUrl}}"
          mode="{{imageMode || 'widthFix'}}"
          class="certificate-image"
          bindlongpress="onImageLongPress"
          bindtap="onImageTap"
          binderror="onImageError"
          bindload="onImageLoad"
        />
        
        <!-- 加载状态 -->
        <view wx:if="{{!certificateUrl && loading}}" class="loading-container">
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 错误状态 -->
        <view wx:if="{{!certificateUrl && !loading && error}}" class="error-container">
          <text class="error-text">{{error || '图片加载失败'}}</text>
          <button class="retry-btn" bindtap="onRetry">重试</button>
        </view>
        
        <!-- 底部操作按钮 -->
      </view>
    </view>
  </view>
</view>
