<!-- components/input-area/input-area.wxml -->
<view class="input-area-wrapper" style="bottom: {{keyboardHeight}}px">
  <view class="input-container">
      <!-- 语音/文字切换按钮 -->
    <view class="input-type-switch" bindtap="onToggleInputType">
      <text class="iconfont {{isVoiceMode ? 'icon-jianpan' : 'icon-yuyin'}}"></text>
    </view>
    <!-- 文本输入框 -->
    <block wx:if="{{!isVoiceMode}}">
      <view class="input-box">
        <textarea
          class="input-textarea"
          value="{{inputContent}}"
          adjust-position="{{false}}"
          show-confirm-bar="{{true}}"
          confirm-type="send"
          cursor-spacing="20"
          maxlength="-1"
          bindfocus="onInputFocus"
          bindblur="onInputBlur"
          bindinput="onInput"
          bindconfirm="onSendMessage"
          placeholder="请输入回答内容..."
          disable-default-padding="{{true}}"
          always-embed="{{true}}"
        ></textarea>
      </view>
    </block>

    <!-- 语音输入按钮 -->
    <block wx:else>
      <view class="voice-input-box">
        <view 
          class="voice-button {{isRecording ? 'recording' : ''}}"
          bindtouchstart="onStartRecording"
          bindtouchend="onStopRecording"
          catchtouchmove="onTouchMove"
          bindtouchcancel="onCancelRecording"
        >
          <text>{{isRecording ? '松开发送' : '按住说话'}}</text>
        </view>
      </view>
    </block>
    
  
  </view>
</view> 