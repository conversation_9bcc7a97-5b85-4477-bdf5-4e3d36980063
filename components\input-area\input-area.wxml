<!-- components/input-area/input-area.wxml -->
<view class="input-area-wrapper" style="bottom: {{keyboardHeight}}px">
  <view class="input-container">
    <view class="input-left">
      <!-- 展开按钮 -->
        <view
          wx:if="{{showExpandButton}}"
          class="expand-button"
          bindtap="onExpandInput"
        >
          <text class="iconfont icon-down {{showFullscreenInput?'':'icon-up'}}"></text>
        </view>
          <!-- 语音/文字切换按钮 -->
        <view class="input-type-switch" bindtap="onToggleInputType">
          <text class="iconfont {{isVoiceMode ? 'icon-jianpan' : 'icon-yuyin'}}"></text>
        </view>
    </view>
    
    <!-- 文本输入框 -->
    <block wx:if="{{!isVoiceMode}}">
      <view class="input-box">
        <!-- {{showExpandButton ? 'limited-height' : ''}} -->
        <textarea
          class="input-textarea"
          style="height: {{textareaHeight}}rpx;"
          value="{{inputContent}}"
          adjust-position="{{false}}"
          show-confirm-bar="{{true}}"
          confirm-type="send"
          cursor-spacing="20"
          maxlength="-1"
          auto-height="{{autoHeight}}"
          bindfocus="onInputFocus"
          bindblur="onInputBlur"
          bindinput="onInput"
          bindconfirm="onSendMessage"
          placeholder="请输入回答内容..."
          disable-default-padding="{{true}}"
          always-embed="{{true}}"
        ></textarea>
      </view>
    </block>

    <!-- 语音输入按钮 -->
    <block wx:else>
      <view class="voice-input-box">
        <view 
          class="voice-button {{isRecording ? 'recording' : ''}}"
          bindtouchstart="onStartRecording"
          bindtouchend="onStopRecording"
          catchtouchmove="onTouchMove"
          bindtouchcancel="onCancelRecording"
        >
          <text>{{isRecording ? '松开发送' : '按住说话'}}</text>
        </view>
      </view>
    </block>
    

  </view>

  <!-- 全屏输入弹窗 -->
  <view class="fullscreen-input-modal {{showFullscreenInput ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="fullscreen-input-container">
      <!-- 顶部工具栏 -->
      <view class="fullscreen-header">
        <view class="header-left">
          <text class="iconfont icon-down" bindtap="onConfirmFullscreenInput"></text>
        </view>
        <!-- <view class="header-title">
          <text>输入内容</text>
        </view>
        <view class="header-right">
          <text class="confirm-btn" bindtap="onConfirmFullscreenInput">完成</text>
        </view> -->
      </view>

      <!-- 全屏输入框 -->
      <view class="fullscreen-input-content">
        <textarea
          class="fullscreen-textarea"
          value="{{fullscreenInputContent}}"
          placeholder="请输入回答内容..."
          auto-focus="{{showFullscreenInput}}"
          bindinput="onFullscreenInput"
          maxlength="-1"
          show-confirm-bar="{{false}}"
          disable-default-padding="{{true}}"
        ></textarea>
      </view>
    </view>
  </view>
</view>