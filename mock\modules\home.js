/**
 * 首页相关API的模拟数据
 */

// 轮播图数据
const banners = [
  {
    id: 1,
    imageUrl: '/static/images/banners/banner1.jpg',
    title: '新春特训营',
    subtitle: '厨艺精进，职场起航',
    linkType: 'page', // page, mini_program, web_view
    linkUrl: '/pages/training/camp-detail?id=10001',
    color: '#e74c3c',
    startTime: '2024-01-15',
    endTime: '2024-02-28'
  },
  {
    id: 2,
    imageUrl: '/static/images/banners/banner2.jpg',
    title: '大师讲堂',
    subtitle: '米其林三星主厨独家授课',
    linkType: 'page',
    linkUrl: '/pages/courses/master-class?id=30005',
    color: '#3498db',
    startTime: '2024-01-01',
    endTime: '2024-03-31'
  },
  {
    id: 3,
    imageUrl: '/static/images/banners/banner3.jpg',
    title: '餐厅管理实战',
    subtitle: '从厨师到店长的进阶之路',
    linkType: 'page',
    linkUrl: '/pages/courses/detail?id=20012',
    color: '#2ecc71',
    startTime: '2024-01-01',
    endTime: '2024-12-31'
  },
  {
    id: 4,
    imageUrl: '/static/images/banners/banner4.jpg',
    title: '新人专享福利',
    subtitle: '注册即送1000学习币',
    linkType: 'page',
    linkUrl: '/pages/profile/wallet',
    color: '#f39c12',
    startTime: '2024-01-01',
    endTime: '2024-12-31',
    forNewUser: true
  }
];

// 公告数据
const announcements = [
  {
    id: 1,
    title: '系统升级通知',
    content: '为提供更好的学习体验，平台将于2024年3月15日凌晨2:00-6:00进行系统升级维护，期间可能出现短暂不可用状态，请您合理安排学习时间。\n\n升级内容包括：\n1. 优化练习模块性能\n2. 新增语音识别功能\n3. 修复已知问题\n\n如有疑问，请联系客服。',
    publishTime: '2024-03-10 10:00:00',
    time: '03-10',
    isImportant: true,
    isRead: false,
    isNew: true
  },
  {
    id: 2,
    title: '2024年度认证考试计划已发布',
    content: '2024年度各级别认证考试时间安排已经公布，请有意参加考试的学员及时查看并提前准备。一季度考试将于3月开始报名。\n\n考试安排：\n• 初级认证：3月20日-3月25日\n• 中级认证：4月10日-4月15日\n• 高级认证：5月8日-5月12日\n\n报名方式：登录官网或小程序进行在线报名。',
    publishTime: '2024-02-25 14:30:00',
    time: '02-25',
    isImportant: true,
    isRead: true,
    isNew: false
  },
  {
    id: 3,
    title: '行业大师直播预告',
    content: '著名中餐大师王师傅将于本周六(3月18日)晚8点进行线上直播，分享宫廷菜制作秘诀，敬请期待！\n\n直播内容：\n• 宫廷菜历史文化背景\n• 经典菜品制作技巧\n• 现场答疑互动\n\n直播时间：3月18日 20:00-21:30\n观看方式：小程序内直播间',
    publishTime: '2024-03-13 09:15:00',
    time: '03-13',
    isImportant: false,
    isRead: false,
    isNew: true
  }
];

// 学习统计数据
const learningStats = {
  today: {
    minutes: 45,
    completedLessons: 3,
    earnedPoints: 120
  },
  week: {
    minutes: 320,
    completedLessons: 18,
    earnedPoints: 850
  },
  month: {
    minutes: 1480,
    completedLessons: 72,
    earnedPoints: 3650
  },
  total: {
    minutes: 8630,
    completedLessons: 453,
    earnedPoints: 22450,
    completedCourses: 12,
    certificates: 5,
    badges: 15
  },
  streak: {
    current: 12,
    longest: 30,
    thisWeek: 5,
    lastUpdate: '2024-03-14'
  }
};

// 练习分类
const courseCategories = [
  {
    id: 1,
    name: '烹饪技术',
    icon: '/static/icons/category-cooking.png',
    color: '#e74c3c',
    courseCount: 45
  },
  {
    id: 2,
    name: '餐厅管理',
    icon: '/static/icons/category-management.png',
    color: '#3498db',
    courseCount: 32
  },
  {
    id: 3,
    name: '服务技能',
    icon: '/static/icons/category-service.png',
    color: '#2ecc71',
    courseCount: 28
  },
  {
    id: 4,
    name: '食品安全',
    icon: '/static/icons/category-safety.png',
    color: '#f39c12',
    courseCount: 15
  },
  {
    id: 5,
    name: '菜品研发',
    icon: '/static/icons/category-innovation.png',
    color: '#9b59b6',
    courseCount: 20
  },
  {
    id: 6,
    name: '营养健康',
    icon: '/static/icons/category-nutrition.png',
    color: '#16a085',
    courseCount: 18
  }
];

// 推荐练习
const recommendedCourses = [
  {
    id: 10001,
    title: '中式烹饪基础入门',
    instructor: '李师傅',
    coverImage: '/static/images/courses/chinese-cooking-basics.jpg',
    rating: 4.8,
    studentCount: 12500,
    price: 0, // 免费
    level: '入门',
    duration: '10小时',
    isNew: false,
    isHot: true,
    isRecommended: true,
    progress: 35, // 百分比，用户学习进度
    lastLearnTime: '2024-03-13 20:15:30'
  },
  {
    id: 10002,
    title: '刀工技巧精进',
    instructor: '王大厨',
    coverImage: '/static/images/courses/knife-skills.jpg',
    rating: 4.9,
    studentCount: 9800,
    price: 4900, // 单位：分
    level: '进阶',
    duration: '8小时',
    isNew: false,
    isHot: true,
    isRecommended: true,
    tags: ['精品练习', '实操演示']
  },
  {
    id: 10003,
    title: '西餐料理基础',
    instructor: 'Jean Pierre',
    coverImage: '/static/images/courses/western-cooking.jpg',
    rating: 4.7,
    studentCount: 7560,
    price: 5900,
    level: '入门',
    duration: '12小时',
    isNew: true,
    isHot: false,
    isRecommended: true,
    tags: ['新课上线', '中英双语']
  },
  {
    id: 10004,
    title: '餐厅成本控制与管理',
    instructor: '赵经理',
    coverImage: '/static/images/courses/restaurant-cost-management.jpg',
    rating: 4.6,
    studentCount: 5320,
    price: 6900,
    level: '高级',
    duration: '15小时',
    isNew: false,
    isHot: false,
    isRecommended: true,
    tags: ['热门练习', '案例分析']
  }
];

// 学习路径
const learningPaths = [
  {
    id: 1,
    title: '初级厨师培养计划',
    description: '从零开始，系统掌握厨师必备的基础知识和技能',
    coverImage: '/static/images/paths/junior-chef.jpg',
    totalCourses: 8,
    totalHours: 60,
    completed: 3,
    progress: 37.5,
    certificates: [
      {
        id: 101,
        name: '食品安全操作证书'
      },
      {
        id: 201,
        name: '中式烹饪基础证书'
      }
    ]
  },
  {
    id: 2,
    title: '餐厅管理进阶之路',
    description: '从服务员到店长的晋升通道，掌握餐厅全面管理技能',
    coverImage: '/static/images/paths/restaurant-management.jpg',
    totalCourses: 12,
    totalHours: 90,
    completed: 2,
    progress: 16.7,
    certificates: [
      {
        id: 301,
        name: '厨房管理证书'
      }
    ]
  }
];

// 学习活动
const activities = [
  {
    id: 1,
    title: '三月厨艺挑战赛',
    description: '参与每周烹饪技能挑战，赢取丰厚奖励和徽章认证',
    coverImage: '/static/images/activities/cooking-challenge.jpg',
    startTime: '2024-03-01 00:00:00',
    endTime: '2024-03-31 23:59:59',
    status: 'ongoing', // 'upcoming', 'ongoing', 'ended'
    participants: 1250
  },
  {
    id: 2,
    title: '餐饮行业高峰论坛',
    description: '线上直播餐饮行业趋势与发展，知名餐饮专家分享经营心得',
    coverImage: '/static/images/activities/industry-forum.jpg',
    startTime: '2024-03-25 14:00:00',
    endTime: '2024-03-25 17:00:00',
    status: 'upcoming',
    participants: 768
  }
];

// 学习提醒
const learningReminders = [
  {
    id: 1,
    type: 'course',
    title: '《中式烹饪基础入门》学习进度提醒',
    content: '您已有3天未继续学习练习，当前进度35%，不要中断学习哦！',
    courseId: 10001,
    lessonId: 5,
    createTime: '2024-03-14 09:00:00'
  },
  {
    id: 2,
    type: 'exam',
    title: '考试即将开始提醒',
    content: '您预约的"食品安全管理认证"考试将于3天后(3月18日)开始，请提前做好准备。',
    examId: 7,
    createTime: '2024-03-15 10:00:00'
  },
  {
    id: 3,
    type: 'live',
    title: '直播预约提醒',
    content: '您预约的"行业大师直播"将于今天晚上8点开始，记得准时观看！',
    liveId: 10086,
    createTime: '2024-03-18 18:00:00'
  }
];

// API调用处理函数
const homeMock = {
  // 获取首页轮播图
  'GET /api/home/<USER>': (data) => {
    let result = [...banners];

    // 如果指定了是否为新用户，则筛选相应的轮播图
    if (data.isNewUser !== undefined) {
      if (data.isNewUser === 'true' || data.isNewUser === true) {
        // 包含专为新用户展示的轮播图
        result = result.filter(item => !item.forNewUser || item.forNewUser);
      } else {
        // 过滤掉专为新用户展示的轮播图
        result = result.filter(item => !item.forNewUser);
      }
    }

    // 筛选当前有效的轮播图
    const now = new Date().toISOString().split('T')[0];
    result = result.filter(item => {
      return item.startTime <= now && item.endTime >= now;
    });

    return {
      code: 0,
      message: 'success',
      data: result
    };
  },

  // 获取公告列表
  'GET /api/home/<USER>': (data) => {
    let result = [...announcements];

    // 根据请求参数只返回未读公告
    if (data.unreadOnly === 'true' || data.unreadOnly === true) {
      result = result.filter(item => !item.isRead);
    }

    // 根据重要性筛选
    if (data.isImportant === 'true' || data.isImportant === true) {
      result = result.filter(item => item.isImportant);
    }

    return {
      code: 0,
      message: 'success',
      data: result
    };
  },

  // 获取当前公告列表
  'GET /api/wechat/announcements/current': (data) => {
    let result = [...announcements];

    return {
      code: 0,
      message: 'success',
      data: {
        list: result
      }
    };
  },

  // 获取公告详情
  'GET /api/wechat/announcements/1': (data) => {
    const announcement = announcements.find(item => item.id === 1);
    return {
      code: 0,
      message: 'success',
      data: announcement
    };
  },

  'GET /api/wechat/announcements/2': (data) => {
    const announcement = announcements.find(item => item.id === 2);
    return {
      code: 0,
      message: 'success',
      data: announcement
    };
  },

  'GET /api/wechat/announcements/3': (data) => {
    const announcement = announcements.find(item => item.id === 3);
    return {
      code: 0,
      message: 'success',
      data: announcement
    };
  },

  // 获取学习统计数据
  'GET /api/home/<USER>': (data) => {
    return {
      code: 0,
      message: 'success',
      data: learningStats
    };
  },

  // 获取练习分类
  'GET /api/home/<USER>': (data) => {
    return {
      code: 0,
      message: 'success',
      data: courseCategories
    };
  },

  // 获取推荐练习
  'GET /api/home/<USER>': (data) => {
    return {
      code: 0,
      message: 'success',
      data: recommendedCourses
    };
  },

  // 获取学习路径
  'GET /api/home/<USER>': (data) => {
    return {
      code: 0,
      message: 'success',
      data: learningPaths
    };
  },

  // 获取学习活动
  'GET /api/home/<USER>': (data) => {
    let result = [...activities];

    // 根据状态筛选
    if (data.status) {
      result = result.filter(item => item.status === data.status);
    }

    return {
      code: 0,
      message: 'success',
      data: result
    };
  },

  // 获取学习提醒
  'GET /api/home/<USER>': (data) => {
    return {
      code: 0,
      message: 'success',
      data: learningReminders
    };
  },

  // 标记公告为已读
  'POST /api/home/<USER>/read': (data) => {
    const { id } = data;

    // 模拟更新已读状态
    const announcement = announcements.find(item => item.id === parseInt(id));
    if (announcement) {
      announcement.isRead = true;

      return {
        code: 0,
        message: 'success',
        data: { id, isRead: true }
      };
    } else {
      return {
        code: 404,
        message: '公告不存在',
        data: null
      };
    }
  },

  // 加入学习活动
  'POST /api/home/<USER>/join': (data) => {
    const { activityId } = data;

    const activity = activities.find(item => item.id === parseInt(activityId));
    if (activity) {
      if (activity.status === 'ended') {
        return {
          code: 400,
          message: '该活动已结束',
          data: null
        };
      }

      // 模拟参与成功
      activity.participants += 1;

      return {
        code: 0,
        message: 'success',
        data: {
          activityId,
          joined: true,
          message: '成功参与活动！'
        }
      };
    } else {
      return {
        code: 404,
        message: '活动不存在',
        data: null
      };
    }
  }
};

export default homeMock;