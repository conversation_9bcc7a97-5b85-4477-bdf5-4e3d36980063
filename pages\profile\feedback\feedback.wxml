<!--pages/profile/feedback.wxml-->
<view class="container">

  <view class="feedback-container">
    <!-- 反馈说明 -->
    <view class="feedback-intro">
      <text>您的反馈对我们很重要，我们会认真处理每一条反馈信息。</text>
    </view>

    <!-- 反馈表单 -->
    <view class="feedback-form">
      <!-- 反馈内容 -->
      <view class="form-group">
        <text class="form-label">反馈内容</text>
        <textarea
          class="form-textarea"
          value="{{content}}"
          placeholder="请详细描述您遇到的问题或建议"
          maxlength="500"
          bindinput="inputContent"
        ></textarea>
        <text class="word-count">{{content.length}}/500</text>
      </view>

      <!-- 图片上传 -->
      <view class="form-group">
        <text class="form-label">上传图片（选填）</text>
        <view class="image-upload">
          <view class="image-preview">
            <view
              wx:for="{{images}}"
              wx:key="index"
              class="preview-item"
            >
              <image src="{{item}}" mode="aspectFill"></image>
              <text
                class="preview-remove iconfont icon-close"
                data-index="{{index}}"
                catchtap="removeImage"
              ></text>
            </view>
            <view
              class="add-image"
              bindtap="chooseImage"
              wx:if="{{images.length < 4}}"
            >
              <text class="iconfont icon-camera"></text>
              <text class="add-text">添加图片</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 联系人 -->
      <view class="form-group">
        <text class="form-label">联系人<text class="required">*</text></text>
        <input
          class="form-input"
          value="{{contactPerson}}"
          placeholder="请输入您的姓名"
          type="text"
          bindinput="inputContactPerson"
        />
      </view>

      <!-- 联系方式 -->
      <view class="form-group">
        <text class="form-label">联系方式<text class="required">*</text></text>
        <input
          class="form-input"
          value="{{contactInfo}}"
          placeholder="请输入手机号或邮箱地址"
          type="text"
          bindinput="inputContactInfo"
        />
      </view>

      <!-- 提交按钮 -->
      <button
        class="submit-button"
        bindtap="submitFeedback"
        disabled="{{!isValid}}"
      >提交反馈</button>
    </view>

    <!-- 历史反馈 -->
    <view class="history-feedback">
      <view class="history-title">
        <text>历史反馈</text>
        <text class="history-count">共{{pagination.total || historyList.length}}条</text>
      </view>

      <!-- 无反馈记录提示 -->
      <view class="empty-history" wx:if="{{historyList.length === 0}}">
        <text>暂无反馈记录</text>
      </view>

      <!-- 反馈列表 -->
      <view
        wx:for="{{historyList}}"
        wx:key="id"
        class="history-item"
      >
        <view class="history-header">
          <text class="history-status {{item.status}}">反馈{{item.statusText}}</text>
          <text class="history-date">{{item.date}}</text>
        </view>
        <text class="history-content">{{item.content}}</text>

        <!-- 反馈图片 -->
        <view class="history-images" wx:if="{{item.images && item.images.length > 0}}">
          <!-- {{item.images}} -->
          <view
            wx:for="{{item.images}}"
            wx:for-item="imgUrl"
            wx:for-index="imgIndex"
            wx:key="imgIndex"
            class="history-image-item"
          >
            <image
              src="{{imgUrl}}"
              mode="aspectFill"
              bindtap="previewHistoryImage"
              data-urls="{{item.images}}"
              data-current="{{imgUrl}}"
            ></image>
          </view>
        </view>

        <!-- 回复内容 -->
        <view class="history-reply" wx:if="{{item.reply}}">
          <text class="reply-label">官方回复：</text>
          <text class="reply-content">{{item.reply}}</text>
        </view>
      </view>

      <!-- 加载更多提示 -->
      <view class="load-more" wx:if="{{historyList.length > 0}}">
        <text wx:if="{{pagination.currentPage * pagination.pageSize < pagination.total}}">上拉加载更多</text>
        <text wx:else>已加载全部数据</text>
      </view>
    </view>
  </view>
</view>
