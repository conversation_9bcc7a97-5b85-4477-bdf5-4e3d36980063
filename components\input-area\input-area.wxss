/* components/input-area/input-area.wxss */
/* 输入区域包装器 - 新增 */
.input-area-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  transition: bottom 0.3s;
  width: 100%;
  border-top: 1rpx solid #e6e6e6;
}

/* 输入区域容器 */
.input-container {
  position: relative;
  background-color: #ffffff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  padding-bottom: 50rpx;
  /* padding-bottom: calc(50rpx + env(safe-area-inset-bottom)); */
  box-sizing: border-box;
}

/* 输入类型切换按钮 */
.input-type-switch {
  width: 70rpx;
  height: 70rpx;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 50rpx;
  color: #999999;
  background:linear-gradient(to right, #a18cd1, #fbc2eb);
  border-radius: 50%;
}
.input-type-switch .iconfont{
  font-size: 32rpx;
  color: #ffffff;
}

/* 输入框容器 */
.input-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  padding: 0 20rpx;
  margin: 0 20rpx;
  position: relative;
  border-radius: 10rpx;
  min-height: 70rpx;
}

/* 文本输入框 */
.input-textarea {
  flex: 1;
  min-height: 70rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 15rpx 20rpx 15rpx 0;
  box-sizing: border-box;
  width:80%;
}

/* 输入工具栏 */
.input-tools {
  display: flex;
  align-items: center;
}

/* 表情按钮 */
.emoji-button {
  font-size: 50rpx;
  color: #999999;
  margin-right: 20rpx;
}

/* 发送按钮 */
.send-button {
  width: 60rpx;
  height: 60rpx;
  /* border-radius: 50%; */
  /* background-color: #e0e0e0; */
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  /* color: #ffffff; */
  transition: all 0.3s;
}

.send-button.active {
  background-color: #a18cd1;
}

/* 语音输入框 */
.voice-input-box {
  flex: 1;
  margin: 0 20rpx;
}

/* 语音按钮 */
.voice-button {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  transition: all 0.3s;
}

.voice-button.recording {
  background-color: #e0e0e0;
} 