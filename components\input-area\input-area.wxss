/* components/input-area/input-area.wxss */
/* 输入区域包装器 - 新增 */
.input-area-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  transition: bottom 0.3s;
  width: 100%;
  border-top: 1rpx solid #e6e6e6;
}

/* 输入区域容器 */
.input-container {
  position: relative;
  background-color: #ffffff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  align-items:flex-end;
  transition: all 0.3s;
  padding-bottom: 50rpx;
  /* padding-bottom: calc(50rpx + env(safe-area-inset-bottom)); */
  box-sizing: border-box;
}
.input-left{
  width:70rpx;
  display:flex;
  flex-direction: column;
  align-items: center;
  justify-content:space-between;
  /* height:calc( 100% - 70rpx); */
  /* position:absolute;
  left:20rpx;
  bottom:50rpx; */
  /* min-height: 70rpx; */
}

/* 展开按钮 */
.expand-button {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(161, 140, 209, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.expand-button .iconfont {
  font-size: 24rpx;
  color: #a18cd1;
}
.expand-button .icon-up{
  transform: rotate(180deg);
}

/* 输入类型切换按钮 */
.input-type-switch {
  width: 70rpx;
  height: 70rpx;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 50rpx;
  color: #999999;
  background:linear-gradient(to right, #a18cd1, #fbc2eb);
  border-radius: 50%;
  position:absolute;
  bottom:50rpx;
  left:20rpx;
}
.input-type-switch .iconfont{
  font-size: 32rpx;
  color: #ffffff;
}

/* 输入框容器 */
.input-box {
  /* flex: 1;
  display: flex;
  align-items: center; */
  background-color: #f5f5f5;
  border-radius: 10rpx;
  padding: 0 20rpx;
  margin: 0 20rpx;
  position: relative;
  border-radius: 10rpx;
  min-height: 70rpx;
  width:calc(100% - 250rpx);
  float:left;
}

/* 文本输入框 */
.input-textarea {
  width: 100%;
  min-height: 70rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 15rpx 20rpx 15rpx 0;
  box-sizing: border-box;
  transition: height 0.2s ease; /* 添加高度过渡动画 */
}

/* 限制高度的输入框 */
.input-textarea.limited-height {
  /* max-height: 110rpx;  */
  overflow: hidden;
}


/* 输入工具栏 */
.input-tools {
  display: flex;
  align-items: center;
}

/* 表情按钮 */
.emoji-button {
  font-size: 50rpx;
  color: #999999;
  margin-right: 20rpx;
}

/* 发送按钮 */
.send-button {
  transition: all 0.3s;

  background:linear-gradient(to left, #a18cd1, #fbc2eb);

    width:100rpx;
    border-radius:10rpx;
    color:#fff;
    font-size:26rpx;
    line-height:70rpx;
    text-align:center;
  
}

.send-button.active {
  background-color: #a18cd1;
}

/* 语音输入框 */
.voice-input-box {
  flex: 1;
  margin: 0 20rpx;
  width:calc( 100% - 100rpx);
  float: right;
}

/* 语音按钮 */
.voice-button {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  transition: all 0.3s;
}

.voice-button.recording {
  background-color: #e0e0e0;
}

/* 全屏输入弹窗 */
.fullscreen-input-modal {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height:100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: none;
  transition: transform 0.3s ease-in-out;
  /* transform: translateY(100%); */
 
}

.fullscreen-input-modal.show {
  display: block;
  /* transform: translateY(0); */
}

.fullscreen-input-modal.show .fullscreen-input-container{
  transform: translateY(0);
  transition: transform 0.5s ease-in-out;
}
/* 全屏输入容器 */
.fullscreen-input-container {
  background-color: #ffffff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  height: 90vh;
  display: flex;
  flex-direction: column;
  margin-top:10vh;
  transform: translateY(100%);

}
.header-left .iconfont{
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(161, 140, 209, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  margin:20rpx;
}
/* 顶部工具栏 */
/* .fullscreen-header {
  height: 88rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  padding-top: env(safe-area-inset-top);
}

.header-left,
.header-right {
  width: 120rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
} */

/* .cancel-btn {
  font-size: 28rpx;
  color: #666;
}

.confirm-btn {
  font-size: 28rpx;
  color: #a18cd1;
  font-weight: 500;
} */

/* 全屏输入内容区域 */
.fullscreen-input-content {
  flex: 1;
  padding: 0rpx 30rpx;
  background-color: #ffffff;
  font-size:28rpx;
}

/* 全屏输入框 */
.fullscreen-textarea {
  width: 100%;
  height: 100%;
  font-size:28rpx;
  line-height: 48rpx;
  color: #333;
  background-color: transparent;
  border: none;
  outline: none;
  resize: none;
  box-sizing: border-box;
}

.fullscreen-textarea::placeholder {
  font-size:26rpx;
  color: #999;
}