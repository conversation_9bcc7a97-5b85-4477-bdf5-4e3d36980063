const app = getApp()
const authBehavior = require('../../behaviors/auth-behavior')
// 导入API服务
const { getPracticeList } = require('../../api/practice')
const { getInfoConfig,getPracticeStatistic, getRecentPracticeSubjects,getNoticeList } = require('../../api/home')
const request = require('../../utils/request').default
const { getUserInfo } = require('../../api/user')

Page({
  // 使用授权行为
  behaviors: [authBehavior],
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },
  // 页面的初始数据
  data: {
    isNewUser: false,
    currentTab: 'practice',
    recommendCourses:[],
    imgURL: request.config.imgURL,
    bannerConfig: {
      mainTitle: '欢迎使用餐烤餐考',
      subTitle: '提升餐饮技能，助力职业发展'
    },
    companyName:'',
    practiceStats: {
      thisWeek: 8,
      thisMonth: 23,
      threeMonths: 67,
      total: 132
    },
    examStats: {
      obtained: 3,
      pending: 2
    },
    form: {
      phone: '',
      name: '',
      idCard: ''
    },
    errors: {
      phone: false,
      name: false,
      idCard: false
    },
    nextPath:'pages/home/<USER>',

    // 练习统计项配置数组
    practiceStatItems: [
      { icon: 'icon-wode', title: '本周', key: 'thisWeek', className: 'position-waiter' },
      { icon: 'icon-rili', title: '本月', key: 'thisMonth', className: 'position-cashier' },
      { icon: 'icon-bell', title: '近三月', key: 'threeMonths', className: 'position-receptionist' },
      { icon: 'icon-quanbu', title: '全部', key: 'total', className: 'position-security' }
    ],
    // 考试统计项配置数组
    examStatItems: [
      { icon: 'icon-certificate', title: '已获取证书', key: 'obtained' },
      { icon: 'icon-award', title: '待获取证书', key: 'pending' }
    ],
    // 餐考师按钮位置
    masterBtnPosition: {
      x: 400,
      y: 1000
    },
    // 触摸起始位置
    startPos: {
      x: 0,
      y: 0
    },
    // 按钮尺寸
    btnSize: {
      width: 0,
      height: 0
    },
    // 窗口尺寸
    windowSize: {
      width: 0,
      height: 0
    },
    isZero:true,
    currentPosition:'',
    currentLevel:'',
    // 通知公告数据
    noticeList: [],
    displayNoticeList: [] // 首页显示的通知列表（最多2条）
  },

  // 生命周期函数--监听页面加载
  onLoad: function(options) {
    wx.setStorageSync('selectedTab', 1);
    // 等同于uniapp中的onLoad生命周期
    this.checkAuthStatus();
    // 更新用户信息  
    // 获取推荐练习
    // this.getRecommendCourses();

    // 获取首页配置信息
    if(wx.getStorageSync('isIdentityVerified')){
      this.loadInfoConfig();
    }

    // 添加身份验证状态更新事件监听
    const app = getApp();
    if (app.globalData.eventBus) {
      app.globalData.eventBus.on('isIdentityVerified:updated', (verified) => {
        this.setData({
          isIdentityVerified: verified
        });
      });
      app.globalData.eventBus.on('isAuthorized:updated', (authorized) => {
        this.setData({
          isAuthorized: authorized
        });
      });
    }

    // 初始化身份验证状态
    this.setData({
      isIdentityVerified: app.globalData.isIdentityVerified || false
    });
    this.init()


    // 初始化悬浮按钮位置
    this.initMasterBtnPosition();


  },

  // 页面显示时检查授权状态
  onShow: function() {
    this.init()
  },
  async init(){
    let res =  await this.checkAuthStatus()
    if(res){
      await Promise.all([
        await this.getAllPositions(),
        await this.getAllLevels()
      ]).then((result)=>{
          this.setData({
            positions:result[0].rows,
            levels:result[1]
          })
      })
        // 加载通知公告数据
        this.loadNotices();
      this.getRecommendCourses();
          // 更新用户信息
    this.updateUserInfo()

      this.setData({
        isNewUser: wx.getStorageSync('isNewUser'),
      })
       // 设置标题
    wx.setNavigationBarTitle({
      title: wx.getStorageSync('userInfo').companyName.slice(0,5)||'首页'
    });
      // 获取用户统计数据
      getPracticeStatistic().then(res=>{
        let isZero = this.data.practiceStatItems.every(item=>{
           return res[item.key].minutes==0
        })
          this.setData({
            isZero: isZero,
            practiceStats: res,
            examStats: res.certificates
          })
      })
    }

    // 更新TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: wx.getStorageSync('selectedTab') });
      if(this.data.isIdentityVerified){
        this.getTabBar().setData({
          list:this.data.allTabs
        });
      }
    }
  },
  // 初始化悬浮按钮位置
  initMasterBtnPosition: function() {
    const systemInfo = wx.getSystemInfoSync();
    const windowWidth = systemInfo.windowWidth;
    const windowHeight = systemInfo.windowHeight;
    const safeBottom = app.globalData.safeAreaBottom || 0;

    // 从本地存储获取上次保存的位置，如果没有则使用默认位置
    const savedPos = wx.getStorageSync('masterBtnPosition');

    let position = {};
    if (savedPos && savedPos.x !== undefined && savedPos.y !== undefined) {
      // 使用保存的位置
      position = savedPos;
    } else {
      // 默认位置：右侧底部
      position = {
        x:   400, // 估计按钮宽度为120px
        y:  500  // 底部预留空间
      };
    }

    // 更新按钮位置和窗口尺寸
    this.setData({
      masterBtnPosition: position,
      windowSize: {
        width: windowWidth,
        height: windowHeight
      }
    });

    // 获取按钮实际尺寸
    const query = wx.createSelectorQuery();
    query.select('.master-btn').boundingClientRect(rect => {
      if (rect) {
        this.setData({
          btnSize: {
            width: rect.width || 100,
            height: rect.height || 100
          }
        });
      }
    }).exec();
  },

  // 监听悬浮按钮位置变化
  onMasterBtnPositionChange: function(e) {
    const { x, y, source } = e.detail;
    const { windowSize, btnSize } = this.data;
    if (!source) return; // 忽略没有source的事件
    // 拖动过程中，直接更新位置，不做依附处理
    if (source === 'touch') {
      this.setData({
        masterBtnPosition: {
          x: x,
          y: y
        }
      });
      return;
    }else{
      const btnWidth = btnSize.width || 100;
      const btnHeight = btnSize.height || 100;
      let finalX = x;
      let finalY = y;

      // 判断按钮中心点位置
      const btnCenterX = x + (btnWidth / 2);
      if (btnCenterX > windowSize.width / 2) {
        // 如果超过屏幕中间，则依附到右侧
        finalX = windowSize.width - btnWidth - 20; // 右侧留出20px边距
      } else {
        // 否则依附到左侧
        finalX = 0; // 左侧留出20px边距
      }
      // 检查Y轴是否超出屏幕范围
      if (y < 0 || y > windowSize.height - btnHeight-60) {
        // Y轴超出屏幕范围，恢复到初始位置
        finalY = 500; // 使用初始Y轴位置
      } else {
        finalY = y;
      }

      // 更新最终位置并保存
      this.setData({
        masterBtnPosition: {
          x: finalX,
          y: finalY
        }
      });

      // 保存位置到本地存储
      wx.setStorageSync('masterBtnPosition', {
        x: finalX,
        y: finalY
      });
    }
  },

  // 获取推荐练习
  getRecommendCourses: function() {
    const isNewUser = wx.getStorageSync('isNewUser');
    const openId = wx.getStorageSync('openId');

    if (isNewUser) {
      // 新用户：获取推荐练习内容
      const params = {
        positionName: wx.getStorageSync('userInfo').currentPosition,
        positionLevel:wx.getStorageSync('userInfo').currentLevel,
        openId: openId,
      };

      getPracticeList(params)
        .then(result => {

          result = result.filter(item=>{
            return item.status=='必考'
          })
          result = result.slice(0, 3);
          this.setData({
            recommendCourses: result
          });
        })
        .catch(error => {
          console.error('获取推荐练习失败：', error);
          this.setData({
            recommendCourses: []
          });
        });
    } else {
      // 老用户：获取上次练习记录
      const params = {
        openId: openId,
      };

      getRecentPracticeSubjects(params)
        .then(result => {
          result = result.filter(item=>{
            return item.status=='必考'
          })
          result = result.slice(0, 3);

          result.map(item=>{
            item.positionNameText = this.data.positions.find(position => position.id === parseInt(item.positionName))?.name;
            item.positionLevelText = this.data.levels.find(level => level.id === parseInt(item.positionLevel))?.name;
          })
          this.setData({
            recommendCourses: result || []
          });
        })
        .catch(error => {
          this.setData({
            recommendCourses: []
          });
        });
    }
  },

  // 处理feature的访问检查逻辑
  checkFeatureAccess(e) {
    const feature = e.currentTarget.dataset.feature;
    const isAuthorized = this.data.isAuthorized;
    const isIdentityVerified = this.data.isIdentityVerified;
    const isNeedAuth = wx.getStorageSync('isNeedAuth')

    let nextPath = '';

    switch (feature) {
      case 'practice':
        nextPath = '/pages/practice/list';
        break;
      case 'exam':
        nextPath = '/pages/practice/list';
        break;
      case 'analysis':
        nextPath = '/pages/practice/list';
        break;
      case 'aimaster':
        nextPath = '/pages/practice/list';
        break;
      default:
        break;
    }

    // 设置要跳转的路径
    this.setData({ nextPath });

    // 检查授权和身份认证状态
      if (!isAuthorized) {
        // 未授权，显示授权弹窗
        this.setData({ showAuthPopup: true });
        return;
      }
    if(isNeedAuth){
      if (!isIdentityVerified) {
        // 已授权但未认证，显示身份认证弹窗
        // this.setData({ showAuthModal: true });
         // 跳转到申请加入企业
        this.goToJoin()
        return;
      }
    }

      if (nextPath) {
        wx.navigateTo({ url: nextPath });
      }


  },

  // 页面相关事件处理函数
  switchTab: function(e) {
    // 切换练习/考试Tab
    const tab = typeof e === 'string' ? e : e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  continueLearning: function(e) {
    const course = e.currentTarget.dataset.course;
    // 继续上次的学习
    this.navigateToPractice(course);
  },

  goCourseDetail: function(e) {
    // 查看练习详情
    const course = e.currentTarget.dataset.course;
    this.navigateToPractice(course);
  },

  goAllCourses: function() {
    wx.setStorageSync('selectedTab', 1);
    // 查看全部练习
    wx.switchTab({
      url: '/pages/practice/list'
    });
  },

  viewCertificates: function() {
    // 查看证书
    this.navigateToAuthRequiredPage('/pages/profile/my-certificates/my-certificates');
  },

  async goMasterChat() {
    await this.updateUserInfo()
    if(wx.getStorageSync('userInfo').auditStatus=='审核中'){
      wx.showToast({
        title: '审核中请稍后...',
                icon: 'none'
      })
    }else if(wx.getStorageSync('userInfo').auditStatus=='通过'){
      // 进入餐烤师分流页面
      const isAuthorized = this.data.isAuthorized;
      const isIdentityVerified = this.data.isIdentityVerified;
      const isNeedAuth = wx.getStorageSync('isNeedAuth');

      // 设置要跳转的路径
      const nextPath = '/pages/master-chat/index';
      this.setData({ nextPath });

      // 检查授权和身份认证状态
      if (!isAuthorized) {
        // 未授权，显示授权弹窗
        this.setData({ showAuthPopup: true });
        return;
      }

      if(isNeedAuth){
        if (!isIdentityVerified) {
          // 已授权但未认证，显示身份认证弹窗
          // this.setData({ showAuthModal: true });
          // return;
          // 跳转到申请加入企业
          this.goToJoin()
        }
      }

      // 直接跳转
      wx.navigateTo({ url: nextPath });
      // wx.showToast({
      //   title: '功能开发中，敬请期待...',
      //   icon: 'none'
      // })
    }else{
      wx.navigateTo({
        url: '/pages/profile/join-enterprise/join-enterprise',
      })
    }
  },

  // 授权弹窗成功回调
  // onAuthSuccess(e) {
  //   this.setData({
  //     isAuthorized: true,
  //     showAuthPopup: false
  //   });

  //   // 授权成功后检查是否需要身份认证
  //   if (!this.data.isIdentityVerified &&this.data.isNeedAuth) {
  //     this.setData({ showAuthModal: true });
  //   }
  // },

  // 授权弹窗关闭回调
  // onAuthClose() {
  //   this.setData({ showAuthPopup: false });
  // },


  // 处理表单输入变化
  handleInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;

    this.setData({
      [`form.${field}`]: value,
      [`errors.${field}`]: false // 清除对应字段的错误提示
    });
  },

  // 生命周期函数--监听页面卸载
  onUnload: function() {
    // 移除事件监听
    const app = getApp();
    if (app.globalData.eventBus) {
      app.globalData.eventBus.off('isIdentityVerified:updated');
      app.globalData.eventBus.off('isAuthorized:updated');
    }
  },
  onHide: function() {
    // 当用户离开当前tab时，清除授权状态
    this.clearAuthStatus();
  },

  // Tab项被点击时触发
  onTabItemTap: function(item) {
    // 更新选中状态
    wx.setStorageSync('selectedTab', 1);
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: wx.getStorageSync('selectedTab') });
    }
  },

  // 获取首页配置信息
  loadInfoConfig: function() {
    getInfoConfig()
      .then(result => {
        // 如果接口返回了有效数据，则使用接口数据，否则保持默认值
        if (result) {
          this.setData({
            bannerConfig: result
          });
        }
      })
      .catch(error => {
        console.error('获取首页配置信息失败：', error);
        // 发生错误时保持默认值
      });
  },
  // 分享
  onShareAppMessage: function() {
    return {
      title: '餐烤餐考',
      path: '/pages/home/<USER>'
    }
  },

  /**
   * 富文本转纯文本
   * @param {string} richText - 富文本内容
   * @returns {string} - 纯文本内容
   */
  stripHtmlTags(richText) {
    if (!richText) return '';

    // 移除HTML标签
    let plainText = richText.replace(/<[^>]*>/g, '');

    // 解码HTML实体
    plainText = plainText
      .replace(/&nbsp;/g, ' ')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    // 移除多余的空白字符
    plainText = plainText.replace(/\s+/g, ' ').trim();

    return plainText;
  },

  /**
   * 加载通知公告
   */
  loadNotices() {
    // 这里应该调用API获取通知数据
    getNoticeList().then(result=>{
      if (result && result.list) {
        // 处理富文本内容，转换为纯文本
        const processedList = result.list.map(item => ({
          ...item,
          plainContent: this.stripHtmlTags(item.content)
        }));

        // 处理通知数据，首页只显示最新的2条
        const displayList = processedList.slice(0, 2);

        this.setData({
          noticeList: processedList,
          displayNoticeList: displayList
        });

        console.log('通知数据加载完成，首页显示:', displayList.length, '条');
      }
    }).catch(error => {
      console.error('获取通知列表失败:', error);
      this.setData({
        noticeList: [],
        displayNoticeList: []
      });
    });
  },

  /**
   * 查看通知详情
   */
  viewNoticeDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/notice-detail/notice-detail?id=${id}`
    });
  },

  /**
   * 查看全部通知
   */
  viewAllNotices() {
    console.log('跳转到通知列表页面');
    wx.navigateTo({
      url: '/pages/notice/list'
    });
  },
})