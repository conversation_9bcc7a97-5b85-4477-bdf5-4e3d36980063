/**
 * 日期格式化工具函数
 * 提供跨平台一致的时间格式化能力，解决安卓上 toLocaleTimeString 兼容性问题
 */

/**
 * 时间转换为秒
 * @param {string} time - 时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"
 * @returns {number} 转换后的秒数
 */
function timeToSeconds(timeDate) {
  const [date, time] = timeDate.split(' ');
  const [year, month, day] = date.split('-');
  const [hours, minutes, seconds] = time.split(':');
  return new Date(year, month - 1, day, hours, minutes, seconds).getTime();
}

/**
 * 获取格式化的时间 (时:分)
 * @param {Date} [date=new Date()] - 要格式化的日期对象，默认为当前时间
 * @returns {string} 格式化后的时间字符串，格式为 "HH:MM"
 */
function getFormattedTime(date = new Date()) {
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}

/**
 * 获取格式化的时间 (时:分:秒)
 * @param {Date} [date=new Date()] - 要格式化的日期对象，默认为当前时间
 * @returns {string} 格式化后的时间字符串，格式为 "HH:MM:SS"
 */
function getFormattedTimeWithSeconds(date = new Date()) {
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
}

/**
 * 获取完整格式化的日期和时间
 * @param {Date} [date=new Date()] - 要格式化的日期对象，默认为当前时间
 * @returns {string} 格式化后的日期时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"
 */
function getFormattedDateTime(date = new Date()) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  
  return `${year}-${month}-${day} ${getFormattedTimeWithSeconds(date)}`;
}

/**
 * 获取格式化的时间 (时:分:秒)
 * @param {number} seconds - 要格式化的时间秒数
 * @returns {string} 格式化后的时间字符串，格式为 "MM:SS"
 */
function getFormattedTimeFromSeconds(totalSeconds) {
  const totalSecond=totalSeconds/1000
  const minutes = Math.floor(totalSecond / 60).toString().padStart(2, '0');
  const seconds = Math.floor(totalSecond % 60).toString().padStart(2, '0');
  return `${minutes}:${seconds}`;
}



module.exports = {
  getFormattedTime,
  getFormattedTimeWithSeconds,
  getFormattedDateTime,
  timeToSeconds,
  getFormattedTimeFromSeconds
}; 