// components/stats-summary/stats-summary.js
Component({
  properties: {
    title: {
      type: String,
      value: '统计'
    },
    stats: {
      type: Array,
      value: []
    },
    bgStyle: {
      type: String,
      value: 'var( --primary-gradient)'
    }
  },
  
  data: {
    customStyle: ''
  },
  
  options:{
    styleIsolation: 'apply-shared',
    multipleSlots: true
  },
  observers: {
    'bgStyle': function(bgStyle) {
      this.setData({
        customStyle: `background: ${bgStyle}`
      });
    }
  },
  
  methods: {
    
  }
}) 