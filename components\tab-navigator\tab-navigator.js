Component({
  properties: {
    tabs: {
      type: Array,
      value: []
    },
    // 当前激活的标签索引或值
    activeTab: {
      type: Number,
      value: 0
    },
    activeTabName: {
      type: String,
      value: ''
    },
    // 标签项是否是对象形式
    isObjectTab: {
      type: Boolean,
      value: false
    },
    // 如果标签是对象，指定标签文本的键名
    labelKey: {
      type: String,
      value: 'label'
    },
    // 如果标签是对象，指定标签值的键名
    valueKey: {
      type: String,
      value: 'value'
    }
  },
  
  methods: {
    isActive(e) {
      const { index } = e.currentTarget.dataset;
      const tab = this.data.tabs[index];
      if (this.data.isObjectTab) {
        return this.data.activeTab === tab[this.data.valueKey];
      }
      return this.data.activeTab === index;
    },
    
    onTabTap(e) {
      const { index } = e.currentTarget.dataset;
      const tab = this.data.tabs[index];
      if (this.data.isObjectTab) {
        this.triggerEvent('change', { value: tab[this.data.valueKey], tab });
      } else {
        this.triggerEvent('change', { value: index, tab });
      }
    },
    
    getTabLabel(index) {
      const tab = this.data.tabs[index];
      return this.data.isObjectTab ? tab[this.data.labelKey] : tab;
    }
  }
}) 