// api/master-chat.js
import { get, post } from '../utils/request';

/**
 * 获取餐考师初始消息
 * @returns {Promise<Array>} 初始消息列表
 */
export const getInitialMessages = () => {
  return get('/master-chat/initial-messages');
};

/**
 * 获取快捷提问列表
 * @returns {Promise<Array>} 快捷提问列表
 */
export const getQuickQuestions = () => {
  return get('/master-chat/quick-questions');
};

/**
 * 获取餐厅配置信息
 * @returns {Promise<Object>} 餐厅配置信息
 */
export const getRestaurantConfig = () => {
  return get('/wechat/getRestaurantConfig');
};