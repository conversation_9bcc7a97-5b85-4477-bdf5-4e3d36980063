// api/master-chat.js
import { get, post } from '../utils/request';

/**
 * 获取餐考师初始消息
 * @returns {Promise<Array>} 初始消息列表
 */
export const getInitialMessages = () => {
  return get('/master-chat/initial-messages');
};

/**
 * 获取快捷提问列表
 * @returns {Promise<Array>} 快捷提问列表
 */
export const getQuickQuestions = () => {
  return get('/master-chat/quick-questions');
};

/**
 * 获取餐厅配置信息
 * @returns {Promise<Object>} 餐厅配置信息
 */
export const getRestaurantConfig = () => {
  return get('/wechat/getRestaurantConfig');
};

/**
 * 上传语音消息
 * @param {Object} file - 语音文件对象
 * @returns {Promise<Object>} 上传结果
 */
export const uploadVoiceMessage = (file) => {
  // 微信小程序上传文件需要使用特殊API
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: 'https://api.example.com/master-chat/upload-voice', // 替换为实际的API地址
      filePath: file.path,
      name: 'file',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token') || ''}`
      },
      success: (res) => {
        if (res.statusCode === 200) {
          let data;
          try {
            data = JSON.parse(res.data);
            if (data.code === 0 || data.code === 200) {
              resolve(data.data);
            } else {
              wx.showToast({
                title: data.message || '上传失败',
                icon: 'none'
              });
              reject(data);
            }
          } catch (e) {
            reject(e);
          }
        } else {
          wx.showToast({
            title: `上传失败(${res.statusCode})`,
            icon: 'none'
          });
          reject(res);
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '上传失败，请检查网络',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
}; 