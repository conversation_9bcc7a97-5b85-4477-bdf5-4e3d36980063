# 授权行为更新日志

## 更新内容

### 1. 修改 auth-popup 组件

- 更新为使用微信官方机制获取用户昵称和手机号
- 使用 `open-type="chooseAvatar"` 获取微信头像
- 使用 `open-type="getPhoneNumber"` 获取微信绑定手机号
- 移除原有的手动输入手机号和验证码逻辑
- 优化界面样式，使其更符合微信小程序设计规范

### 2. 更新 behaviors/auth-behavior.js

- 调整授权检查逻辑，使用 token 判断授权状态
- 优化页面导航逻辑，增加对 tabBar 页面的适配
- 更新需要授权的页面列表，明确授权范围
- 添加授权失败后的回退逻辑

### 3. 更新 app.js

- 添加基础 URL 配置和授权状态检查
- 增加令牌验证功能，确保用户授权状态有效
- 初始化全局授权状态参数

### 4. 更新 TabBar 页面

- 为全部 TabBar 页面添加 auth-popup 组件引用
- 修改 home/index.js
- 修改 practice/list.js
- 修改 promotion/list.js
- 修改 profile/index.js

### 5. 全局组件注册

- 在 app.json 中注册 auth-popup 组件，使其全局可用

## 注意事项

- 需要在微信小程序管理后台添加获取手机号权限
- 需要实现后端 API 解密获取手机号的接口
- 微信授权头像、手机号等功能需要小程序符合相关类目要求 