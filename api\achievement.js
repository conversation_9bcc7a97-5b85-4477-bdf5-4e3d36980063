// api/achievement.js
import request from '../utils/request'

/**
 * 获取用户成就勋章列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} 成就勋章列表
 */
export function getAchievementList(params = {}) {
  return request({
    url: '/achievement/list',
    method: 'GET',
    data: params
  })
}

/**
 * 获取成就勋章详情
 * @param {number} achievementId - 成就勋章ID
 * @returns {Promise} 成就勋章详情
 */
export function getAchievementDetail(achievementId) {
  return request({
    url: `/achievement/detail/${achievementId}`,
    method: 'GET'
  })
}

/**
 * 获取用户成就统计
 * @returns {Promise} 成就统计数据
 */
export function getAchievementStats() {
  return request({
    url: '/achievement/stats',
    method: 'GET'
  })
}

/**
 * 领取成就勋章
 * @param {number} achievementId - 成就勋章ID
 * @returns {Promise} 领取结果
 */
export function claimAchievement(achievementId) {
  return request({
    url: '/achievement/claim',
    method: 'POST',
    data: { achievementId }
  })
}
