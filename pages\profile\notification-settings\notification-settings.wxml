<view class="container">
  <view class="header">
    <text>通知设置</text>
  </view>

  <view class="settings-container">
    <view class="settings-intro">
      <text>请选择您想接收的通知类型</text>
    </view>

    <view class="settings-list">
      <view class="settings-item" wx:for="{{notificationSettings}}" wx:key="type">
        <view class="item-info">
          <view class="item-icon {{item.type}}">
            <text class="iconfont {{item.icon}}"></text>
          </view>
          <view class="item-content">
            <text class="item-title">{{item.title}}</text>
            <text class="item-desc">{{item.description}}</text>
          </view>
        </view>
        <switch checked="{{item.enabled}}" data-type="{{item.type}}" bindchange="toggleNotification" color="#a18cd1" />
      </view>
    </view>

    <view class="footer-notes">
      <text>注意：关闭通知后，您将无法及时获取相关信息。</text>
    </view>
  </view>

  <view class="footer-btn">
    <button class="save-btn" bindtap="saveSettings">保存设置</button>
  </view>
</view> 