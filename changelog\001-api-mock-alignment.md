# API与模拟数据对齐

## 变更摘要
- 修改了练习模块(practice.js)和晋升考试模块(promotion.js)的API接口定义
- 将模拟数据模块的导出格式从ES模块导出改为CommonJS导出
- 确保API函数命名与模拟数据处理函数一致
- 更新了mock/index.js中的导入方式

## 练习模块API对齐
原始API函数 | 新API函数 | 对应的模拟数据路径
--- | --- | ---
(未定义) | `getPracticeStats()` | GET /api/practice/stats
(未定义) | `getPracticeCategories()` | GET /api/practice/categories
`getPracticeList()` | (保持不变) | GET /api/practice/list
`getCourseDetail()` | `getPracticeDetail()` | GET /api/practice/detail
`submitPracticeRecord()` | `submitPractice()` | POST /api/practice/submit
(未定义) | `getWrongQuestions()` | GET /api/practice/wrong-questions
`getPracticeRecords()` | (保持不变) | GET /api/practice/records

## 晋升考试模块API对齐
原始API函数 | 新API函数 | 对应的模拟数据路径
--- | --- | ---
`getExamStats()` | (保持不变) | GET /api/promotion/stats
`getExamList()` | (保持不变) | GET /api/promotion/exams
(未定义) | `getExamLevels()` | GET /api/promotion/levels
(未定义) | `getExamTypes()` | GET /api/promotion/types
`getExamDetail()` | (保持路径不变，参数形式变更) | GET /api/promotion/exam/detail
(未定义) | `getExamResult()` | GET /api/promotion/exam/result
`submitExamAnswer()` | `submitExam()` | POST /api/promotion/exam/submit
(未定义) | `registerExam()` | POST /api/promotion/exam/register

## 模块导出格式变更
- 将 `practice.js` 和 `promotion.js` 的导出格式从 `export default XXX` 改为 `module.exports = { XXX }`
- 更新 `mock/index.js` 中的导入方式为 `import { XXXMock } from './modules/XXX'`

## 后续工作
- 更新前端组件中对API的调用，将旧API函数替换为新函数
- 为新增的API功能添加对应的前端UI实现 