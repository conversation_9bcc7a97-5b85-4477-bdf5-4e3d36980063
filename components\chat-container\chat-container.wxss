/* components/chat-container/chat-container.wxss */
/* 聊天容器 - 使用JS动态计算顶部内边距 */
.chat-container {
  flex: 1;
  padding: 20rpx;
  /* padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); */
  box-sizing: border-box;
  height: 100vh;
  overflow: auto;
  width: 100%;
  background-color: #f5f5f5;
}

.chat-list {
  padding: 30rpx 0px;
  width: 100%;
}

/* 系统消息 */
.system-message {
  text-align: center;
  margin: 20rpx 0;
  padding: 10rpx 0;
}

.system-text {
  display: inline-block;
  padding: 10rpx 30rpx;
  background-color: rgba(0, 0, 0, 0.05);
  color: #999999;
  font-size: 24rpx;
  border-radius: 8rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999999;
  font-size: 24rpx;
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  color: #a18cd1;
  font-size: 20rpx;
}

.loading-dots {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #a18cd1;
  margin: 0 8rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
} 

.exam-finished-tip,.get-question-tip{
  text-align: center;
  padding: 20rpx 0;
  color: #999999;
  font-size: 24rpx;
}
.get-question-btn{
  color:#a18cd1;
}
