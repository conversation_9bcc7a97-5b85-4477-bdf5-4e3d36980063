// pages/profile/my-certificates.js
import { getMyCertificateList, getCertificateDetail,getCertificateGenerate,getCertificateDetailByWechat } from '../../../api/certificate'
import { getPositionCertification } from '../../../api/promotion'
import {getPositionList,getLevels} from '../../../api/user'
const { getInfoConfig } = require('../../../api/home')
import request from '../../../utils/request'
const authBehavior = require('../../../behaviors/auth-behavior')
Page({
  behaviors: [authBehavior],

  /**
   * 页面的初始数据
   */
  data: {
    tabs: [],
    currentTab: 0,
    currentCert: null,
    searchQuery: '',
    showAnimation: false,
    isRefreshing: false,
    selectedPosition: 0,
    selectedLevel: 0,
    positions: [],
    positionOptions: [], // 添加存储岗位选项的数组
    pathNodes: [],
    certificates: [],
    loading: false,
    levelData: [], // 存储等级相关信息
    currentPage: 1,
    pageSize: 20,
    hasMore: true,
    currentLevelName: '',
    currentPositionName: wx.getStorageSync('userInfo')?.PositionName || '',
    currentLevel:'',
    certificatesRequired: 0,
    showFilterPopup: false,
    showCertDetailPopup: false,
    filteredCertificates: [],
    groupedCertificates: {},
    totalCertificates: 0,
    imgURL: request.config.imgURL,
    logo: '',
    progress:'',
    scrollViewHeight: 0, // 添加scrollViewHeight字段
    positionDrawerVisible: false,
    pageScroll: true, // 恢复页面滚动
    currentPosition: '',
    positionValue: '',
    certificateUrl:'',
    certId:0

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
    // 监听窗口尺寸变化事件
    wx.onWindowResize(this.handleWindowResize);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 页面渲染完成后再次计算高度，确保准确
    // this.calculateScrollViewHeight();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadData();
    // 页面显示时重新计算高度
    setTimeout(() => {
      this.calculateScrollViewHeight();
    }, 1000);
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 解除窗口尺寸变化监听
    wx.offWindowResize(this.handleWindowResize);
  },

  // 处理窗口尺寸变化
  handleWindowResize(size) {
    // 窗口尺寸变化时重新计算高度
    this.calculateScrollViewHeight();
  },

  // 计算scroll-view的高度
  calculateScrollViewHeight() {
    const that = this;
    
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;
    
    // 创建查询选择器
    const query = wx.createSelectorQuery().in(this);
    
    // 查询需要测量的元素
    query.select('#growthPath').boundingClientRect();
    query.select('#tabNavigator').boundingClientRect();
    query.select('#filterBar').boundingClientRect();
    
    query.exec(function(res) {
      if (res && res.length >= 3) {
        // 获取各元素的高度
        const growthPathHeight = res[0] ? res[0].height : 0;
        const tabNavigatorHeight = res[1] ? res[1].height : 0;
        const filterBarHeight = res[2] ? res[2].height : 0;
        
        // 计算其他元素的总高度
        const otherElementsHeight = growthPathHeight + tabNavigatorHeight + filterBarHeight;
        
        // 计算scroll-view的高度 (窗口高度 - 其他元素高度)
        // 额外减去20是为了留一些底部安全距离和可能的边距
        const scrollViewHeight = windowHeight - otherElementsHeight - 20;
        
        // 更新scroll-view的高度
        that.setData({
          scrollViewHeight: scrollViewHeight > 0 ? scrollViewHeight : 300 // 确保不会是负值，如果计算错误则给一个默认值
        });
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.onRefresh().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 使用用户默认岗位
  useDefaultPosition: function() {
    this.setData({
      currentPosition: wx.getStorageSync('userInfo').positionName,
      positionValue: wx.getStorageSync('userInfo').positionId,
    });
  },
   // 选择岗位
   async selectPosition(e) {
    const position = e.currentTarget.dataset.position;
        // 保存选择到专用存储
        // wx.setStorageSync('promotionPositionId', position.id);
        this.setData({
          currentPosition: position.name,
          positionValue: position.id
        });
        this.closePositionDrawer();
        // 重新获取证书信息
        await this.fetchPositionCertification();
        // 重新获取列表
        await this.loadCertificates(true);
        // 重新获取进度
        this.calculateProgress()
      },


  // 显示岗位选择抽屉
  showPositionDrawer: function() {
    this.setData({
      positionDrawerVisible: true,
      pageScroll: false // 禁用页面滚动
    });

    // 禁用页面滚动
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });

    // 设置页面样式，禁止滚动
    wx.setPageStyle({
      style: {
        overflow: 'hidden'
      }
    });
  },

  // 关闭岗位选择抽屉
  closePositionDrawer: function() {
    this.setData({
      positionDrawerVisible: false,
      pageScroll: true // 恢复页面滚动
    });

    // 恢复页面滚动
    wx.setPageStyle({
      style: {
        overflow: 'auto'
      }
    });
  },


  async loadData() {
    try {
      await Promise.all([
        // this.fetchGrowthPath(),
        this.fetchLogo(),
        this.getPostions(),
        this.useDefaultPosition(),
        await this.fetchPositionCertification(),
        this.fetchCertificateLevels(),
        this.loadCertificates(),

        // this.fetchPositionOptions(),
      ]);
      this.calculateProgress()
    } catch (error) {
      console.error('加载数据失败：', error);
      // wx.showToast({
      //   title: '加载数据失败，请重试',
      //   icon: 'none'
      // });
    }
  },

  async fetchPositionCertification() {
    const result = await getPositionCertification({positionId:this.data.positionValue});
    if (result) {
      this.setData({
        certificatesRequired: result.exams.length-result.employee.certificateCount,
        currentLevelName:result.employee.level,
        currentLevel:result.employee.levelId,
        totalCertificates:result.employee.certificateCount
      });
    }
  },

  async fetchLogo() {
    const result = await getInfoConfig();
    if (result) {
      this.setData({
        logo:  '/static/images/logo.png'
      });
    }
  },

  // 获取成长路径数据
  // async fetchGrowthPath() {
  //   try {
  //     const result = await getGrowthPath();
  //     if (result) {
  //       this.setData({
  //         pathNodes: result.nodes || [],
  //         // nextLevelName: result.nextLevel || '',
  //       });
  //     }
  //   } catch (error) {
  //     console.error('获取成长路径失败：', error);
  //   }
  // },
  getPostions(){
    getPositionList().then(res=>{
      this.setData({
        positions:res.rows
      })
    })
  },
  // 获取岗位名称选项
  async fetchPositionOptions() {
    try {
      const result = await getPositionList();
      if (result && result.rows && result.rows.length > 0) {
        // 提取岗位名称和ID
        const positionOptions = result.rows.map(item => ({
          name: item.name,
          value:item.id
        }));

        // 更新岗位选项数据
        const positionNames = ['全部', ...positionOptions.map(item => item.name)];
        
        this.setData({
          positionOptions: positionOptions,
          positions: positionNames,
          // 更新标签页
          tabs: [
            {
              name: '全部证书',
              value:0
            },
             ...positionOptions]
        });
      }
    } catch (error) {
      console.error('获取岗位名称选项失败：', error);
    }
  },

  // 获取证书等级数据
  async fetchCertificateLevels() {
    try {
      const result = await getLevels();
      if (result) {
        this.setData({
          pathNodes:result.reverse(),
          levelData: [{name:'全部等级',id:0},...result]
        });
      }
    } catch (error) {
      console.error('获取证书等级数据失败：', error);
    }
  },


  showCertDetail: function(e) {
    const { cert } = e.currentTarget.dataset;
    // 如果已经有详细信息，直接显示
     // 调用证书API获取详情
     getCertificateGenerate({certificateId:cert.id})
     .then(data => {
       // 格式化证书数据
       this.setData({
        certificateUrl:this.data.imgURL+data.certificateUrl,
         currentCert: data,
         showCertDetailPopup: true
       });
     })
      // 否则先获取详细信息
      // this.fetchCertificateDetail(cert.id);
  },

  async fetchCertificateDetail(id) {
    wx.showLoading({ title: '加载中...' });
    try {
      const res = await getCertificateDetail(id);
      this.setData({
        currentCert: res,
        showCertDetailPopup: true
      });
    } catch (error) {
      wx.showToast({ title: '网络异常，请稍后再试', icon: 'none' });
      console.error('获取证书详情错误:', error);
    } finally {
      wx.hideLoading();
    }
  },

  closeCertDetail: function() {
    this.setData({
      showCertDetailPopup: false
    });
  },

  shareCert: function(e) {
    // 如果是从组件触发，获取证书数据
    const cert = e.detail ? e.detail.cert : this.data.currentCert;
    console.log('分享证书:', cert);
    
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  },

  async loadCertificates(refresh = false) {
    this.setData({
      loading: true
    });
    
    try {
      // 如果是刷新，则重置页码
      if (refresh) {
        this.setData({
          currentPage: 1
        });
      }
      
      // 构建请求参数
      const params = {
        positionId: this.data.positionValue,
        // levelId: this.data.currentLevel,
        // certificateName: this.data.searchQuery
      };
      const res = await getMyCertificateList(params);
      if (res) {
        let newCertificates;
        // if (this.data.currentPage === 1 || refresh) {
          newCertificates = res.list || [];
        // } else {
        //   newCertificates = [...this.data.certificates, ...(res.list || [])];
        // }
        
        this.setData({
          certificates: newCertificates,
          hasMore: newCertificates.length < (res.total || 0),
          showAnimation: true,
          // totalCertificates: res.totalCertificates,
          // currentPage: refresh ? 2 : this.data.currentPage + 1
        });
        
      }
    } catch (error) {
      console.error('获取证书列表失败：', error);
      wx.showToast({
        title: '获取证书列表失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        isRefreshing: false
      });
    }
  },
  
  showFilter() {
    this.setData({
      showFilterPopup: true
    });
  },
  
  closeFilter() {
    this.setData({
      showFilterPopup: false
    });
  },
  
  // 防止抽屉中的滑动事件冒泡
  catchTouchMove() {
    return false;
  },
  
  
  // 通过ID获取岗位名称
  getPositionNameById(id) {
    const position = this.data.positionOptions.find(item => item.id === id);
    return position ? position.name : '';
  },
  
  selectLevel(e) {
    const { level } = e.currentTarget.dataset;
    this.setData({
      selectedLevel: level
    });
  },
  
  applyFilter() {
    this.closeFilter();
    this.loadCertificates(true);
  },
  
  handleSearch(e) {
    this.setData({
      searchQuery: e.detail.value
    });
    this.loadCertificates(true);
  },
  
  loadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadCertificates();
    }
  },
  
  async onRefresh() {
    this.setData({
      isRefreshing: true
    });
    await this.loadCertificates(true);
  },

  // 添加阻止页面滚动的方法
  preventTouchMove() {
    // 阻止触摸事件冒泡，不做其他处理
  },

  // 计算成长路径进度百分比
  calculateProgress() {
    // 确保pathNodes已初始化
    const { pathNodes, currentLevel } = this.data;
    if (!pathNodes || !pathNodes.length || !currentLevel) {
      return "0%"; // 默认无进度
    }
    
    // 找到当前等级在节点中的索引
    const currentIndex = pathNodes.findIndex(node => node.id == currentLevel);
    if (currentIndex === -1) {
      return "0%"; // 如果找不到，则无进度
    }
    
    // 保存当前节点索引，用于在WXML中判断哪些节点已完成
    this.setData({
      currentNodeIndex: currentIndex
    });
    
    // 计算进度百分比
    // 注意：如果是最后一个节点，进度为100%
    // 否则，进度是 当前节点索引 / (总节点数-1)
    const totalSteps = pathNodes.length - 1;
    if (totalSteps === 0) return "100%"; // 仅有一个节点
    // 下一节点进度

    const progress = currentIndex / totalSteps * 100;
    const nextProgress = (currentIndex+1) / totalSteps * 100;
    const cha=nextProgress-progress;
    if(currentIndex==pathNodes.length-1){
      this.setData({
        progress:100,
      })
    }else{
      this.setData({
        progress:progress+(cha/2),
      })
    }
    return `${progress}%`;
    // this.setData({
    //   progress:progress+(cha/2),
    // })
  },
  
  /**
   * 分享证书事件处理
   */
  shareCert: function(e) {
    const cert = e.detail.cert;
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 证书图片长按事件 - 保存图片
   */
  onCertificateImageLongPress() {
    console.log('长按证书图片，准备保存');

    if (!this.data.certificateUrl) {
      wx.showToast({
        title: '图片地址无效',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示操作菜单
    wx.showActionSheet({
      itemList: ['保存图片到相册', '预览图片'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 保存图片到相册
          this.saveCertificateImage();
        } else if (res.tapIndex === 1) {
          // 预览图片
          this.previewCertificateImage();
        }
      },
      fail: (err) => {
        console.log('用户取消操作', err);
      }
    });
  },

  /**
   * 证书图片点击事件 - 预览图片
   */
  onCertificateImageTap() {
    console.log('点击证书图片，预览图片');
    this.previewCertificateImage();
  },

  /**
   * 保存证书图片到相册
   */
  saveCertificateImage() {
    console.log('开始保存证书图片:', this.data.certificateUrl);

    // 显示加载提示
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 先下载图片到本地
    wx.downloadFile({
      url: this.data.certificateUrl,
      success: (downloadRes) => {
        console.log('图片下载成功:', downloadRes.tempFilePath);

        // 保存图片到相册
        wx.saveImageToPhotosAlbum({
          filePath: downloadRes.tempFilePath,
          success: () => {
            wx.hideLoading();
            wx.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 2000
            });
            console.log('证书图片保存成功');
          },
          fail: (saveErr) => {
            wx.hideLoading();
            console.error('保存图片失败:', saveErr);

            // 检查是否是权限问题
            if (saveErr.errMsg.includes('auth')) {
              wx.showModal({
                title: '需要相册权限',
                content: '保存图片需要访问您的相册权限，请在设置中开启',
                showCancel: true,
                cancelText: '取消',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    // 打开设置页面
                    wx.openSetting({
                      success: (settingRes) => {
                        console.log('设置页面返回:', settingRes);
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          wx.showToast({
                            title: '权限已开启，请重试保存',
                            icon: 'none',
                            duration: 2000
                          });
                        }
                      }
                    });
                  }
                }
              });
            } else {
              wx.showToast({
                title: '保存失败，请重试',
                icon: 'none',
                duration: 2000
              });
            }
          }
        });
      },
      fail: (downloadErr) => {
        wx.hideLoading();
        console.error('图片下载失败:', downloadErr);
        wx.showToast({
          title: '图片下载失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 预览证书图片
   */
  previewCertificateImage() {
    if (!this.data.certificateUrl) {
      wx.showToast({
        title: '图片地址无效',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    console.log('预览证书图片:', this.data.certificateUrl);

    wx.previewImage({
      current: this.data.certificateUrl, // 当前显示图片的链接
      urls: [this.data.certificateUrl], // 需要预览的图片链接列表
      success: () => {
        console.log('图片预览成功');
      },
      fail: (err) => {
        console.error('图片预览失败:', err);
        wx.showToast({
          title: '图片预览失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }
})