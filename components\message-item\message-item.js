import request from '../../utils/request';
Component({
  properties: {
    message: {
      type: Object,
      value: {}
    },
    messageIndex: {
      type: Number,
      value: -1
    },
    pageType: {
      type: String,
      value: 'exam' // 'exam' 或 'practice'
    },
    isPracticeStarted: {
      type: <PERSON>olean,
      value: false
    },
    isNextBtnDisabled: {
      type: Boolean,
      value: false
    },
    isRecordDetail: {
      type: Boolean,
      value: false
    }
  },
  options: {
    styleIsolation: 'apply-shared'
  },
  data: {
    avatar: '/static/images/icon.png', // 默认头像
    logo: '/static/images/logo.png'
  },
  
  lifetimes: {
    attached: function() {
      // 组件被创建时获取用户头像
      this.getUserAvatar();
    },
    ready: function() {
      // 组件初次渲染完成后再次尝试获取
      this.getUserAvatar();
    }
  },
  
  pageLifetimes: {
    show: function() {
      // 页面显示时尝试获取用户头像
      this.getUserAvatar();
    }
  },
  
  methods: {
    // 获取用户头像方法
    getUserAvatar: function() {
      try {
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo && userInfo.avatar) {
          let avatarUrl = userInfo.avatar;
          
          // 检查是否需要添加域名前缀
          if (avatarUrl && !avatarUrl.startsWith('http') && request.config.imgURL) {
            avatarUrl = request.config.imgURL + avatarUrl;
          }
          
          this.setData({
            avatar: avatarUrl,
          });
        }
      } catch (error) {
        console.error('获取用户头像失败:', error);
      }
    },
    
    onPlayMessageAudio(e) {
      const type = e.currentTarget.dataset.type;
      const isAudio = e.currentTarget.dataset.isAudio || false;
      this.triggerEvent('playAudio', { 
        type, 
        isAudio, 
        index: this.data.messageIndex 
      });
    },
    
    onStartPractice() {
      this.triggerEvent('startPractice');
    },
    
    onGetNextQuestion(e) {
      const disabled = e.currentTarget.dataset.disabled;
      if (disabled) return;
      this.triggerEvent('nextQuestion');
    }
  }
}) 