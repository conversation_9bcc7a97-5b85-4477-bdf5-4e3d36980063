// pages/practice/detail.js
import {
  getPracticeQuestion,
  evaluatePracticeAnswer ,
  submitPracticeRecord
} from '../../api/practice';
import { getFormattedTime, getFormattedTimeWithSeconds } from '../../utils/dateFormat';
const { StreamingWebSocket, TypewriterEffect } = require('../../utils/streaming-websocket');
const { getPracticeStreamUrl, getConnectionConfig } = require('../../config/websocket');

const authBehavior = require('../../behaviors/auth-behavior');

// 引入微信同声传译插件
const plugin = requirePlugin("WechatSI");
// 获取全局唯一的语音合成管理器
const manager = plugin.getRecordRecognitionManager();

Page({
  behaviors: [authBehavior],

  /**
   * 页面的初始数据
   */
  data: {
    practice_id: null, // 练习ID（从practice/question接口获取）
    title: '', // 练习标题
    startTime: null, // 练习开始时间
    timer: null, // 计时器
    formatTime: '00:00', // 格式化的时间显示
    keyboardHeight: 0, // 键盘高度
    isVoiceMode: false, // 是否为语音模式
    isRecording: false, // 是否正在录音
    isCancelling: false, // 是否正在取消录音
    wasCancelled: false, // 是否已取消录音（用于onStop回调）
    inputContent: '', // 输入内容
    showSystemMessage: true, // 是否显示系统消息
    hasMore: false, // 是否有更多历史消息
    messages: [], // 消息列表
    isLoading: false, // 是否正在加载
    question: '', // 当前题目内容
    standardAnswer: '', // 标准答案
    completedQuestions: 0, // 已完成题目数量
    recordingTime: '00:00', // 录音时间
    recordTimer: null, // 录音计时器
    startY: 0, // 触摸开始位置Y坐标
    recorderManager: null, // 录音管理器
    innerAudioContext: null, // 音频播放器
    isPlaying: false, // 是否正在播放语音
    content: '', // 识别内容
    timeUpdateChecked: false, // 用于检查音频时长
    isRecovering: false, // 用于防止无限循环恢复
    timeUpdateCount: 0, // 用于检查音频时长
    hasRecordAuth: false, // 是否已获取录音权限
    windowWidth: 0 ,// 窗口宽度
    isSendDisabled:false, // 是否禁用发送按钮
    isNextBtnDisabled: false, // 是否禁用下一题按钮
    status:"",
    loadingText:"出题中",
    showCountdown: false, // 是否显示倒计时
    countdownNumber: 3, // 倒计时数字
    countdownTimer: null, // 倒计时定时器
    positionLevel: '', // 职位等级
    positionName: '', // 职位名称
    // WebSocket 流式处理相关
    streamingSocket: null, // WebSocket 连接实例
    typewriterEffect: null, // 打字机效果实例
    isStreaming: false, // 是否正在流式输出
    streamingMessageIndex: -1, // 当前流式输出的消息索引
    showGetQuestionTip:false, // 是否显示获取试题提示
    questionTipText:"获取解析失败，请重新获取"
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 解析json
    const params = JSON.parse(options.info);

    this.setData({
      title: params.title || '',
      desc: params.desc || '',
      fileId: params.fileId || '',
      practice_id: params.practice_id || null,
      formatTime: params.remainingMinutes || '00:00',
      status: params.status || '',
      positionLevel: params.positionLevel || '',
      positionName: params.positionName || ''
    });

    // 检查并设置音频播放权限
    this.checkAudioPermission();

    // 初始化录音管理器
    this.initRecorderManager();

    // 初始化音频播放器
    this.initAudioContext();

    //    // 在后台预加载题目，但不立即显示
    // this.preloadQuestion();

    // 页面加载时就请求录音权限
    this.requestRecordPermission();

    // 添加欢迎消息
    const aiMessage = {
      type: 'ai',
      content: '你好呀，欢迎来和我一起学习，做好进步的准备了嘛，请点击下方的"开始练习"让我们一起悄悄的学习，然后惊艳所有人~',
      time: getFormattedTime(),
      isPlaying: false,
      audioDuration: 0,
      audioSrc: '',
      isAudioOnly: false,
      showStartButton: true // 显示开始按钮
    };

    this.setData({
      messages: [aiMessage]
    });

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      windowWidth: systemInfo.windowWidth
    });

    // 初始化 WebSocket 连接和打字机效果
    this.initStreamingConnection();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('📱 页面显示，检查 WebSocket 连接状态');

    // 只在没有 WebSocket 实例时才初始化，避免频繁重连
    if (!this.data.streamingSocket) {
      console.log('🔄 初始化 WebSocket 连接');
      this.initStreamingConnection();
    } else if (!this.data.streamingSocket.isConnected) {
      console.log('� WebSocket 实例存在但未连接，等待使用时再连接');
    } else {
      console.log('✅ WebSocket 连接正常');
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 播放消息音频
   */
  playMessageAudio(e) {
    // 注意：e 可能是页面原生事件，也可能是组件自定义事件
    let index, type, isAudio;

    // 如果是组件事件，获取detail中的数据
    if (e.detail && (e.detail.index !== undefined || e.detail.type)) {
      index = e.detail.index;
      type = e.detail.type;
      isAudio = e.detail.isAudio;
    } else {
      // 如果是原生事件，从dataset获取数据
      index = e.currentTarget.dataset.index;
      type = e.currentTarget.dataset.type;
      isAudio = e.currentTarget.dataset.isAudio;
    }

    const messages = this.data.messages;
    const message = messages[index];

    if (!message || !message.content) return;

    // 如果当前消息正在播放，则停止播放
    if (message.isPlaying) {
      this.stopSpeech();
      this.updateMessagePlayingState(index, false);
      return;
    }

    // 停止所有正在播放的音频
    this.stopAllAudio();

    // 根据消息类型和内容类型执行不同的播放逻辑
    if (type === 'user' && isAudio && message.audioSrc) {
      // 用户消息是录音，直接播放原始录音
      // console.log('播放用户录音音频:', message.audioSrc);
      this.playAudioFile(message.audioSrc, index, true);
    } else {
      // AI消息或用户文本消息，使用TTS转换为语音
      // console.log('播放文本消息的TTS:', message);
      this.playTextToSpeech(message.content, index, true);
    }
  },

  // 更新消息的播放状态
  updateMessagePlayingState(index, isPlaying) {
    if (index < 0 || index >= this.data.messages.length) return;

    this.setData({
      [`messages[${index}].isPlaying`]: isPlaying
    });
  },

  // 停止所有正在播放的音频
  stopAllAudio() {
    const messages = this.data.messages;
    if (messages && messages.length > 0) {
      const updatedMessages = messages.map(msg => {
        if (msg.isPlaying) {
          msg.isPlaying = false;
        }
        return msg;
      });

      this.setData({
        messages: updatedMessages,
        isPlaying: false
      });
    }

    // 停止当前播放
    this.stopSpeech();
  },

  /**
   * 初始化音频播放器
   */
  initAudioContext() {
    // 确保先销毁之前的实例
    this.releaseAudioContext();

    // 创建内部音频上下文
    const innerAudioContext = wx.createInnerAudioContext({useWebAudioImplement: false});

    // 设置音量为最大值
    innerAudioContext.volume = 1.0;
    innerAudioContext.useWebAudioImplement=true


    // 音频播放开始事件
    innerAudioContext.onPlay(() => {
      console.log('音频播放开始');
      this.setData({
        isPlaying: true
      });
    });

    // 音频播放结束事件
    innerAudioContext.onEnded(() => {
      this.setData({
        isPlaying: false
      });
    });

    // 音频播放错误事件
    innerAudioContext.onError((res) => {
      console.error('音频播放错误:', res);
      this.setData({
        isPlaying: false
      });

      // wx.showToast({
      //   title: '音频播放失败: ' + (res.errMsg || res.errCode),
      //   icon: 'none'
      // });
    });

    // 监听音频加载中事件
    innerAudioContext.onWaiting(() => {
      console.log('音频加载中...');
    });

    // 监听音频可以播放事件
    innerAudioContext.onCanplay(() => {
    });

    this.innerAudioContext = innerAudioContext;
  },

  /**
   * 释放音频上下文资源
   */
  releaseAudioContext() {
    if (this.innerAudioContext) {
      try {
        this.innerAudioContext.stop();
        // 移除所有事件监听
        this.innerAudioContext.offEnded();
        this.innerAudioContext.offError();
        this.innerAudioContext.offTimeUpdate();
        this.innerAudioContext.offCanplay();
        this.innerAudioContext.offPlay();
        this.innerAudioContext.destroy();
      } catch (error) {
        console.error('销毁音频上下文失败:', error);
      }
      this.innerAudioContext = null;
    }
  },

  /**
   * 统一的文本转语音播放方法
   * @param {string} text - 要转换为语音的文本
   * @param {number} messageIndex - 消息在列表中的索引，用于更新状态
   * @param {boolean} autoPlay - 是否自动播放
   */
  playTextToSpeech(text, messageIndex = -1, autoPlay = false) {
    if (!text) return;

    // 停止当前正在播放的音频
    this.stopSpeech();

    // 重置timeUpdate检查状态
    this.timeUpdateChecked = false;
    // 重置timeUpdate计数器
    this.timeUpdateCount = 0;

    // 如果指定了消息索引，则更新其播放状态
    if (messageIndex >= 0) {
      // 停止其他正在播放的音频的状态
      this.stopAllAudio();

      // 设置当前消息为播放状态
      if(autoPlay){
        this.updateMessagePlayingState(messageIndex, true);
      }

      // 如果消息已经有语音文件，直接播放
      const message = this.data.messages[messageIndex];
      if (message && message.audioSrc && !message.isAudio) {
        // console.log('使用已缓存的TTS语音文件播放:', message.audioSrc);
        this.playAudioFile(message.audioSrc, messageIndex,autoPlay);
        return;
      }
    }else {
      // 通用播放状态
      this.setData({
        isPlaying: true
      });
    }

    // 确保先销毁之前的实例
    this.releaseAudioContext();

    // 创建新的音频实例
    this.innerAudioContext = wx.createInnerAudioContext({useWebAudioImplement: false});

    // 设置音频事件处理
    this.setupAudioEvents(messageIndex);

    // 使用插件进行语音合成
    const plugin = requirePlugin("WechatSI");
    plugin.textToSpeech({
      lang: 'zh_CN',
      tts: true,
      content: text,
      success: (res) => {
        // 获取当前时间
        const currentTime = getFormattedTime();
        console.log('语音合成成功:', currentTime,res);

        if (!res.filename) {
          this.updateMessagePlayingState(messageIndex, false);
          return;
        }

        // 保存语音文件URL到消息对象
      if (messageIndex >= 0) {
        this.setData({
            [`messages[${messageIndex}].audioSrc`]: res.filename
          });
        }

        // 播放合成的音频
        this.playAudioSource(res.filename,autoPlay);
      },
      fail: (res) => {
        console.error('语音合成失败:', res);
        this.updateMessagePlayingState(messageIndex, false);

        // wx.showToast({
        //   title: '语音合成失败',
        //   icon: 'none'
        // });
      }
    });
  },

  /**
   * 设置音频事件处理
   * @param {number} messageIndex - 消息索引
   */
  setupAudioEvents(messageIndex = -1) {
    if (!this.innerAudioContext) return;

    // 设置音频结束事件
    this.innerAudioContext.onEnded(() => {
      this.updateMessagePlayingState(messageIndex, false);
    });

    // 设置音频错误事件
    this.innerAudioContext.onError((res) => {
      console.error('音频播放错误', res);
      this.updateMessagePlayingState(messageIndex, false);

      // wx.showToast({
      //   title: '音频播放失败: ' + res.errMsg,
      //   icon: 'none'
      // });

      // 尝试恢复
      if (messageIndex >= 0 && this.data.messages[messageIndex]) {
      setTimeout(() => {
          this.tryRecoverAudio(this.data.messages[messageIndex].content, messageIndex);
      }, 1000);
      }
    });

    // 设置音频加载完成事件，获取实际时长
    this.innerAudioContext.onCanplay(() => {
      console.log('音频加载完成:', this.innerAudioContext);
      this.checkAudioDuration(messageIndex);
    });

    // 当播放开始时也检查duration，此时通常已经有值
    this.innerAudioContext.onPlay(() => {
      this.checkAudioDuration(messageIndex);
    });

    // 使用timeUpdate事件作为最后的保障，确保能获取到duration
    this.innerAudioContext.onTimeUpdate(() => {
      // 只检查前5次timeUpdate事件，增加获取duration的机会
      if (messageIndex >= 0 && (!this.timeUpdateCount || this.timeUpdateCount < 5)) {
        // 初始化或递增计数器
        this.timeUpdateCount = (this.timeUpdateCount || 0) + 1;
        if (!this.data.messages[messageIndex]?.audioDuration ||
            this.data.messages[messageIndex].audioDuration === '00:00') {
        this.checkAudioDuration(messageIndex);
          } else {
          // 已经获取到时长，重置计数器
          this.timeUpdateCount = 5;
        }
      }
    });
  },

  /**
   * 播放音频源
   * @param {string} src - 音频文件路径
   */
  playAudioSource(src,autoPlay = false) {
    if (!src || !this.innerAudioContext) return;

    try {
      // 设置音频源
      this.innerAudioContext.src = src;

      // 添加延迟后自动播放，确保src设置完成
      setTimeout(() => {
        try {
          // 先判断是否已被销毁
          if (this.innerAudioContext&&autoPlay) {
            this.innerAudioContext.play();
          }
        } catch (error) {
          console.error('播放音频失败:', error);
            this.setData({
              isPlaying: false
            });
          }
      }, 200);
    } catch (error) {
      console.error('设置音频源失败:', error);
          this.setData({
            isPlaying: false
          });
        }
  },

  /**
   * 直接播放语音文件
   * @param {string} audioSrc - 语音文件URL
   * @param {number} messageIndex - 消息索引
   */
  playAudioFile(audioSrc, messageIndex,autoPlay = false) {
    if (!audioSrc) return;

    // 重置timeUpdate检查状态
    this.timeUpdateChecked = false;
    this.timeUpdateCount = 0;

    // 确保先销毁之前的实例
    this.releaseAudioContext();

    // 创建新的音频实例
    this.innerAudioContext = wx.createInnerAudioContext({useWebAudioImplement: false});

    // 设置音频事件处理
    this.setupAudioEvents(messageIndex);

    // 更新消息状态为播放中
    this.updateMessagePlayingState(messageIndex, true);

    // 播放音频
    this.playAudioSource(audioSrc,autoPlay);
  },

  /**
   * 停止语音播放
   */
  stopSpeech() {
    if (this.innerAudioContext) {
      try {
        // 先暂停再停止，有助于解决某些机型上的问题
        try {
          this.innerAudioContext.pause();
        } catch (e) {
          console.error('暂停音频失败:', e);
        }

        // 停止音频播放
        this.innerAudioContext.stop();
      } catch (error) {
        console.error('停止音频失败:', error);
      }

      // 重置状态
      this.timeUpdateChecked = false;
    }
  },

  /**
   * 尝试恢复音频播放
   */
  tryRecoverAudio(text, messageIndex) {

    // 确保音频实例被重新创建
    this.releaseAudioContext();

    // 如果有缓存的音频文件，尝试直接播放
    const message = messageIndex >= 0 ? this.data.messages[messageIndex] : null;
    if (message && message.audioSrc) {
      this.playAudioFile(message.audioSrc, messageIndex);
    } else if (text) {
      // 否则尝试重新合成
      // 设置一个标志避免无限循环
      if (!this.isRecovering) {
        this.isRecovering = true;
        this.playTextToSpeech(text, messageIndex);
        setTimeout(() => {
          this.isRecovering = false;
        }, 3000);
      }
    }
  },


  /**
   * 开始练习按钮点击事件
   */
  startPractice() {
    if (this.data.isPracticeStarted) {
      return; // 避免重复点击
    }

    // 开始计时器
    this.startTimer();
    // 添加系统消息
    const systemMessage = {
      type: 'system',
      content: '开始练习，请根据提示完成练习内容',
      time: getFormattedTime()
    };

    const messages = [...this.data.messages, systemMessage];

      this.setData({
      messages: messages,
      isPracticeStarted: true
    });

    // 如果已经预加载了题目，直接显示
    // if (this.data.preloadedQuestion) {
    //   console.log('检测到已预加载题目，直接显示');
    //   this.displayPreloadedQuestion();
    // }
    // else {
      // 如果没有预加载成功，重新获取题目
      this.getNextQuestion();
    // }
  },

  /**
   * 显示已预加载的题目
   */
  displayPreloadedQuestion() {
    const outputs = this.data.preloadedQuestion;
    if (!outputs) {
      this.getNextQuestion();
      return;
    }

    const standardAnswer = outputs.correctAnswer;

    // 保存题目信息
    this.setData({
      question: outputs.question,
      standardAnswer: standardAnswer,
      preloadedQuestion: null // 清空预加载数据
    });

    // 添加AI消息
    const aiMessage = {
      type: 'ai',
      content: outputs.question,
      time: getFormattedTime(),
      isPlaying: false,
      audioDuration: 0,
      audioSrc: '',
      isAudioOnly: false
    };

        this.setData({
      messages: [...this.data.messages, aiMessage]
    });

    // 获取当前时间
    const currentTime = getFormattedTime();
    // 将问题转换为语音并播放
    this.playTextToSpeech(outputs.question, this.data.messages.length - 1);

    setTimeout(() => {
      this.scrollToBottom();
    }, 300);
  },

  // 获取下一道题目
  async getNextQuestion(e) {
    // 检查按钮是否已禁用
    if (e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.disabled || this.data.isNextBtnDisabled) {
      return;
    }

    // 设置按钮为禁用状态
    this.setData({
      isNextBtnDisabled: true
    });

    this.setData({ isLoading: true,isSendDisabled:true,loadingText:"出题中"});

    this.scrollToBottom()
    try {
      let positionId=''
      let leverId=''
      // 从缓存中获取 positionId 和 leverId
      if(this.data.positionLevel&&this.data.positionName){
        positionId=this.data.positionName
        leverId=this.data.positionLevel
      }else{
         positionId = wx.getStorageSync('practicePositionId') || '';
         leverId = wx.getStorageSync('practiceLevelId') || '';
      }
      const params = {
        "file_id": this.data.fileId,
        "practice_id": this.data.practice_id,
        "time": this.data.formatTime || '00:00', // 添加计时参数
        "positionId": positionId, // 添加职位ID
        "leverId": leverId, // 添加级别ID
        "status": this.data.status || '' // 添加status参数
      };

      const result = await getPracticeQuestion(params);
      if (result) {
        const  outputs  = result;
        let standardAnswer = outputs.answer;

        // 保存题目信息
        this.setData({
          question: outputs.question,
          standardAnswer: standardAnswer,
          practice_id: outputs.practice_id // 保存practice_id
        });

        // 添加AI消息
        const aiMessage = {
          type: 'ai',
          content: outputs.question,
          time: getFormattedTime(),
          isPlaying: false,
          audioDuration: 0,
          audioSrc: '',
          isAudioOnly: false
        };
        this.data.messages.forEach(item=>{
          if(item.type === 'ai'){
            item.showNextQuestion = false;
          }
        })
        this.setData({
          messages: [...this.data.messages, aiMessage],

        });

        // 获取当前时间
        const currentTime = getFormattedTime();
        // 将问题转换为语音并播放
        this.playTextToSpeech(outputs.question, this.data.messages.length - 1);
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      }
    } catch (error) {
      console.error('获取题目失败:', error);
      wx.showToast({
        title: '获取题目失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        isLoading: false,
        isNextBtnDisabled: false,
        isSendDisabled:false,
       });
    }
  },
  startTimer() {
    const startTime = Date.now();
    this.setData({
      startTime: startTime
    });
    // 初始化计时器，从00:00开始计时
    const timer = setInterval(() => {
      const diff = Math.floor((Date.now() - this.data.startTime) / 1000);
      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');
      const seconds = (diff % 60).toString().padStart(2, '0');
      const formatTime = `${minutes}:${seconds}`;
      this.setData({
        formatTime: formatTime
      });
    }, 1000);

    this.timer = timer;
  },

  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.switchTab({
          url: '/pages/practice/list'
        });
      }
    });
  },

  toggleInputType() {
    if (this.data.isSendDisabled||!this.data.isPracticeStarted) {
      // this.setData({
      //   inputContent:'',
      // });
      const tip=this.data.isSendDisabled?'请点击下一题按钮，继续练习哦。':'请点击开始练习，进行练习哦。'
      wx.showToast({
        title: tip,
        icon: 'none'
      });
      return;
    }
    if(!this.data.isSendDisabled){
      this.setData({
        isVoiceMode: !this.data.isVoiceMode,
        // inputContent: ''
      });

      if (this.data.isVoiceMode) {
        wx.hideKeyboard();
      }
    }
  },

  onInputFocus(e) {
    this.setData({
      keyboardHeight: e.detail.height || 0
    });
    // 键盘弹出后，滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 300);
  },

  onInputBlur(e) {
    this.setData({
      keyboardHeight: 0,
      inputContent:e.detail.value
    });
    // TODO:测试用
    // this.sendMessage();

    // 键盘收起后，重新滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 300);
  },


  onScrollToUpper() {
    // 暂不实现加载更多历史消息
  },

  // 发送用户回答并获取解析
  async sendMessage(e) {
    // this.setData({
    //   inputContent:e.detail.content
    // })
    // 如果按钮被禁用，或者没有开始练习，或者没有下一题按钮，则不发送
    if (this.data.isSendDisabled||!this.data.isPracticeStarted||this.data.isNextBtnDisabled) {
      setTimeout(() => {
        this.setData({
          inputContent: ''
        });
      }, 50);
        let  tip=''
        if(this.data.isSendDisabled||this.data.isNextBtnDisabled){
          tip='请点击下一题按钮，继续练习哦。'
        }else {
          tip='请点击开始练习，进行练习哦。'

        }
        wx.showToast({
          title: tip,
          icon: 'none'
        });
        return;
      }

    // 使用传入的内容或当前内容
    const content = e.detail.content || this.data.inputContent;

    if (!content.trim()) {
      wx.showToast({
        title: '请输入回答内容',
        icon: 'none'
      });
      return
    }
    if ( !this.data.question || !this.data.standardAnswer) {
      wx.showToast({
        title: `未获取到题目，请重试`,
        icon: 'none'
      });
      return;
    }

    // 停止当前语音播放
    this.stopSpeech();

    // 构造用户消息
    const userMessage = {
      type: 'user',
      content: content,
      time: getFormattedTime(),
      isAudio: false, // 标记为非音频消息
      audioSrc: '' // 文本消息没有音频源
    };

    // 添加消息到列表
    const newMessages = [...this.data.messages, userMessage];

    this.scrollToBottom();
    // this.playTextToSpeech(userMessage.content, this.data.messages.length - 1, true);

    // *先清空输入框并更新消息列表
    // 使用setTimeout确保输入框清空操作在下一个事件循环中执行
    // 这样可以避免与组件内部的状态更新产生冲突
    this.setData({
      messages: newMessages,
      isLoading: true,
      loadingText:"思考中"
    });

    // 确保在UI更新后再清空输入内容
    setTimeout(() => {
      this.setData({
        inputContent: ''
      });
    }, 50);

    // 一问一答解析（传入保存的输入内容）
    this.parseOneQuestionOneAnswerFallback(content);
  },

  // 重新获取解析
  onGetQuestion(){
    this.setData({
      showGetQuestionTip:false
    })
    this.parseOneQuestionOneAnswerFallback();
  },
  // 传统方式的回退方法（集成WebSocket流式处理）
  async parseOneQuestionOneAnswerFallback(userInput=null) {
    try {
      this.setData({
        showGetQuestionTip:false
      })
      // 优先尝试使用 WebSocket 流式处理
      if (this.data.streamingSocket && this.tryWebSocketStreaming(userInput)) {
        console.log('WebSocket 流式处理已启动');
        return; // WebSocket 处理成功，直接返回
      }
    } catch (error) {
      console.error('解析回答失败:', error);
      this.setData({
        // 显示获取试题提示
        showGetQuestionTip:true,
      });
      wx.showToast({
        title: '解析失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        isLoading: false,
        // 禁用发送按钮
        isSendDisabled: true
       });
    }
  },

  /**
   * 尝试使用 WebSocket 流式处理
   * @param {string} userInput - 用户输入
   * @returns {boolean} - 是否成功启动 WebSocket 处理
   */
  tryWebSocketStreaming(userInput) {
    try {
      // 检查 WebSocket 实例是否存在
      if (!this.data.streamingSocket) {
        return false;
      }
      // 检查 WebSocket 连接状态
      if (!this.data.streamingSocket.isConnected) {
        this.connectWebSocketForStreaming(userInput);
        return true; // 返回 true 表示已启动连接过程
      }

      // 直接发送数据
      this.sendWebSocketData(userInput);
      return true;

    } catch (error) {
      console.error('WebSocket 流式处理失败:', error);
      return false;
    }
  },

  /**
   * 为流式处理建立 WebSocket 连接
   * @param {string} userInput - 用户输入
   */
  connectWebSocketForStreaming(userInput) {
    const wsUrl = getPracticeStreamUrl();
    const connectionConfig = getConnectionConfig();

    // 重新设置事件监听器（确保在连接前设置）
    this.setupWebSocketEventListeners(this.data.streamingSocket, this.data.typewriterEffect);

    // 异步连接，不阻塞主流程
    this.data.streamingSocket.connect(wsUrl, {
      header: {
        'openid': wx.getStorageSync('openId') || 'test_user',
        'Content-Type': 'application/json'
      },
      timeout: connectionConfig.connectTimeout
    }).then(() => {
      console.log('WebSocket 连接成功');
      // 连接成功后发送数据
      this.sendWebSocketData(userInput);
    }).catch((error) => {
      console.error('WebSocket 连接失败:', error);
      this.setData({
        isLoading: false,
        isSendDisabled: true,
        showGetQuestionTip:true,
      });
      // 手动触发 HTTP 回退处理
      // this.handleWebSocketFallback(userInput);
    });
  },

  /**
   * 处理 WebSocket 回退到 HTTP 的情况
   * @param {string} userInput - 用户输入
   */
  async handleWebSocketFallback(userInput) {
    console.log("触发http发送")
    try {
      // 发送用户回答到Dify解析工作流
      const params = {
        question: this.data.question,
        answer_yh: userInput !== null ? userInput : this.data.inputContent,
        tip: this.data.standardAnswer,
        practice_id: this.data.practice_id,
        time: this.data.formatTime || '00:00'
      };

      const result = await evaluatePracticeAnswer(params);

      if (result) {
        const resultValue = result.resultValue;
        const analysis = result.analysisContent;

        this.displayResponseWithStreaming(resultValue, analysis);
      }
    } catch (error) {
      console.error('HTTP 回退失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        isLoading: false,
        isSendDisabled: true
      });
    }
  },

  /**
   * 发送数据到 WebSocket 服务器
   * @param {string} userInput - 用户输入
   */
  sendWebSocketData(userInput) {
    const params = {
      type: 'parse_answer',
      payload: {
        question: this.data.question,
        answer_yh: userInput !== null ? userInput : this.data.inputContent,
        tip: this.data.standardAnswer,
        practice_id: this.data.practice_id,
        time: this.data.formatTime || '00:00'
      }
    };

    console.log('准备发送 WebSocket 数据:', params);
    if (this.data.streamingSocket && this.data.streamingSocket.isConnected) {
      // 重置打字机效果，防止内容累加
      if (this.data.typewriterEffect) {
        this.data.typewriterEffect.reset();
        console.log('🔄 已重置打字机效果');
      }

      // 在发送数据前创建 AI 消息用于流式输出
      const aiReply = {
        type: 'ai',
        result: '',
        content: '',
        time: getFormattedTime(),
        isPlaying: false,
        audioDuration: 0,
        audioSrc: '',
        showNextQuestion: false,
        isStreaming: true // 标记为流式输出消息
      };

      const newMessages = [...this.data.messages, aiReply];
      const messageIndex = newMessages.length - 1;

      this.setData({
        messages: newMessages,
        isStreaming: true,
        streamingMessageIndex: messageIndex,
        isLoading: false,
        completedQuestions: this.data.completedQuestions + 1
      }, () => {
        // setData 完成后的回调

        // 重置打字机效果，确保新的流式输出不受之前状态影响
        if (this.data.typewriterEffect) {
          console.log('🔄 重置打字机效果状态');
          this.data.typewriterEffect.stop();
          this.data.typewriterEffect.currentText = '';
          this.data.typewriterEffect.targetText = '';
        }

        // 滚动到底部
        this.scrollToBottom();

        // 发送数据
        this.data.streamingSocket.send(params);
        console.log('WebSocket 数据已发送成功');
      });

      // 设置超时检测，如果10秒内没有收到响应，则回退到HTTP
      setTimeout(() => {
        if (this.data.isStreaming && this.data.streamingMessageIndex >= 0) {
          // 检查当前流式消息是否还在进行中且没有内容
          const currentMessage = this.data.messages[this.data.streamingMessageIndex];
          if (currentMessage && currentMessage.isStreaming && !currentMessage.content) {
            console.warn('WebSocket 响应超时，回退到 HTTP 处理');
            // 移除当前的空消息
            const newMessages = this.data.messages.slice(0, -1);
            this.setData({
              messages: newMessages,
              isStreaming: false,
              streamingMessageIndex: -1
            });
            // 执行 HTTP 回退
            this.handleWebSocketFallback(userInput);
          }
        }
      }, 10000);
    } else {
      console.error('WebSocket 未连接，无法发送数据');
      throw new Error('WebSocket 未连接');
    }
  },

  /**
   * 使用流式效果显示传统HTTP响应
   * @param {string} resultValue - 评估结果
   * @param {string} analysis - 分析内容
   */
  displayResponseWithStreaming(resultValue, analysis) {
    console.log('htttp响应');
    // 创建一个新的 AI 消息用于流式显示
    const aiReply = {
      type: 'ai',
      result: resultValue,
      content: '',
      time: getFormattedTime(),
      isPlaying: false,
      audioDuration: 0,
      audioSrc: '',
      showNextQuestion: false,
      isStreaming: true // 标记为流式输出消息
    };

    const newMessages = [...this.data.messages, aiReply];
    const messageIndex = newMessages.length - 1;

    this.setData({
      messages: newMessages,
      isStreaming: true,
      streamingMessageIndex: messageIndex,
      completedQuestions: this.data.completedQuestions + 1
    });

    // 使用打字机效果显示内容
    if (this.data.typewriterEffect) {
      // 重置打字机效果
      this.data.typewriterEffect.stop();

      // 设置完成回调
      this.data.typewriterEffect.onComplete = (text) => {
        // 打字机效果完成
        this.setData({
          isStreaming: false,
          [`messages[${this.data.streamingMessageIndex}].isStreaming`]: false,
          [`messages[${this.data.streamingMessageIndex}].showNextQuestion`]: true
        });

        // 播放完整的语音
        // this.playTextToSpeech(text, this.data.streamingMessageIndex, true);

        // 滚动到底部
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      };

      // 开始打字机效果
      this.data.typewriterEffect.start(analysis);
    } else {
      // 如果没有打字机效果实例，直接显示内容
      this.setData({
        [`messages[${this.data.streamingMessageIndex}].content`]: analysis,
        [`messages[${this.data.streamingMessageIndex}].isStreaming`]: false,
        [`messages[${this.data.streamingMessageIndex}].showNextQuestion`]: true,
        isStreaming: false
      });

      // 播放语音
      // this.playTextToSpeech(analysis, this.data.streamingMessageIndex, true);

      // 滚动到底部
      setTimeout(() => {
        this.scrollToBottom();
      }, 100);
    }
  },

  scrollToBottom() {
    setTimeout(() => {
      const chatContainer = this.selectComponent('#chatContainer');
      if (chatContainer && typeof chatContainer.scrollToBottom === 'function') {
        chatContainer.scrollToBottom();
      }
    }, 100);
  },

  // 初始化录音管理器
  initRecorderManager() {
    const that=this;
    console.log('初始化录音管理器',manager);
    manager.onError((res) => {
      console.log('识别错误：', res);
    });
    manager.onStart=function(res) {
      console.log('识别开始：', res);
    };
    manager.onStop=function(res){
      console.log('识别结束onstop：', res);

      // 检查是否是被取消的录音
      if (that.data.wasCancelled) {
        console.log('录音已被用户取消，不处理录音结果');
        // 重置取消标志
        that.setData({
          wasCancelled: false,
          isLoading: false,
        });
        return;
      }
      console.log('结束data',that.data)

      // 语音识别结束，获取文本内容和临时录音文件
      const text = res.result || '';
      const tempFilePath = res.tempFilePath || '';

      // 防止二次判断，此处保留原有逻辑以兼容之前的实现
      if (that.data.isCancelling) {
        // 如果是取消录音，重置状态
        that.setData({
          isRecording: false,
          isCancelling: false,
          isLoading: false,
        });
        return; // 如果是取消录音，不进行处理
      }


      if (tempFilePath) {
        if(!text){
          wx.showToast({
            title: '语音录入未识别，请重新回答问题',
            icon: 'none'
          });
          return;
        }
        // 构造用户消息
        const userMessage = {
          type: 'user',
          content: text, // 显示识别的文本内容
          time: getFormattedTime(),
          isAudio: true, // 标记为音频消息
          audioSrc: tempFilePath, // 保存录音文件路径
          audioDuration: that.data.recordingTime || '00:00' // 保存录音时长
        };

        // 添加消息到列表并更新输入框
        const newMessages = [...that.data.messages, userMessage];
      that.setData({
          messages: newMessages,
          inputContent: text, // 将识别的文本设置到输入框
          isLoading:true,
          loadingText:"思考中"
        });

        that.scrollToBottom();

        // 如果识别出文本，则解析答案
        if (text && text.trim()) {
          // TODO:
          that.parseOneQuestionOneAnswerFallback(text);
        }
      } else {
        // 如果没有录音文件但有识别文本，设置为输入框内容
        if (text && text.trim()) {
          that.setData({
            inputContent: text
          });
          // 不自动发送，等用户确认后发送
        }
      }

      that.setData({
        isRecording: false,
        isCancelling: false
      });
    };
  },

  // 开始录音计时
  startRecordTimer() {
    // 清除可能存在的计时器
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    const startTime = Date.now();
    const recordTimer = setInterval(() => {
      const diff = Math.floor((Date.now() - startTime) / 1000);
      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');
      const seconds = (diff % 60).toString().padStart(2, '0');
      const recordingTime = `${minutes}:${seconds}`;

      this.setData({
        recordingTime: recordingTime
      });

      // 如果录音时长超过60秒，自动停止
      if (diff >= 60) {
        this.stopRecording();
      }
    }, 1000);

    this.setData({
      recordTimer: recordTimer
    });
  },

  // 停止录音计时
  stopRecordTimer() {
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
      this.setData({
        recordTimer: null,
        recordingTime: '00:00'
      });
    }
  },

  // 手指触摸移动事件
  onTouchMove(e) {
    if (!this.data.isRecording) return;

    try {
      // 阻止页面滚动
      e.preventDefault?.(); // 尝试阻止默认事件（可能不支持）

      // 获取当前触摸点的Y坐标
      const currentY = e.detail.touches[0].clientY;
      // 计算移动距离
      const moveDistance = this.data.startY - currentY;

      // 如果向上移动超过50像素，标记为取消状态
      if (moveDistance > 50) {
        if (!this.data.isCancelling) {
          this.setData({
            isCancelling: true
          });
        }
      } else {
        if (this.data.isCancelling) {
          this.setData({
            isCancelling: false
          });
        }
      }
    } catch (error) {
      console.error('处理触摸移动事件失败:', error);
      // 出错时重置录音取消状态，防止页面卡住
      this.setData({
        isCancelling: false
      });
    }

    // 返回false阻止事件冒泡和默认行为
    return false;
  },

  startRecording(e) {
    if (this.data.isSendDisabled||!this.data.isPracticeStarted) {
        this.setData({
          inputContent:'',
        });

        const tip=this.data.isSendDisabled?'请点击下一题按钮，继续练习哦。':'请点击开始练习，进行练习哦。'
        wx.showToast({
          title: tip,
          icon: 'none'
        });
        return;
      }
    // 获取触摸的起始Y坐标
    const startY = e.detail.touches[0].clientY;
    this.setData({
      startY: startY,
      isCancelling: false,
      wasCancelled: false // 重置取消标志
    });

    // 检查是否已有录音权限
    if (this.data.hasRecordAuth) {
      // 已有权限，开始倒计时
      this.startCountdown();
    } else {
      // 没有权限，提示用户并重置录音状态
        this.setData({
          isRecording: false
        });

        wx.showModal({
          title: '提示',
        content: '需要您授权录音权限才能使用语音功能',
          confirmText: '去授权',
          success: (res) => {
            if (res.confirm) {
            wx.openSetting({
              success: (settingRes) => {
                if (settingRes.authSetting['scope.record']) {
                  this.setData({
                    hasRecordAuth: true
                  });
            }
          }
        });
          }
      }
    });
    }
  },

  // 开始倒计时
  startCountdown() {
    this.setData({
      showCountdown: true,
      countdownNumber: 3,
      isRecording: true,
      inputContent:''
      
    });

    // 清除可能存在的倒计时定时器
    this.clearCountdownTimer();

    // 开始倒计时
    let count = 3;
    const countdownTimer = setInterval(() => {
      count--;

      if (count > 0) {
        this.setData({
          countdownNumber: count
        });
      } else {
        // 倒计时结束，开始录音
        this.setData({
          showCountdown: false,
          countdownNumber: 3,
        });

        // 清除倒计时定时器
        this.clearCountdownTimer();

        // 开始实际录音
        this.startActualRecording();
      }
    }, 300);

    this.setData({
      countdownTimer: countdownTimer
    });
  },

  // 开始实际录音
  startActualRecording() {
    // 开始录音
    console.log('开始录音', manager);
    manager.start({
      lang: 'zh_CN',
    });

    // 开始录音计时
    this.startRecordTimer();
  },

  // 清除倒计时定时器
  clearCountdownTimer() {
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
      this.setData({
        countdownTimer: null
      });
    }
  },

  stopRecording() {

    // 如果正在倒计时，取消倒计时
    if (this.data.showCountdown) {
      this.cancelCountdown();
      return;
    }

    if (!this.data.isRecording) return;

    // 如果正在取消录音，则调用取消录音方法
    if (this.data.isCancelling) {
      this.cancelRecording();
      return;
    }

    // 获取当前录音时间的秒数
    // const recordingTimeArr = this.data.recordingTime.split(':');
    // const minutes = parseInt(recordingTimeArr[0]);
    // const seconds = parseInt(recordingTimeArr[1]);
    // const totalSeconds = minutes * 60 + seconds;

    // 停止录音计时
    this.stopRecordTimer();

    // 停止录音
    manager.stop();

    this.setData({
      isRecording: false,
    });

    // 判断录音时长是否小于2秒
    // if (totalSeconds < 1) {
    //   wx.showToast({
    //     title: '录音时间过短',
    //     icon: 'none'
    //   });
    //   return;
    // }
  },

  cancelRecording() {
    console.log('cancelRecording',this.data)

    // 如果正在倒计时，取消倒计时
    if (this.data.showCountdown) {
      this.cancelCountdown();
      return;
    }

    this.setData({
      wasCancelled: true // 设置已取消标志
    })
    // 停止录音计时
    this.stopRecordTimer();

    // 重置录音状态，并设置取消标志
    this.setData({
      isRecording: false,
      isCancelling: false,
    });

    // 停止录音
    manager.stop();
    if (!this.data.isRecording) return;

  },

  // 取消倒计时
  cancelCountdown() {
    console.log('取消录音倒计时');

    // 清除倒计时定时器
    this.clearCountdownTimer();

    // 隐藏倒计时遮罩
    this.setData({
      showCountdown: false,
      countdownNumber: 3,
      isRecording: false
    });
  },

  // 处理组件触发的取消倒计时事件
  onCancelCountdown(e) {
    console.log('事件详情:', e.detail);
    this.cancelCountdown();
  },

  // 处理上滑状态变化
  onSlideChange(e) {
    const { isMovingUp } = e.detail;

    // 更新取消状态
    this.setData({
      isCancelling: isMovingUp
    });
  },

  // 处理录音结束事件
  onRecordingEnd(e) {
    const { isCancel } = e.detail;

    if (isCancel) {
      // 取消录音
      this.cancelRecording();
    } else {
      // 正常结束录音
      this.stopRecording();
    }
  },

  /**
   * 检查并设置音频播放权限
   */
  checkAudioPermission() {
    // 音频播放不需要显式授权，但可以检查设备静音状态
    wx.getSystemInfo({
      success: (res) => {
        // 提示用户确保设备未静音
        if (!res.microphoneAuthorized) {
          wx.showToast({
            title: '请确保已授予麦克风权限',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });

    // 尝试播放一个空音频以初始化音频引擎
    const testAudio = wx.createInnerAudioContext();
    testAudio.autoplay = true;
    testAudio.volume = 0;
    testAudio.playbackRate = 5;
    testAudio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA';
    testAudio.useWebAudioImplement=true

    // 短暂播放后销毁
    setTimeout(() => {
      testAudio.destroy();
    }, 500);
  },


  // 检查音频时长
  checkAudioDuration(messageIndex) {
    if (messageIndex < 0 || messageIndex >= this.data.messages.length) {
      console.error('无效的消息索引:', messageIndex);
      return;
    }

    const message = this.data.messages[messageIndex];
    if (message.type !== 'audio' && message.type !== 'tts' && message.type !== 'ai' && message.type !== 'user') {
      console.error('非音频消息类型:', message.type);
      return;
    }

    // 设置初始加载状态
    if (!message.audioDuration) {
      this.setData({
        [`messages[${messageIndex}].isAudioLoading`]: true
      });
    }

    // 添加重试机制
    let retryCount = 0;
    const maxRetries = 5;
    const retryIntervals = [100, 300, 500, 1000, 2000]; // 递增的等待时间

    const checkDuration = () => {
      if (this.innerAudioContext && this.innerAudioContext.duration &&
          !isNaN(this.innerAudioContext.duration) && this.innerAudioContext.duration > 0) {
        // 成功获取到时长
        console.log(`成功获取音频时长: ${this.innerAudioContext.duration}秒`);
        this.formatAndSetAudioDuration(messageIndex, this.innerAudioContext.duration);
      } else if (retryCount < maxRetries) {
        // 重试
        console.log(`尝试获取音频时长，第${retryCount + 1}次尝试`);
        setTimeout(() => {
          retryCount++;
          checkDuration();
        }, retryIntervals[retryCount]);
      } else {
        // 无法获取时长，使用估算值
        console.warn('无法获取音频时长，使用估算值');
        const estimatedDuration = this.estimateAudioDuration(message.content);
        this.formatAndSetAudioDuration(messageIndex, estimatedDuration);
      }
    };

    // 初始检查
    setTimeout(() => {
      checkDuration();
    }, 300);
  },

  /**
   * 根据文本内容估算音频时长
   * @param {string} content - 文本内容
   * @returns {number} - 估计的时长（秒）
   */
  estimateAudioDuration(content) {
    if (!content) return 5; // 默认最少5秒

    // 中文朗读大约每分钟300字，每字平均0.2秒
    // 英文朗读大约每分钟150词，每词平均0.4秒
    const chineseCharCount = (content.match(/[\u4e00-\u9fa5]/g) || []).length;
    const otherCharsCount = content.length - chineseCharCount;

    // 估算总时长（秒）
    let estimatedDuration = chineseCharCount * 0.2 + otherCharsCount * 0.1;

    // 确保最少有5秒的时长，并处理极端情况
    return Math.max(5, Math.min(estimatedDuration, 300)); // 最少5秒，最多5分钟
  },

  /**
   * 格式化并设置音频时长
   * @param {number} index - 消息索引
   * @param {number} duration - 音频时长（秒）
   */
  formatAndSetAudioDuration(index, duration) {
    // 格式化时长为 mm:ss 格式
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    const formattedDuration = `${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`;

    // 设置波形宽度和时长
    this.setWaveformWidth(index, duration);

    // 更新数据
        this.setData({
      [`messages[${index}].audioDuration`]: formattedDuration,
      [`messages[${index}].rawDuration`]: duration,
      [`messages[${index}].isAudioLoading`]: false
    });
  },

  /**
   * 设置音频波形宽度
   * @param {number} index - 消息索引
   * @param {number} duration - 音频时长（秒）
   */
  setWaveformWidth(index, duration) {
    // 根据音频时长计算波形宽度，但要考虑屏幕宽度的限制
    const baseWidth = this.data.windowWidth * 0.6; // 基础宽度为屏幕的60%
    const maxWidth = this.data.windowWidth * 0.8; // 最大宽度为屏幕的80%
    const minWidth = this.data.windowWidth * 0.4; // 最小宽度为屏幕的40%

    // 计算波形宽度：基础宽度 + 每秒额外宽度（但有最大/最小限制）
    let waveformWidth = baseWidth + (duration - 10) * 5; // 每秒增加5px宽度
    waveformWidth = Math.max(minWidth, Math.min(waveformWidth, maxWidth));

    this.setData({
      [`messages[${index}].waveformWidth`]: waveformWidth + 'px'
    });
  },

  /**
   * 请求录音权限
   */
  requestRecordPermission() {
    wx.authorize({
      scope: 'scope.record',
      success: () => {
        this.setData({
          hasRecordAuth: true
        });
      },
      fail: () => {
        console.log('未获取录音权限');
        this.setData({
          hasRecordAuth: false
        });

        // 显示获取权限对话框
        wx.showModal({
          title: '提示',
          content: '需要您授权录音权限才能使用语音功能',
          confirmText: '去授权',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.record']) {
                    this.setData({
                      hasRecordAuth: true
                    });
                  }
                }
              });
            }
          }
        });
      }
    });
  },

  /**
   * 监听屏幕旋转事件
   */
  onResize(size) {
    console.log('屏幕尺寸变化:', size);
    // 屏幕尺寸变化时重新计算导航栏高度
    this.getNavHeight();
  },

  // 捕获触摸移动事件，用于阻止页面滚动
  catchTouchMove() {
    // 直接返回false阻止事件冒泡和默认行为
    return false;
  },

  onInputChange(e) {
    this.setData({
      inputContent: e.detail.value
    });
  },





  /**
   * 初始化流式连接
   */
  initStreamingConnection() {
    try {
      // 获取连接配置
      const connectionConfig = getConnectionConfig();

      // 创建 WebSocket 实例
      const streamingSocket = new StreamingWebSocket({
        maxReconnectAttempts: connectionConfig.reconnect.maxAttempts,
        reconnectDelay: connectionConfig.reconnect.delay,
        heartbeatInterval: connectionConfig.heartbeat.interval,
        connectTimeout: connectionConfig.connectTimeout
      });

    // 保存页面实例引用
    const that = this;

    // 创建打字机效果实例
    const typewriterEffect = new TypewriterEffect({
      speed: 30, // 30ms 一个字符
      onUpdate: (text) => {
        // 更新当前流式输出的消息内容
        if (that.data.streamingMessageIndex >= 0) {
          // 使用完整的消息数组更新，确保页面重新渲染
          const newMessages = [...that.data.messages];
          if (newMessages[that.data.streamingMessageIndex]) {
            newMessages[that.data.streamingMessageIndex].content = text;
            that.setData({
              messages: newMessages
            });
          }
        } else {
          console.warn('⚠️ streamingMessageIndex 无效:', that.data.streamingMessageIndex);
        }
      },
      onComplete: (text) => {
        // 打字机效果完成
        console.log('🎯 打字机效果完成，文本长度:', text?.length);

        // 更新消息状态为完成
        if (that.data.streamingMessageIndex >= 0) {
          const newMessages = [...that.data.messages];
          if (newMessages[that.data.streamingMessageIndex]) {
            newMessages[that.data.streamingMessageIndex].content = text;
            newMessages[that.data.streamingMessageIndex].isStreaming = false;
            newMessages[that.data.streamingMessageIndex].showNextQuestion = true;

            that.setData({
              messages: newMessages,
              isStreaming: false
            }, () => {
              this.scrollToBottom();
              // 流式输出完成，不自动播放语音
              console.log('🎯 文本输出完毕，语音功能已准备就绪');
              // 可以在这里添加语音按钮的显示逻辑
            });
          }
        } else {
          that.setData({
            isStreaming: false
          });
        }
      },
      onError:(error)=>{
      },
    });

    // 设置 WebSocket 事件监听
    this.setupWebSocketEventListeners(streamingSocket, typewriterEffect);

      // 保存实例到页面数据
      this.setData({
        streamingSocket: streamingSocket,
        typewriterEffect: typewriterEffect
      });

    } catch (error) {
      console.error('初始化流式连接失败:', error);
      // 即使初始化失败，也要确保页面能正常工作
      this.setData({
        streamingSocket: null,
        typewriterEffect: null
      });
    }
  },

  /**
   * 设置 WebSocket 事件监听器
   * @param {StreamingWebSocket} streamingSocket - WebSocket 实例
   * @param {TypewriterEffect} typewriterEffect - 打字机效果实例
   */
  setupWebSocketEventListeners(streamingSocket, typewriterEffect) {
    console.log('设置 WebSocket 事件监听器');

    // 保存页面实例的引用
    const that = this;

    streamingSocket.on('streamStart', (message) => {
      console.log('🚀 流式输出开始:', message);
      // 只是标记开始，不处理数据
    // });

    // streamingSocket.on('streamChunk', (message) => {

      // 验证消息索引是否有效
      if (that.data.streamingMessageIndex >= 0 && that.data.messages[that.data.streamingMessageIndex]) {
      } else {
        console.error('❌ 目标消息不存在或索引无效:', {
          index: that.data.streamingMessageIndex,
          messagesLength: that.data.messages?.length
        });
      }

      // 将数据块追加到打字机效果中
      if (message.data && typewriterEffect) {
        typewriterEffect.append(message.data);
      } else {
        console.warn('⚠️ 无法处理数据块:', {
          hasContent: !!message.data,
          hasTypewriter: !!typewriterEffect,
          content: message.data
        });
      }
      this.scrollToBottom();
    });

    streamingSocket.on('streamEnd', (message) => {
      console.log('🏁 流式输出结束:', message);
      // 更新消息的评估结果，其他状态由打字机完成回调处理
      if (that.data.streamingMessageIndex >= 0) {
        const newMessages = [...that.data.messages];
        if (newMessages[that.data.streamingMessageIndex]) {
          newMessages[that.data.streamingMessageIndex].result = message.result || '';
          that.setData({
            messages: newMessages
          });
        }
      }
      this.scrollToBottom();
    });
    streamingSocket.on('errorEnd', (message) => {
      console.log('🏁 流式输出error:', message);
      that.setData({
        isLoading: false,
        isSendDisabled: true,
        showGetQuestionTip:true
      });
    }); 

    streamingSocket.on('error', (error) => {
      console.error('❌ WebSocket 错误:', error);
      // 重置状态
      that.setData({
        isStreaming: false,
        isLoading: false,
        isSendDisabled: false
      });
    });
  },

  /**
   * 清理流式连接
   */
  cleanupStreamingConnection() {
    console.log('🧹 清理流式连接');

    // 停止打字机效果
    if (this.data.typewriterEffect) {
      this.data.typewriterEffect.stop();
      console.log('⏹️ 打字机效果已停止');
    }

    // 关闭 WebSocket 连接
    if (this.data.streamingSocket) {
      this.data.streamingSocket.close(true); // 传递 true 表示主动关闭
      console.log('🔌 WebSocket 连接已关闭');
    }

    // 清理状态
    this.setData({
      streamingSocket: null,
      typewriterEffect: null,
      isStreaming: false,
      streamingMessageIndex: -1
    });

    console.log('✅ 流式连接清理完成');
  },

  /**
   * 页面隐藏时的处理
   */
  onHide() {
    console.log('📱 页面隐藏，清理 WebSocket 连接');
    this.cleanupStreamingConnection();
  },

  /**
   * 页面卸载时的处理
   */
  onUnload() {
    console.log('🚪 页面卸载，清理所有资源');

    // 清理流式连接
    this.cleanupStreamingConnection();

    // 停止所有定时器
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }

    // 停止语音播放
    if (typeof this.stopSpeech === 'function') {
      this.stopSpeech();
    }

    console.log('✅ 页面资源清理完成');
  },
});
