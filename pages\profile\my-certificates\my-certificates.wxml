<!--pages/profile/my-certificates.wxml-->
<view class="pages-container {{showCertDetailPopup ? 'no-scroll' : ''}}">
  <!-- 证书统计 -->
  <view id="growthPath" class="summary-box">
    <view class="header">
      <view class="title">证书成就</view>
      <button class="filter-button" bindtap="showPositionDrawer">
      <text class="iconfont icon-user"></text>
      <text>岗位</text>
    </button>
    </view>
    <view class="cert-count">
      <text class="cert-number">{{totalCertificates}}</text>
      <view class="cert-text">
        <text>已获得证书</text>
        <text class="cert-subtitle">继续加油，提升等级！</text>
      </view>
    </view>

    <!-- 晋升提示 -->
    <view class="promotion-tip">
      <view class="promotion-icon">
        <text class="iconfont icon-award"></text>
      </view>
      <view class="promotion-text">
        <view class="promotion-title">距离{{currentPositionName}} {{currentLevelName}}考试</view>
        <text class="promotion-subtitle">再获得 <text class="cert-highlight">{{certificatesRequired || 0}}</text> 个证书即可提升等级！</text>
      </view>
    </view>

    <!-- 添加成长路径可视化 -->
    <view class="growth-path">
      <view class="path-track" >
        <view class="path-track-line" style="width: {{progress}}%"></view>
      </view>
      <view class="path-nodes">
        <view wx:for="{{pathNodes}}" 
              wx:key="index"
              class="path-node {{item.id == currentLevel ? 'active current' : (index < currentNodeIndex ? 'completed' : '')}}">
          <view class="node-dot"></view>
          <view class="node-label">{{item.name}}</view>
        </view>
      </view>
      <view class="path-label">你的成长路径</view>
    </view>
  </view>

  <!-- 证书列表 - 按等级分组显示 -->
  <view class="certificates-container"
  style="height: {{scrollViewHeight}}px;"
  >
  <scroll-view 
    scroll-y 
    bindscrolltolower="loadMore"
    refresher-enabled="{{true}}"
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onRefresh"
    style="height: 100%;"
  >
    <!-- 加载状态 -->
    <view wx:if="{{loading && certificates.length === 0}}" class="loading-state">
      <text class="iconfont icon-loading"></text>
      <text>正在加载...</text>
    </view>
    
    <!-- 分等级显示证书 -->
    <block wx:elif="{{certificates.length > 0}}">
      <view wx:for="{{certificates}}" wx:for-index="level" wx:for-item="item" wx:key="level" class="level-section" bindtap="showCertDetail" data-cert="{{item}}">
        <view class="level-section-header">
          <view class="level-section-title">
            <view class="level-title-text">{{item.certificateName}}</view>
          </view>
        </view>
        
        <view class="level-description">
          <view class="cert-time-info">
            <text class="iconfont icon-time"></text>
            获取时间：{{item.createTime}}
          </view>
          <view class="cert-expire-info">
            <text class="iconfont icon-calendar"></text>
            到期时间：{{item.validUntil || '永久有效'}}
          </view>
        </view>
        
        <view class="card-footer">
          <view class="card-btn">
            查看证书
            <!-- <text class="iconfont icon-right"></text> -->
          </view>
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
     <empty-tip text="暂无证书" wx:if="{{ certificates.length === 0 && !loading}}"></empty-tip>
     <!-- <view wx:if="{{ certificates.length === 0 && !loading}}" class="empty-state">
      <view class="empty-icon-container">
        <text class="iconfont icon-cert-empty"></text>
      </view>
      <text class="empty-text">暂无证书</text>
      <text class="empty-subtext">完成学习获取你的第一个证书吧！</text>
    </view> -->
  </scroll-view>

  
  </view>
  <!-- 岗位选择抽屉 -->
  <view class="drawer-mask" wx:if="{{positionDrawerVisible}}" bindtap="closePositionDrawer" catchtouchmove="catchTouchMove"></view>
  <view class="drawer position-drawer {{positionDrawerVisible ? 'drawer-show' : ''}}" catchtouchmove="catchTouchMove">
    <view class="drawer-header">
      <text class="drawer-title">当前岗位：{{currentPosition}}</text>
      <text class="iconfont icon-close" bindtap="closePositionDrawer"></text>
    </view>
    <scroll-view class="drawer-body" scroll-y="{{true}}" wx:if="{{positions.length > 0}}">
      <view
        class="selector-item {{item.id === positionValue ? 'selector-item-active' : ''}}"
        wx:for="{{positions}}"
        wx:key="value"
        bindtap="selectPosition"
        data-position="{{item}}"
      >
        <view class="selector-icon">
          <text class="iconfont icon-user"></text>
        </view>
        <view class="selector-info">
          <text class="selector-title">{{item.name}}</text>
          <!-- <text class="selector-subtitle">{{item.desc}}</text> -->
        </view>
        <view class="selector-check" wx:if="{{item.id === positionValue}}">
          <text class="iconfont icon-check"></text>
        </view>
      </view>
    </scroll-view>
    <view class="drawer-body" wx:else>
      <empty-tip text="暂无岗位" />
    </view>
  </view>
  <!-- 证书详情弹窗 - 使用新的公共组件 -->
  <certificate-detail-popup
    visible="{{showCertDetailPopup}}"
    certificateUrl="{{certificateUrl}}"
    title="证书详情"
    showActions="{{true}}"
    showShareButton="{{true}}"
    showSaveButton="{{true}}"
    maskClosable="{{true}}"
    bind:close="closeCertDetail"
    bind:share="onCertificateShare"
    bind:savesuccess="onCertificateSaveSuccess"
    bind:savefail="onCertificateSaveFail"
    bind:longpress="onCertificateLongPress"
    bind:imagetap="onCertificateImageTap"
  />

  <!-- 筛选抽屉 -->
  <view class="drawer-mask" wx:if="{{showFilterPopup}}" bindtap="closeFilter" catchtouchmove="catchTouchMove"></view>
    <view class="drawer filter-drawer {{showFilterPopup ? 'drawer-show' : ''}}" catchtouchmove="catchTouchMove">
      <view class="drawer-header">
        <text class="drawer-title">筛选条件</text>
        <text class="iconfont icon-close" bindtap="closeFilter"></text>
      </view>
      <scroll-view class="drawer-body" scroll-y="{{true}}">
        <view class="filter-section">
          <text class="filter-title">岗位</text>
          <view class="filter-options">
            <text
              wx:for="{{tabs}}"
              wx:key="index"
              class="selector-item {{selectedPosition === item.value ? 'selector-item-active' : ''}}"
              data-pos="{{item}}"
              bindtap="selectPosition"
            >{{item.name}}</text>
          </view>
        </view>
        <view class="filter-section">
          <text class="filter-title">等级</text>
          <view class="filter-options">
            <text
              wx:for="{{levelData}}"
              wx:key="index"
              class="selector-item {{selectedLevel === item.id ? 'selector-item-active' : ''}}"
              data-level="{{item.id}}"
              bindtap="selectLevel"
            >{{item.name}}</text>
          </view>
        </view>
        <!-- <view class="filter-section">
          <text class="filter-title">时间</text>
          <view class="filter-options">
            <text
              wx:for="{{timeRanges}}"
              wx:key="index"
              class="selector-item {{selectedTimeRange === item ? 'selector-item-active' : ''}}"
              data-time="{{item}}"
              bindtap="selectTimeRange"
            >{{item}}</text>
          </view>
        </view> -->
        <view class="drawer-footer">
          <button class="reset-btn" bindtap="resetFilter">重置</button>
          <button class="apply-btn" bindtap="applyFilter">确定</button>
        </view>
      </scroll-view>
    </view>
  </view>


