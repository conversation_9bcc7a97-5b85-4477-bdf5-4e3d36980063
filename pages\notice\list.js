// pages/notice/list.js
const { getNoticeList } = require('../../api/home');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    noticeList: [],
    isLoading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '通知公告'
    });
    this.loadNoticeList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据
    this.refreshNoticeList();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshNoticeList();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadMoreNotices();
    }
  },

  /**
   * 加载通知列表
   */
  loadNoticeList() {
    if (this.data.isLoading) return;

    this.setData({
      isLoading: true
    });

    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize
    };

    getNoticeList(params)
      .then(result => {
        if (result && result.list) {
          // 处理富文本内容，转换为纯文本
          const processedList = result.list.map(item => ({
            ...item,
            plainContent: this.stripHtmlTags(item.content)
          }));

          this.setData({
            noticeList: processedList,
            hasMore: processedList.length >= this.data.pageSize,
            page: 1
          });
        }
      })
      .catch(error => {
        console.error('获取通知列表失败:', error);
        wx.showToast({
          title: '获取通知失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({
          isLoading: false
        });
        wx.stopPullDownRefresh();
      });
  },

  /**
   * 刷新通知列表
   */
  refreshNoticeList() {
    this.setData({
      page: 1,
      hasMore: true,
      noticeList: []
    });
    this.loadNoticeList();
  },

  /**
   * 加载更多通知
   */
  loadMoreNotices() {
    if (this.data.isLoading || !this.data.hasMore) return;

    this.setData({
      isLoading: true
    });

    const params = {
      page: this.data.page + 1,
      pageSize: this.data.pageSize
    };

    getNoticeList(params)
      .then(result => {
        if (result && result.list) {
          // 处理富文本内容，转换为纯文本
          const processedList = result.list.map(item => ({
            ...item,
            plainContent: this.stripHtmlTags(item.content)
          }));

          const newList = [...this.data.noticeList, ...processedList];
          this.setData({
            noticeList: newList,
            hasMore: processedList.length >= this.data.pageSize,
            page: this.data.page + 1
          });
        }
      })
      .catch(error => {
        console.error('加载更多通知失败:', error);
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({
          isLoading: false
        });
      });
  },

  /**
   * 查看通知详情
   */
  viewNoticeDetail(e) {
    const notice = e.currentTarget.dataset.notice;
    if (notice && notice.id) {
      wx.navigateTo({
        url: `/pages/notice-detail/notice-detail?id=${notice.id}`
      });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 富文本转纯文本
   * @param {string} richText - 富文本内容
   * @returns {string} - 纯文本内容
   */
  stripHtmlTags(richText) {
    if (!richText) return '';

    // 移除HTML标签
    let plainText = richText.replace(/<[^>]*>/g, '');

    // 解码HTML实体
    plainText = plainText
      .replace(/&nbsp;/g, ' ')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    // 移除多余的空白字符
    plainText = plainText.replace(/\s+/g, ' ').trim();

    return plainText;
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '通知公告',
      path: '/pages/notice/list'
    };
  }
});
