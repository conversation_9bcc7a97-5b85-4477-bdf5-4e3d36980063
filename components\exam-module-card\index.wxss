.certificate-card {
  background: white;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 36rpx rgba(0, 0, 0, 0.08);
  /* transition: all 0.3s ease; */
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 0;
  border: 2rpx solid rgba(0, 0, 0, 0.05);
  padding:30rpx 20rpx 30rpx 20rpx;

}

/* .certificate-card:active {
  transform: translateY(-6rpx);
  box-shadow: 0 20rpx 40rpx rgba(161, 140, 209, 0.12);
} */

.certificate-icon {
  height: 70rpx;
  width: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  /* transition: all 0.3s ease; */
  border:1px solid rgba(161, 140, 209, 0.1);
  background:linear-gradient(135deg, rgba(161, 140, 209, 0.1), rgba(251, 194, 235, 0.1));
  border-radius:10rpx;
}

.certificate-icon .iconfont {
  font-size: 30rpx;
  z-index: 1;
  /* transition: all 0.3s ease; */
  color:#8a70c2;
  /* color:#fff; */
}

.certificate-content {
  position: relative;
  z-index: 3;
  display: flex;
  /* flex-direction: column; */
  /* justify-content: center; */
  align-items: center;
  justify-content: space-between;
  flex-grow: 1;
  padding-left: 16rpx;
  flex:1;
}

.content-title {
  max-width: 65%; /* 限制宽度，为按钮留出空间 */
  padding-right: 10rpx; /* 右侧添加一些间距 */
}

.certificate-title {
  font-weight: bold;
  font-size: 30rpx;
  color: #333;
  word-break: break-word; /* 允许在任意字符间换行 */
  word-wrap: break-word; /* 允许长单词换行 */
  white-space: normal; /* 正常换行 */
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 最多显示2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.certificate-subtitle {
  color: #9989;
  font-size: 24rpx;
}


.cert-status-badge.status-approved {
  background: linear-gradient(135deg, rgba(129, 251, 184, 0.8), rgba(40, 199, 111, 0.8));
}

.cert-status-badge.status-pending {
  background: linear-gradient(135deg, rgba(255, 204, 137, 0.8), rgba(255, 153, 0, 0.8));
}

.cert-status-badge.status-rejected {
  background: linear-gradient(135deg, rgba(255, 154, 158, 0.8), rgba(255, 8, 68, 0.8));
}

.cert-status-badge.status-waiting {
  background: linear-gradient(135deg, rgba(129, 251, 184, 0.8), rgba(40, 199, 111, 0.8));
}

.cert-status-badge {
  position: absolute;
  right: 0;
  top: 0;
  width: fit-content;
  padding: 8rpx 20rpx;
  border-bottom-left-radius: 24rpx;
  font-size: 20rpx;
  font-weight: 600;
  z-index: 20;
  color:#fff;
}

.cert-status-badge .iconfont {
  margin-right: 6rpx;
  font-size: 20rpx;
}

.certificate-button {
  
  /* position: absolute;
  right: 0rpx;
  top:0px;
  bottom:0px; */
  /* display: flex; */
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
  font-size: 26rpx;
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.15);
  align-items: center;
  justify-content: center;
  height:fit-content;
}

.certificate-button .iconfont {
  margin-right: 10rpx;
  font-size: 26rpx;
}

/* .certificate-button:active {
  transform: translateY(-50%) scale(0.95);
} */

.certificate-button.take-exam-btn {
  background: linear-gradient(135deg, #8a70c2, #a18cd1);
  color: white;
}

.certificate-button.apply-exam-btn {
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
  color: white;
}

.certificate-button.view-cert-btn, .certificate-button.view-report-btn {
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.7), rgba(251, 194, 235, 0.7));
  color: white;
}

.certificate-button.view-report-btn {
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.7), rgba(251, 194, 235, 0.7));
  color: white;
}

.certificate-button.disabled-btn {
  background: linear-gradient(135deg, #d1d1d1, #b0b0b0);
  color: white;
  opacity: 0.8;
  pointer-events: none;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
} 