/* pages/profile/achievements/achievements.wxss */


/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  /* border-radius: 20rpx; */
  padding: 30rpx 0rpx;
  box-shadow: 0 8rpx 24rpx rgba(161, 140, 209, 0.3);
  padding-bottom:150rpx;
}

.user-info {
  display: flex;
  align-items: center;
  flex-direction: column;
  
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  /* margin-right: 24rpx; */
}
.user-avatar image{
  width:100%;
  height:100%;
  border-radius:50%;

}
.user-avatar .iconfont{
  font-size: 80rpx;
  color: rgba(255, 255, 255, 0.8);
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  text-align: center;
}

.user-name {
  font-size: 36rpx;
  line-height: 70rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
}

.user-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}
.user-subtitle .iconfont{
  display: inline-block;
}
/* .achievement-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.stat-divider {
  width: 2rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.3);
} */

/* 勋章列表卡片 */
.achievements-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  margin:20rpx;
  margin-top:-130rpx;
}

.card-header {
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.card-subtitle {
  font-size: 26rpx;
  color: #999;
}

/* 勋章网格 */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.achievement-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
}

.achievement-item:active {
  transform: scale(0.95);
  background-color: #f8f9fa;
}

.achievement-icon-wrapper {
  position: relative;
  margin-bottom: 16rpx;
}

.achievement-icon {
  width: 130rpx;
  height: 130rpx;
}
.gray{
  filter: grayscale(100%);
}

.achievement-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50rpx;
}

.achievement-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  line-height: 1.4;
}
.achievement-tit{
  font-size:22rpx;
  color:#999;
  line-height: 45rpx;
}
.achievement-item.not-obtained .achievement-name {
  color: #999;
}



/* 弹出层样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.modal-container {
  width: 100%;
  overflow: hidden;
}


.modal-close {
  font-size: 32rpx;
  color: #fff;
  padding: 10rpx;
  margin:100rpx auto;
  text-align: center;
}
.modal-close .iconfont{
  border:2px solid #fff;
  border-radius:50%;
  padding:10rpx;
  font-size: 32rpx;

}

.modal-content {
}

/* 勋章详情 */
.achievement-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.detail-icon-wrapper {
  position: relative;
  margin-bottom: 30rpx;
}

.detail-icon {
  width: 360rpx;
  height: 360rpx;
}

.detail-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 80rpx;
}


.detail-name {
  font-size: 36rpx;
  color: #fff;
  margin-bottom: 16rpx;
  text-align: center;
}

.detail-description {
  font-size: 28rpx;
  color: #fff;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.detail-info {
  width: 100%;
  text-align: center;
  background: linear-gradient(135deg,transparent 0%, #a18cd1 40%, #fbc2eb 70%,transparent 100%);
}

.info-section {
  margin-bottom: 30rpx;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-label {
  display: block;
  font-size: 26rpx;
  color: #fff;
  margin-bottom: 12rpx;
}

.info-content {
  display: block;
  font-size: 28rpx;
  color: #fff;
  line-height: 1.5;
}

.not-obtained-text {
  color: #ff6b6b;
}
