Component({
  properties: {
    messages: {
      type: Array,
      value: []
    },
    isRecording: {
      type: Boolean,
      value: false
    },
    scrollTop: {
      type: Number,
      value: 0
    },
    totalNavHeight: {
      type: Number,
      value: 0
    },
    keyboardHeight: {
      type: Number,
      value: 0
    },
    hasMore: {
      type: Boolean,
      value: false
    },
    isLoading: {
      type: Boolean,
      value: false
    },
    pageType: {
      type: String,
      value: 'exam' // 'exam' 或 'practice'
    },
    isPracticeStarted: {
      type: Boolean,
      value: false
    },
    isNextBtnDisabled: {
      type: Boolean,
      value: false
    },
    // 记录详情
    isRecordDetail: {
      type: Boolean,
      value: false
    },
    loadingText: {
      type: String,
      value: '思考中'
    },
    examFinished: {
      type: Boolean,
      value: false
    },
    examFinishedText: {
      type: String,
      value: '答题已结束请提交试卷'
    },
    showGetQuestionTip: {
      type: <PERSON>olean,
      value: false
    },
    QuestionTipText: {
      type: String,
      value: '获取试题失败，请重新获取'
    }
  },
  
  methods: {
    onScrollToUpper() {
      this.triggerEvent('scrollToUpper');
    },
    
    onPlayMessageAudio(e) {
      const detail = e.detail;
      this.triggerEvent('playMessageAudio', detail);
    },
    
    onStartPractice() {
      this.triggerEvent('startPractice');
    },
    
    onGetQuestion() {
      this.triggerEvent('getQuestion');
    },
    

    onGetNextQuestion() {
      this.triggerEvent('getNextQuestion');
    },

    // 滚动到底部方法，供父组件调用
    scrollToBottom() {
      const query = this.createSelectorQuery();
      query.select('.chat-list').boundingClientRect(rect => {
        if (rect) {
          this.setData({
            scrollTop: rect.height
          });
        }
      }).exec();
    }
  }
}) 