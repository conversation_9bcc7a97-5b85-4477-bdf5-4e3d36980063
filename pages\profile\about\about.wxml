<!--pages/profile/about.wxml-->
<view class="container">
  <view class="about-container">
    <!-- 应用信息头部 -->
    <view class="app-header">
      <view class="app-logo">
        <text class="iconfont icon-utensils"></text>
      </view>
      <text class="app-name">餐烤餐考</text>
      <!-- <text class="app-version">版本 1.0.0</text> -->
      <text class="app-slogan">
        专业的餐饮培训考核平台，助力员工技能提升和职业发展
      </text>
    </view>
    
    <!-- 核心功能 -->
    <view class="section-card">
      <view class="section-header">
        <text class="iconfont icon-star"></text>
        <text>核心功能</text>
      </view>
      <view class="section-content">
        <view class="feature-list">
          <view wx:for="{{features}}" wx:key="index">
            <view class="feature-item">
              <view class="feature-icon iconfont {{item.gradient}}">
                <text class="iconfont {{item.icon}}"></text>
              </view>
              <view class="feature-name">{{item.name}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 团队介绍 -->
    <!-- <view class="section-card">
      <view class="section-header">
        <text class="iconfont icon-team"></text>
        <text>开发团队</text>
      </view>
      <view class="section-content">
        <view class="team-list">
          <view class="team-member">
            <image class="member-avatar" src="/static/images/avatar1.png" mode="aspectFill"></image>
            <view class="member-info">
              <text class="member-name">王总监</text>
              <text class="member-role">产品负责人</text>
            </view>
          </view>
          <view class="team-member">
            <image class="member-avatar" src="/static/images/avatar2.png" mode="aspectFill"></image>
            <view class="member-info">
              <text class="member-name">李设计</text>
              <text class="member-role">UI/UX设计师</text>
            </view>
          </view>
          <view class="team-member">
            <image class="member-avatar" src="/static/images/avatar3.png" mode="aspectFill"></image>
            <view class="member-info">
              <text class="member-name">张工程</text>
              <text class="member-role">前端开发</text>
            </view>
          </view>
          <view class="team-member">
            <image class="member-avatar" src="/static/images/avatar4.png" mode="aspectFill"></image>
            <view class="member-info">
              <text class="member-name">赵技术</text>
              <text class="member-role">后端开发</text>
            </view>
          </view>
        </view>
      </view>
    </view> -->
    
    <!-- 联系方式 -->
    <!-- <view class="section-card">
      <view class="section-header">
        <text class="iconfont icon-mail"></text>
        <text>联系我们</text>
      </view>
      <view class="section-content">
        <view class="contact-item">
          <view class="contact-icon">
            <text class="iconfont icon-mail"></text>
          </view>
          <view class="contact-text">
            <text class="contact-label">电子邮箱</text>
            <text class="contact-value"><EMAIL></text>
          </view>
        </view>
        <view class="contact-item">
          <view class="contact-icon">
            <text class="iconfont icon-phone"></text>
          </view>
          <view class="contact-text">
            <text class="contact-label">客服电话</text>
            <text class="contact-value">************</text>
          </view>
        </view>
        <view class="contact-item">
          <view class="contact-icon">
            <text class="iconfont icon-location"></text>
          </view>
          <view class="contact-text">
            <text class="contact-label">公司地址</text>
            <text class="contact-value">北京市朝阳区科技园区8号楼</text>
          </view>
        </view>
      </view>
    </view> -->
    
    <!-- 版权信息 -->
    <!-- <view class="copyright">
      <text>© 2023 餐烤餐考 All Rights Reserved</text>
      <text>天津云顶云科技有限公司 版权所有</text>
    </view> -->
  </view>
</view>