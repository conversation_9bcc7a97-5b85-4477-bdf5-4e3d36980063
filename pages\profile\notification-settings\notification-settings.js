import { updateNotificationSettings } from '../../../api/user';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    notificationSettings: [
      {
        type: 'system',
        title: '系统通知',
        description: '包括账号、安全等相关通知',
        enabled: true,
        icon: 'icon-notice'
      },
      {
        type: 'activity',
        title: '活动提醒',
        description: '新活动、促销等相关通知',
        enabled: true,
        icon: 'icon-activity'
      },
      {
        type: 'exam',
        title: '考试提醒',
        description: '考试开始、结束等相关通知',
        enabled: true,
        icon: 'icon-exam'
      },
      {
        type: 'practice',
        title: '练习提醒',
        description: '新练习、推荐练习等相关通知',
        enabled: true,
        icon: 'icon-practice'
      }
    ],
    hasChanged: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取用户当前的通知设置
    this.fetchNotificationSettings();
  },

  /**
   * 获取用户当前的通知设置
   */
  fetchNotificationSettings() {
    // 这里应该调用API获取用户当前设置
    // 目前使用模拟数据
    wx.showLoading({
      title: '加载中'
    });

    // 模拟API请求
    setTimeout(() => {
      wx.hideLoading();
      // 实际项目中应该从后端获取设置
      // const settings = await getUserSettings();
      // this.setData({
      //   notificationSettings: settings.notifications
      // });
    }, 500);
  },

  /**
   * 切换通知开关
   */
  toggleNotification(e) {
    const { type } = e.currentTarget.dataset;
    const { checked } = e.detail;
    
    const notificationSettings = this.data.notificationSettings.map(item => {
      if (item.type === type) {
        return { ...item, enabled: checked };
      }
      return item;
    });
    
    this.setData({
      notificationSettings,
      hasChanged: true
    });
  },

  /**
   * 保存设置
   */
  async saveSettings() {
    if (!this.data.hasChanged) {
      wx.navigateBack();
      return;
    }

    wx.showLoading({
      title: '保存中'
    });

    try {
      // 转换为API需要的格式
      const settings = this.data.notificationSettings.reduce((acc, curr) => {
        acc[curr.type] = curr.enabled;
        return acc;
      }, {});

      // 调用API保存设置
      await updateNotificationSettings(settings);
      
      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error('保存设置失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '餐考通知设置',
      path: '/pages/profile/notification-settings'
    };
  }
}) 