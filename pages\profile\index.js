// pages/profile/index.js
// 从uniapp的pages/profile/index.vue转换而来

// 导入API
const { getUserInfo, getUserSkillRadar ,checkIdentityVerification} = require('../../api/user')
const {  getPracticeStatistic } = require('../../api/home')

import request from '../../utils/request';

const authBehavior = require('../../behaviors/auth-behavior')

Page({
  // 使用授权行为
  behaviors: [authBehavior],

  // 页面的初始数据
  data: {
    nextPath:'pages/profile/index',
    statistic:{
      minutes: 0,
      count: 0,
      obtained:0,
    },
    radarData: {
      categories: [
        '努力程度',
        '成长意愿',
        '掌握程度',
        '目标达成',
        '晋升轨迹',
        '学习稳定'
      ],
      series: [{
        name: '当前水平',
        data: [0, 0, 0, 0, 0, 0]
      }, 
      // {
      //   name: '上次评估',
      //   data: [0, 0, 0, 0, 0, 0]
      // }
    ]
    },
    radarOpts: {
      padding: [15, 15, 0, 15],
      dataLabel: false,
      legend: false,
      extra: {
        radar: {
          gridType: 'radar',
          gridColor: '#CCCCCC',
          gridCount: 5,
          max: 100,
          labelColor: '#666666',
          radarStyle: 'radar'
        }
      }
    },
    menuCategories: [
      {
        title: '企业管理',
        items: [
          {
            title: '申请加入企业',
            icon: 'icon-usertie',
            url: '/pages/profile/join-enterprise/join-enterprise',
            type: 'join-enterprise',
            isShow:this.data.showJoinCompany
          },
          // {
          //   title: '员工履历',
          //   icon: 'icon-qyll',
          //   url: '/pages/profile/company-resume/company-resume',
          //   type:'company-resume'
          // },
        ]
      },
      {
        title: '学习与成长',
        items: [
          {
            title: '练习记录',
            icon: 'icon-list-ol',
            url: '/pages/profile/practice-records/practice-records',
            type:'practice-records',
            isShow:true,
          },
          {
            title: '考试记录',
            icon: 'icon-trophy',
            url: '/pages/profile/exam-records/exam-records',
            type:'exam-records',
            isShow:true,

          },
          {
            title: '我的证书',
            icon: 'icon-certificate',
            url: '/pages/profile/my-certificates/my-certificates',
            type:'my-certificates',
            isShow:true,

          },
          {
            title: '成就勋章',
            icon: 'icon-award',
            url: '/pages/profile/achievements/achievements',
            type:'achievements',
            isShow:true,

          }
        ]
      },
      {
        title: '设置与帮助',
        items: [
          // {
          //   title: '通知设置',
          //   icon: 'icon-bell1',
          //   url: '/pages/profile/notification-settings/notification-settings'
          // },
          {
            title: '意见反馈',
            icon: 'icon-comment',
            url: '/pages/profile/feedback/feedback',
            isShow:true,
          },
          // {
          //   title: '关于我们',
          //   icon: 'icon-info',
          //   url: '/pages/profile/about/about'
          // }
        ]
      }
    ],
    isLoading: false,
    showJoinCompany:true,
  },

  // 生命周期函数
  onLoad: function() {
    this.loadUserData();
  },

  onShow: function() {
     // 每次页面显示时，重新检查授权状态
     this.checkAuthStatus();
     this.drawRadarChart()
    if(wx.getStorageSync('isAuthorized')){
      this.updateUserInfo()
      this.fetchUserSkillRadar()
       // 获取用户统计数据
      getPracticeStatistic().then(res=>{
        this.setData({
          statistic: {...res.total,obtained:res.certificates.obtained},
        })
      })
    }
   
    // 更新TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: wx.getStorageSync('selectedTab') });
      if(this.data.isIdentityVerified){
        this.getTabBar().setData({
          list:this.data.allTabs
        });
      }
    }
   
  },

  // 页面准备就绪
  onReady: function() {
    // 页面初次渲染完成后，绘制雷达图
    // this.drawRadarChart();
  },

  onHide: function() {
    // 当用户离开当前tab时，清除授权状态
    this.clearAuthStatus();
  },
  getIdentityVerification(){

    checkIdentityVerification({openId:wx.getStorageSync('openId')}).then(res=>{
      if(res.isBound){
        this.setData({
          showJoinCompany:false
        })
      }
    })
    },

  // 加载用户数据
  loadUserData: function() {
    this.setData({
      isLoading: true
    });

    if (this.data.isAuthorized) { 
      // 并行获取用户基础信息和技能雷达图
    Promise.all([
      this.updateUserInfo(),
      this.fetchUserSkillRadar(),
      this.drawRadarChart(),
      this.getIdentityVerification()
    ])
    .catch(error => {
      console.error('加载用户数据失败：', error);
      wx.showToast({
        title: '加载用户数据失败',
        icon: 'none'
      });
    })
    .finally(() => {
      this.setData({
        isLoading: false
      });
    });
    }
  },

  // 获取用户基本信息
  fetchUserInfo: function() {
    return getUserInfo( wx.getStorageSync('openId'))
      .then(result => {
        if (result) {
          let avatar = result.user.avatar?request.config.imgURL+result.user.avatar:''
          this.setData({
            userInfo: {
              // ...this.data.userInfo,
              ...result.user,
              avatar:avatar,
              realName:result.user.realName||result.user.nickname,
            },
            currentPosition:result.user.positionName,
            currentLevel:result.user.levelName||'无',
          });
          // 保存用户信息到本地
          wx.setStorageSync('userInfo', result.user);
        }
      })
      .catch(error => {
        console.error('获取用户信息失败：', error);

      });
  },

  onAuthClose: function(e){
    this.setData({
      [e.detail.type]: false
    })
    this.fetchUserSkillRadar()
    this.drawRadarChart()
  },

  // 获取用户技能雷达图数据
  fetchUserSkillRadar: function() {
    return getUserSkillRadar()
      .then(result => {
        if (result) {
          // 返回数据格式
          // {"code":200,"data":{"questionCount":{"score":47,"current":21,"max":44,"description":"努力程度"},"questionCount2":{"score":47,"current":21,"max":44,"description":"成长意愿"},"correctRate":{"score":1,"current":3,"total":289,"rate":1.0380622837370241,"description":"掌握程度"},"examPassRate":{"score":25,"passed":1,"total":4,"rate":25,"description":"目标能力"},"certificateCompletion":{"score":0,"obtained":0,"required":2,"rate":0,"description":"晋升轨迹"},"continuousLearning":{"score":200,"learningDays":2,"totalDays":1,"description":"学习稳定"}},"message":"获取维度数据成功"}
          // 将数据遍历获取其中的description score
          let radarData = [],categories=[],data=[];
          Object.keys(result).forEach(key => {
            // radarData[result[key].description] = result[key].score;
            radarData.push({
              name:result[key].description,
              score:result[key].score
            })
          });
          radarData.map(item=>{
            categories.push(item.name)
            data.push(item.score>100?100:item.score)
          })
          // 更新 this.data.radarData 中的 categories
          this.setData({
            radarData:{
              ...this.data.radarData,
              categories:categories,
              series:[{
                name:'当前水平',
                data:data
              }]
            }
          },()=>{
            this.drawRadarChart()
          })
          

          
          // 更新雷达图数据
          // let radarData = {...this.data.radarData};
          // radarData.series[0].data = result.current;
          // radarData.series[1].data = result.previous;

          // this.setData({
          //   radarData: radarData
          // }, () => {
          //   // 数据更新后绘制雷达图
          //   this.drawRadarChart();
          // });
        }
      })
      .catch(error => {
        console.error('获取用户技能雷达图失败：', error);
      });
  },

  // 页面跳转
  navigateToUrl: function(e) {
    const url = e.currentTarget.dataset.url;
    const type = e.currentTarget.dataset.type;

    // 特殊处理某些功能
    // if(type == 'company-resume'){
    //    wx.showToast({
    //     title:'功能开发中，敬请期待……',
    //     icon:'none'
    //   })
    // } else
     if(type == 'join-enterprise') {
      // 申请加入企业页面直接跳转，不需要授权检查
      this.goToJoin()
    } else {
      // 其他页面使用授权导航
      this.navigateToAuthRequiredPage(url);
    }
  },
  // 授权弹框
  gotoAuth(){
    this.checkAuthStatus()
  },  

  // Tab项被点击时触发
  onTabItemTap: function(item) {
    // 更新选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: wx.getStorageSync('selectedTab') });
    }
  }
})
