// pages/practice/detail.js
import { 
  getPracticeQuestion, 
  evaluatePracticeAnswer 
} from '../../../api/practice';
import { getFormattedTime, getFormattedTimeWithSeconds } from '../../../utils/dateFormat';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    practiceId: '', // 练习ID
    title: '', // 练习标题
    startTime: null, // 练习开始时间
    timer: null, // 计时器
    formatTime: '00:00', // 格式化的时间显示
    scrollTop: 0, // 滚动位置
    keyboardHeight: 0, // 键盘高度
    isVoiceMode: false, // 是否为语音模式
    isRecording: false, // 是否正在录音
    isCancelling: false, // 是否正在取消录音
    inputContent: '', // 输入内容
    showSystemMessage: true, // 是否显示系统消息
    hasMore: false, // 是否有更多历史消息
    messages: [], // 消息列表
    isLoading: false, // 是否正在加载
    currentQuestion: null, // 当前题目信息
    questionId: '', // 当前题目ID
    question: '', // 当前题目内容
    standardAnswer: '', // 标准答案
    userId: '', // 用户ID，实际应从全局获取
    completedQuestions: 0, // 已完成题目数量
    correctCount: 0, // 正确答题数量
    recordingTime: '00:00', // 录音时间
    recordTimer: null, // 录音计时器
    startY: 0, // 触摸开始位置Y坐标
    recorderManager: null, // 录音管理器
    innerAudioContext: null, // 音频播放器
    isPlaying: false, // 是否正在播放语音
    tempFilePath: '' // 临时录音文件路径
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userId = wx.getStorageSync('openId') || ''; // 获取用户ID

    this.setData({
      title: options.title || '练习详情',
      practiceId: options.id || '',
      userId: userId
    });
    
    // 初始化录音管理器
    this.initRecorderManager();
    
    // 开始计时器
    this.startTimer();

    // 添加系统消息
    this.setData({
      messages: [
        {
          type: 'system',
          content: '练习已开始，请根据提示完成练习内容',
          time: getFormattedTime()
        }
      ]
    });
    
    // 获取第一道题目
    this.getNextQuestion();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.clearTimer();
    
    // 释放音频资源
    if (this.innerAudioContext) {
      this.innerAudioContext.stop();
      this.innerAudioContext.destroy();
    }
    
    // 停止录音
    if (this.recorderManager && this.data.isRecording) {
      this.recorderManager.stop();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取下一道题目
  async getNextQuestion() {
    // if (!this.data.practiceId) return;
    
    this.setData({ isLoading: true });
    
    try {
      const params = {
        // practiceId: this.data.practiceId,
        // userId: this.data.userId
        inputs:{
          // difficulty_level: 'easy',
          // fenduan:"ss",
          file_id:"ss",
          level:"简单",
        },
        // response_mode: "streaming",
        user: "<EMAIL>"
      };
      
      const result = await getPracticeQuestion(params);
      if (result) {
        const { outputs, id } = result;
        
        let text = outputs.text;
        let question = '';
        let standardAnswer = '';

        // 正则表达式匹配
        const questionMatch = text.match(/question:"([^"]+)"/);
        const answerMatch = text.match(/answer:"([^"]+)"/);
        
        if (questionMatch && questionMatch[1]) {
          question = questionMatch[1];
        }
        
        if (answerMatch && answerMatch[1]) {
          standardAnswer = answerMatch[1];
        }
        
        // 保存题目信息
        this.setData({
          currentQuestion: outputs,
          questionId: id,
          question: question,
          standardAnswer: standardAnswer
        });
        
        // 添加AI消息
        const aiMessage = {
          type: 'ai',
          content: question,
          time: getFormattedTime()
        };
        
        this.setData({
          messages: [...this.data.messages, aiMessage]
        });
        
        this.scrollToBottom();
        
        // 将问题转换为语音并播放
        this.textToSpeech(question);
      }
    } catch (error) {
      console.error('获取题目失败:', error);
      wx.showToast({
        title: '获取题目失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },
  
  // 加载消息记录
  async loadMessages() {
    // 我们不再从服务器加载历史消息，而是直接获取题目
    this.getNextQuestion();
  },
  
  startTimer() {
    const startTime = Date.now();
    this.setData({
      startTime: startTime
    });
    
    const timer = setInterval(() => {
      const diff = Math.floor((Date.now() - this.data.startTime) / 1000);
      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');
      const seconds = (diff % 60).toString().padStart(2, '0');
      const formatTime = `${minutes}:${seconds}`;
      
      this.setData({
        formatTime: formatTime
      });
    }, 1000);
    
    this.timer = timer;
  },
  
  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  
  goBack() {
    wx.navigateBack();
  },
  
  toggleInputType() {
    this.setData({
      isVoiceMode: !this.data.isVoiceMode
    });
    
    if (this.data.isVoiceMode) {
      wx.hideKeyboard();
    }
  },
  
  onInputFocus(e) {
    this.setData({
      keyboardHeight: e.detail.height || 0
    });
  },
  
  onInputBlur() {
    this.setData({
      keyboardHeight: 0
    });
  },
  
  onScroll(e) {
    this.setData({
      scrollTop: e.detail.scrollTop
    });
  },
  
  onScrollToUpper() {
    // 暂不实现加载更多历史消息
  },
  
  // 发送用户回答并获取评估
  async sendMessage() {
    if (!this.data.inputContent.trim()) {
      wx.showToast({
        title: '请输入回答内容',
        icon: 'none'
      });
      return
    }
    if (!this.data.questionId || !this.data.question || !this.data.standardAnswer) {
      wx.showToast({
        title: '未获取到题目，请重试',
        icon: 'none'
      });
      return;
    }
    
    // 构造用户消息
    const userMessage = {
      type: 'user',
      content: this.data.inputContent,
      time: getFormattedTime()
    };

    // 添加消息到列表
    const newMessages = [...this.data.messages, userMessage];
    
    // 清空输入框并滚动到底部
    const userAnswer = this.data.inputContent;
    this.setData({
      messages: newMessages,
      inputContent: '',
      isLoading: true
    });
    
    this.scrollToBottom();

    try {
      // 发送用户回答到Dify评估工作流
      const params = {
        inputs:{
        // questionId: this.data.questionId,
        question: this.data.question,
        answer_yh:this.data.inputContent,
        tip:this.data.standardAnswer,
        // standardAnswer: this.data.standardAnswer,
        // userAnswer: userAnswer,
        // userId: this.data.userId
        },
        user: "<EMAIL>"
      };
      
      const result = await evaluatePracticeAnswer(params);
      
      if (result) {
        const { outputs,id } = result;
        
        // 添加AI回复
        const aiReply = {
          type: 'ai',
          content: outputs.text,
          time: getFormattedTime(),
          // evaluation: evaluation,
          // score: score,
          // isCorrect: isCorrect
        };
        
        // 更新统计数据
        this.setData({
          messages: [...this.data.messages, aiReply],
          completedQuestions: this.data.completedQuestions + 1,
          // correctCount: isCorrect ? this.data.correctCount + 1 : this.data.correctCount
          correctCount: this.data.correctCount + 1
        });
        
        this.scrollToBottom();
        
        // 将反馈转换为语音并播放
        this.textToSpeech(outputs.text);
        
        // 延迟一段时间后，获取下一道题目
        setTimeout(() => {
          this.getNextQuestion();
        }, 5000); // 等待5秒，让用户有时间查看和听取反馈
      }
    } catch (error) {
      console.error('评估回答失败:', error);
      wx.showToast({
        title: '评估失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },
  
  scrollToBottom() {
    setTimeout(() => {
      const query = wx.createSelectorQuery();
      query.select('.chat-list').boundingClientRect(data => {
        if (data) {
          this.setData({
            scrollTop: data.height
          });
        }
      }).exec();
    }, 100);
  },
  
  // 初始化录音管理器
  initRecorderManager() {
    // 创建录音管理器实例
    const recorderManager = wx.getRecorderManager();
    
    // 设置录音结束事件处理
    recorderManager.onStop((res) => {
      if (this.data.isCancelling) {
        this.setData({
          isRecording: false,
          isCancelling: false
        });
        return;
      }
      
      const { tempFilePath } = res;
      console.log('录音文件：', tempFilePath);
      
      // 保存录音文件路径
      this.setData({
        inputContent: '[语音消息]',
        tempFilePath: tempFilePath
      });
      
      // 创建音频上下文播放录音
      const innerAudioContext = wx.createInnerAudioContext();
      innerAudioContext.src = tempFilePath;
      
      // 保存音频上下文
      this.innerAudioContext = innerAudioContext;
      
      // 发送录音消息
      this.sendVoiceMessage(tempFilePath);
    });
    
    // 设置录音错误事件处理
    recorderManager.onError((res) => {
      console.error('录音错误：', res);
      this.setData({
        isRecording: false,
        isCancelling: false
      });
      wx.showToast({
        title: '录音失败，请重试',
        icon: 'none'
      });
    });
    
    // 监听录音开始事件
    recorderManager.onStart(() => {
      console.log('录音开始');
      this.startRecordTimer();
    });
    
    this.recorderManager = recorderManager;
  },
  
  // 开始录音计时
  startRecordTimer() {
    // 清除可能存在的计时器
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }
    
    const startTime = Date.now();
    const recordTimer = setInterval(() => {
      const diff = Math.floor((Date.now() - startTime) / 1000);
      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');
      const seconds = (diff % 60).toString().padStart(2, '0');
      const recordingTime = `${minutes}:${seconds}`;
      
      this.setData({
        recordingTime: recordingTime
      });
      
      // 如果录音时长超过60秒，自动停止
      if (diff >= 60) {
        this.stopRecording();
      }
    }, 1000);
    
    this.setData({
      recordTimer: recordTimer
    });
  },
  
  // 停止录音计时
  stopRecordTimer() {
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
      this.setData({
        recordTimer: null,
        recordingTime: '00:00'
      });
    }
  },
  
  // 手指触摸移动事件
  onTouchMove(e) {
    if (!this.data.isRecording) return;
    
    // 获取当前触摸点的Y坐标
    const currentY = e.touches[0].clientY;
    // 计算移动距离
    const moveDistance = this.data.startY - currentY;
    
    // 如果向上移动超过50像素，标记为取消状态
    if (moveDistance > 50) {
      if (!this.data.isCancelling) {
        this.setData({
          isCancelling: true
        });
      }
    } else {
      if (this.data.isCancelling) {
        this.setData({
          isCancelling: false
        });
      }
    }
  },
  
  startRecording(e) {
    // 获取触摸的起始Y坐标
    const startY = e.touches[0].clientY;
    this.setData({
      startY: startY,
      isRecording: true,
      isCancelling: false
    });
    
    // 获取录音权限
    wx.authorize({
      scope: 'scope.record',
      success: () => {
        // 开始录音
        this.recorderManager.start({
          duration: 60000, // 最长录音时间，单位ms
          sampleRate: 16000, // 采样率
          numberOfChannels: 1, // 录音通道数
          encodeBitRate: 48000, // 编码码率
          format: 'mp3' // 音频格式
        });
      },
      fail: () => {
        this.setData({
          isRecording: false
        });
        
        wx.showModal({
          title: '提示',
          content: '需要您授权录音权限',
          confirmText: '去授权',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            }
          }
        });
      }
    });
  },
  
  stopRecording() {
    if (!this.data.isRecording) return;
    
    // 停止录音计时
    this.stopRecordTimer();
    
    // 如果正在取消录音，则不发送
    if (this.data.isCancelling) {
      this.recorderManager.stop();
      return;
    }
    
    // 停止录音
    this.recorderManager.stop();
    // 关闭弹框
    this.setData({
      isRecording: false
    });
  },
  
  cancelRecording() {
    if (!this.data.isRecording) return;
    
    // 停止录音计时
    this.stopRecordTimer();
    
    // 设置取消状态并停止录音
    this.setData({
      isCancelling: true
    });
    
    this.recorderManager.stop();
    
    wx.showToast({
      title: '已取消录音',
      icon: 'none'
    });
  },

  /**
   * 文本转语音
   * @param {string} text - 要转换为语音的文本
   */
  textToSpeech(text) {
    if (!text || this.data.isPlaying) return;
    
    // 设置正在播放状态
    this.setData({
      isPlaying: true
    });
    
    // 创建内部音频上下文
    const innerAudioContext = wx.createInnerAudioContext();
    
    // 设置音量为最大值
    innerAudioContext.volume = 1.0;
    
    // 音频播放开始事件
    innerAudioContext.onPlay(() => {
      console.log('音频播放开始');
      this.setData({
        isPlaying: true
      });
    });
    
    // 音频播放结束事件
    innerAudioContext.onEnded(() => {
      console.log('音频播放结束');
      this.setData({
        isPlaying: false
      });
    });
    
    // 音频播放错误事件
    innerAudioContext.onError((res) => {
      console.error('音频播放错误:', res);
      this.setData({
        isPlaying: false
      });
      
      // wx.showToast({
      //   title: '音频播放失败: ' + res.errMsg,
      //   icon: 'none'
      // });
    });
    
    // 监听音频加载中事件
    innerAudioContext.onWaiting(() => {
      console.log('音频加载中...');
    });
    
    // 监听音频可以播放事件
    innerAudioContext.onCanplay(() => {
      console.log('音频可以播放');
    });
    
    // 设置音频源
    innerAudioContext.src = text;
    
    // 确保设置了音频源后再播放
    setTimeout(() => {
      console.log('开始播放音频:', innerAudioContext.src);
      innerAudioContext.play();
    }, 100);
  },
  
  // 测试音频播放
  testAudioPlay() {
    // 播放一段测试音频
    const testText = "这是一段测试语音，如果您能听到这段语音，说明语音播放功能正常。";
    
    wx.showToast({
      title: '正在播放测试语音',
      icon: 'none',
      duration: 2000
    });
    
    // 停止当前正在播放的音频
    this.textToSpeech('');
    
    // 播放测试音频
    this.textToSpeech(testText);
  },
  
  // 发送语音消息
  sendVoiceMessage(tempFilePath) {
    if (!tempFilePath) return;
    
    // 构造语音消息
    const message = {
      type: 'voice',
      content: tempFilePath,
      time: getFormattedTime(),
      isSelf: true
    };
    
    // 添加到消息列表
    const messages = this.data.messages;
    messages.push(message);
    
    this.setData({
      messages: messages,
      inputContent: ''
    });
    
    // 滚动到底部
    this.scrollToBottom();
    
    // 这里可以添加AI回复逻辑
    setTimeout(() => {
      this.addAIReply('我收到了您的语音消息');
    }, 1000);
  },
  
  // 添加AI回复
  addAIReply(content) {
    const messages = this.data.messages;
    
    // 构造AI回复消息
    const aiMessage = {
      type: 'text',
      content: content,
      time: getFormattedTime(),
      isSelf: false
    };
    
    messages.push(aiMessage);
    
    this.setData({
      messages: messages,
      isLoading: false
    });
    
    // 滚动到底部
    this.scrollToBottom();
  },
  
  // 播放语音消息
  playVoice(e) {
    const filePath = e.currentTarget.dataset.filepath;
    if (!filePath) return;
    
    // 创建音频上下文
    const innerAudioContext = wx.createInnerAudioContext();
    innerAudioContext.src = filePath;
    
    // 设置音频播放开始事件
    innerAudioContext.onPlay(() => {
      console.log('语音消息播放开始');
      this.setData({
        isPlaying: true
      });
    });
    
    // 设置音频播放结束事件
    innerAudioContext.onEnded(() => {
      console.log('语音消息播放结束');
      this.setData({
        isPlaying: false
      });
      
      // 播放结束后销毁实例
      innerAudioContext.destroy();
    });
    
    // 设置音频播放错误事件
    innerAudioContext.onError((res) => {
      console.error('语音消息播放错误:', res);
      this.setData({
        isPlaying: false
      });
      
      wx.showToast({
        title: '语音播放失败',
        icon: 'none'
      });
      
      // 错误时销毁实例
      innerAudioContext.destroy();
    });
    
    // 播放音频
    innerAudioContext.play();
    
    // 保存当前音频上下文
    this.innerAudioContext = innerAudioContext;
  }
});