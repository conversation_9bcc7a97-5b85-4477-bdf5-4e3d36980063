// 证书详情弹框组件使用示例
Page({
  data: {
    // 弹框显示状态
    showCertPopup: false,
    
    // 证书图片URL（示例图片）
    certificateUrl: '',
    
    // 弹框配置
    popupTitle: '证书详情',
    showActions: false,
    showShareButton: false,
    showSaveButton: true,
    maskClosable: true,
    loading: false,
    error: ''
  },

  /**
   * 显示基础弹框示例
   */
  showBasicExample() {
    this.setData({
      certificateUrl: 'https://example.com/certificate.jpg', // 替换为实际的证书图片URL
      popupTitle: '证书详情',
      showActions: false,
      showShareButton: false,
      showSaveButton: true,
      loading: false,
      error: '',
      showCertPopup: true
    });
  },

  /**
   * 显示带操作按钮的弹框
   */
  showWithActions() {
    this.setData({
      certificateUrl: 'https://example.com/certificate.jpg',
      popupTitle: '证书详情',
      showActions: true,
      showShareButton: true,
      showSaveButton: true,
      loading: false,
      error: '',
      showCertPopup: true
    });
  },

  /**
   * 显示自定义标题弹框
   */
  showCustomTitle() {
    this.setData({
      certificateUrl: 'https://example.com/certificate.jpg',
      popupTitle: '我的专业技能证书',
      showActions: true,
      showShareButton: true,
      showSaveButton: true,
      loading: false,
      error: '',
      showCertPopup: true
    });
  },

  /**
   * 显示加载状态示例
   */
  showLoadingExample() {
    this.setData({
      certificateUrl: '',
      popupTitle: '证书详情',
      showActions: false,
      showShareButton: false,
      showSaveButton: true,
      loading: true,
      error: '',
      showCertPopup: true
    });

    // 模拟加载完成
    setTimeout(() => {
      this.setData({
        certificateUrl: 'https://example.com/certificate.jpg',
        loading: false
      });
    }, 2000);
  },

  /**
   * 显示错误状态示例
   */
  showErrorExample() {
    this.setData({
      certificateUrl: '',
      popupTitle: '证书详情',
      showActions: false,
      showShareButton: false,
      showSaveButton: true,
      loading: false,
      error: '图片加载失败，请重试',
      showCertPopup: true
    });
  },

  /**
   * 关闭弹框
   */
  onCloseCertPopup() {
    console.log('关闭证书详情弹框');
    this.setData({
      showCertPopup: false
    });
  },

  /**
   * 分享证书
   */
  onShareCert(e) {
    console.log('分享证书:', e.detail);
    wx.showToast({
      title: '分享功能演示',
      icon: 'success',
      duration: 2000
    });
  },

  /**
   * 保存成功
   */
  onSaveSuccess(e) {
    console.log('保存成功:', e.detail);
    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 2000
    });
  },

  /**
   * 保存失败
   */
  onSaveFail(e) {
    console.log('保存失败:', e.detail);
    wx.showToast({
      title: '保存失败',
      icon: 'error',
      duration: 2000
    });
  },

  /**
   * 下载失败
   */
  onDownloadFail(e) {
    console.log('下载失败:', e.detail);
    wx.showToast({
      title: '下载失败',
      icon: 'error',
      duration: 2000
    });
  },

  /**
   * 长按事件
   */
  onLongPress(e) {
    console.log('长按事件:', e.detail);
    wx.showToast({
      title: '长按事件触发',
      icon: 'none',
      duration: 1000
    });
  },

  /**
   * 图片点击
   */
  onImageTap(e) {
    console.log('图片点击:', e.detail);
    wx.showToast({
      title: '图片点击事件',
      icon: 'none',
      duration: 1000
    });
  },

  /**
   * 图片加载完成
   */
  onImageLoad() {
    console.log('图片加载完成');
  },

  /**
   * 图片加载失败
   */
  onImageError() {
    console.log('图片加载失败');
    this.setData({
      error: '图片加载失败，请检查网络连接'
    });
  },

  /**
   * 重试
   */
  onRetry() {
    console.log('重试加载图片');
    this.setData({
      error: '',
      loading: true
    });

    // 模拟重新加载
    setTimeout(() => {
      this.setData({
        certificateUrl: 'https://example.com/certificate.jpg',
        loading: false
      });
    }, 1000);
  }
});
