<view
  class="category-card {{type === 'required' ? 'current-position required-course' : 'practice-course'}}"
  bindtap="handleClick"
>
 
  <view class="card-header">
    <view class="flex-box">
        <text class="card-title">{{course.examSubjectName}}</text>
        <!-- <view class="card-desc">{{course.desc}}</view> -->
    </view>
     <view class="course-type-badge {{type === 'required' ? 'required-badge' : 'practice-badge'}}">
    <text class="iconfont {{type === 'required' ? 'icon-info-fill' : 'icon-check'}}"></text>
    <text>{{type === 'required' ? '必考' : '必练'}}</text>
  </view>
    <text class="iconfont icon-right"></text>
  </view>
  <view class="card-content">
    <view class="stat-row" wx:if="{{showProgress}}">
      <text class="stat-label">
        <text class="iconfont icon-chart-bar"></text> 考试资格进度:{{course.tip}}
      </text>
      <text class="stat-value">{{course.examQualification}}%</text>
    </view>
    <view class="progress-bar"  wx:if="{{showProgress}}">
      <view class="progress-bar-fill" style="width: {{course.examQualification}}%"></view>
    </view>
    <view class="stat-row">
      <text class="stat-label">
        <text class="iconfont icon-clock"></text> 累计练习时长
      </text>
      <text class="stat-value">{{course.totalStudyDuration}}分钟</text>
    </view>
    <view class="stat-row">
      <text class="stat-label">
        <text class="iconfont icon-list-ol"></text> 练习题目数量
      </text>
      <text class="stat-value">{{course.totalQuestionNum}}题</text>
    </view>
  </view>
</view>
