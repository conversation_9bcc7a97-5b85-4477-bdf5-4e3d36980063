import { getPracticeRecordDetail } from '../../../api/practice';
const authBehavior = require('../../../behaviors/auth-behavior');

Page({
  /**
   * 页面的初始数据
   */
  behaviors: [authBehavior],

  data: {
    id: null,
    isLoading: true,
    messages: [], // 存储消息数组
    practiceInfo: {
      date: '',
      duration: ''
    },
    // 导航高度相关
    statusBarHeight: 0,
    navBarHeight: 0,
    totalNavHeight: 0,
    totalNumber:0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const id = options.id;
    if (!id) {
      wx.showToast({
        title: '无效的记录ID',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 获取从列表页传递的参数
    const date = options.date || '';
    const duration = options.duration || '';

    this.setData({ 
      id,
      practiceInfo: {
        date,
        duration:`时长：${duration}分钟`
      }
    });
    this.loadRecordDetail(id);
    this.getNavHeight();
  },

  /**
   * 加载练习记录详情
   */
  async loadRecordDetail(id) {
    this.setData({ isLoading: true });
    
    try {
      const result = await getPracticeRecordDetail(id);
      console.log('获取到的练习记录详情：', result);
      
      if (result) {
        // 获取练习基本信息，但优先使用URL传入的日期和时长
        const practiceInfo = {
          date: this.data.practiceInfo.date || result.date || result.createTime || '未知时间',
          duration: this.data.practiceInfo.duration || result.duration || '0分钟'
        };
        
        // 将details数组转换为messages格式
        const messages = this.convertDetailsToMessages(result.details || []);
         // 筛选records中 type为user的数量
         let totalNumber=messages.filter(item=>item.type=='user').length
        this.setData({
          messages,
          totalNumber,
          practiceInfo,
          isLoading: false
        });
      } else {
        throw new Error('未获取到数据');
      }
    } catch (error) {
      console.error('获取练习记录详情失败：', error);
      wx.showToast({
        title: '获取详情失败，请重试',
        icon: 'none'
      });
      this.setData({ isLoading: false });
    }
  },

  /**
   * 将details数组转换为消息格式
   */
  convertDetailsToMessages(details) {
    const messages = [];
    
    details.forEach(item => {
      let type = 'system';
      
      if (item.type === 'question') {
        type = 'ai';
      } else if (item.type === 'answer') {
        type = 'user';
      } else if (item.type === 'analysis') {
        type = 'ai';
      }
      
      messages.push({
        type: type,
        content: item.content,
        time: item.time || ''
      });
    });
    
    return messages;
  },
  

  /**
   * 滚动到顶部加载更多记录
   */
  onScrollToUpper() {
    // 本页面暂不需要加载更多历史记录
    console.log('已滚动到顶部');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadRecordDetail(this.data.id);
    wx.stopPullDownRefresh();
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 分享练习详情
   */
  onShareAppMessage() {
    return {
      title: '练习记录',
      path: `/pages/practice/record-detail/record-detail?id=${this.data.id}`
    };
  }
}) 