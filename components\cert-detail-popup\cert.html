
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证证书</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap');
        
        :root {
            /* 金色主题 */
            --gold-primary-color: #1B3F8F;
            --gold-accent-color: #D4AF37;
            --gold-border-color: #C0A062;
            --gold-text-color: #2C3E50;
            --gold-background-color: #FFFEF9;

            /* 紫色主题 */
            --purple-primary-color: #9C6FE4;
            --purple-accent-color: #B794EA;
            --purple-border-color: #D4C1F3;
            --purple-text-color: #4A4A4A;
            --purple-background-color: #FFFFFF;
        }

        /* 默认使用紫色主题 */
        :root {
            --primary-color: var(--purple-primary-color);
            --accent-color: var(--purple-accent-color);
            --border-color: var(--purple-border-color);
            --text-color: var(--purple-text-color);
            --background-color: var(--purple-background-color);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background: #F8F0FF;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 12px;
        }

        .certificate-container {
            width: 100%;
            max-width: 420px;
            min-height: 580px;
            background: var(--background-color);
            position: relative;
            padding: 30px 20px;
            box-shadow: 0 4px 20px rgba(156, 111, 228, 0.15);
            border-radius: 16px;
        }

        .border-frame {
            position: absolute;
            top: 12px;
            left: 12px;
            right: 12px;
            bottom: 12px;
            border: 2px solid var(--accent-color);
            pointer-events: none;
            border-radius: 12px;
        }

        .border-frame::before {
            content: '';
            position: absolute;
            top: -6px;
            left: -6px;
            right: -6px;
            bottom: -6px;
            border: 1px solid var(--border-color);
            border-radius: 14px;
        }

        .corner-decoration {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid var(--accent-color);
        }

        .top-left {
            top: 10px;
            left: 10px;
            border-right: none;
            border-bottom: none;
        }

        .top-right {
            top: 10px;
            right: 10px;
            border-left: none;
            border-bottom: none;
        }

        .bottom-left {
            bottom: 10px;
            left: 10px;
            border-right: none;
            border-top: none;
        }

        .bottom-right {
            bottom: 10px;
            right: 10px;
            border-left: none;
            border-top: none;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            position: relative;
        }

        .logo {
            width: 60px;
            height: 60px;
            margin-bottom: 20px;
        }

        .title {
            font-size: 28px;
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 15px;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 16px;
            color: var(--accent-color);
            margin-bottom: 8px;
            letter-spacing: 2px;
        }

        .level {
            font-size: 18px;
            color: var(--text-color);
            margin: 5px 0;
            font-weight: 500;
            padding: 4px 15px;
            border-radius: 20px;
            display: inline-block;
            line-height: 1.4;
        }

        .course-name {
            font-size: 20px;
            color: var(--text-color);
            margin-top: 15px;
            font-weight: 700;
            padding: 0 20px;
            line-height: 1.4;
        }

        .content {
            text-align: center;
            margin: 30px 0;
            line-height: 2;
        }

        .recipient-name {
            font-size: 20px;
            color: var(--text-color);
            margin: 20px 0;
            font-weight: 700;
        }

        .certificate-text {
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-color);
            margin: 20px auto;
            padding: 0 10px;
            max-width: 600px;
            text-align: center;
        }

        .certificate-text p {
            margin: 0;
            text-align: center;
        }

        .footer {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            margin-top: 40px;
            position: relative;
            padding: 0 15px;
        }

        .issuer {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 20px;
            width: 100%;
            position: relative;
            padding-bottom: 30px;
        }

        .issuer-info {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .issuer-name {
            font-size: 16px;
            color: var(--primary-color);
            font-weight: 700;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 5px;
        }

        .date-range {
            font-size: 14px;
            color: var(--accent-color);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .date-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-label {
            color: var(--accent-color);
            min-width: 75px;
        }

        .seal {
            width: 90px;
            height: 90px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="48" fill="none" stroke="%239C6FE4" stroke-width="2"/><circle cx="50" cy="50" r="44" fill="none" stroke="%239C6FE4" stroke-width="2"/><path d="M50 20l4 12h12l-10 8 4 12-10-8-10 8 4-12-10-8h12z" fill="%239C6FE4"/></svg>') no-repeat center;
            opacity: 0.85;
            align-self: flex-end;
        }

        .certificate-number {
            position: absolute;
            bottom: 0;
            left: 0;
            font-size: 12px;
            color: var(--accent-color);
            padding: 4px 0;
            border-top: 1px dashed var(--border-color);
            width: 100%;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 80px;
            color: rgba(156, 111, 228, 0.03);
            white-space: nowrap;
            pointer-events: none;
            z-index: 0;
        }

        .decorative-line {
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
            margin: 10px 0;
            opacity: 0.5;
        }

        @media (max-width: 360px) {
            .certificate-container {
                padding: 20px 15px;
            }

            .title {
                font-size: 24px;
            }

            .level {
                font-size: 16px;
                padding: 0 10px;
            }

            .certificate-text {
                font-size: 14px;
            }

            .footer {
                padding: 0 10px;
            }

            .issuer {
                gap: 15px;
            }

            .seal {
                width: 80px;
                height: 80px;
            }

            .date-label {
                min-width: 70px;
            }
        }

        @media print {
            body {
                background: none;
                padding: 0;
            }
            
            .certificate-container {
                max-width: none;
                width: 100%;
                box-shadow: none;
            }
        }

        /* 金色主题类 */
        .theme-gold {
            --primary-color: var(--gold-primary-color);
            --accent-color: var(--gold-accent-color);
            --border-color: var(--gold-border-color);
            --text-color: var(--gold-text-color);
            --background-color: var(--gold-background-color);
        }

        .theme-gold .seal {
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="48" fill="none" stroke="%23D4AF37" stroke-width="2"/><circle cx="50" cy="50" r="44" fill="none" stroke="%23D4AF37" stroke-width="2"/><path d="M50 20l4 12h12l-10 8 4 12-10-8-10 8 4-12-10-8h12z" fill="%23D4AF37"/></svg>') no-repeat center;
        }

        .theme-gold .watermark {
            color: rgba(27, 63, 143, 0.03);
        }

        .theme-gold body {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="certificate-container theme-gold">
        <div class="border-frame"></div>
        <div class="corner-decoration top-left"></div>
        <div class="corner-decoration top-right"></div>
        <div class="corner-decoration bottom-left"></div>
        <div class="corner-decoration bottom-right"></div>
        
        <div class="header">
            <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%239C6FE4'/><text x='50' y='65' fill='white' text-anchor='middle' font-size='40'>认证</text></svg>" alt="Logo" class="logo">
            <h1 class="title">餐考认证</h1>
            <div class="subtitle">CERTIFICATION</div>
            <div class="course-name">《餐中服务员每日工作流程》</div>
            <div class="level">P3 级</div>
        </div>

        <div class="decorative-line"></div>

        <div class="content">
            <div class="recipient-name">王小明</div>
            <div class="certificate-text">
                <p>已完成《餐中服务员每日工作流程》课程学习，<br>
                考核成绩合格，特发此证。</p>
            </div>
        </div>

        <div class="decorative-line"></div>

        <div class="footer">
            <div class="issuer">
                <div class="issuer-info">
                    <div class="issuer-name">阿依来餐厅</div>
                    <div class="date-range">
                        <div class="date-item">
                            <span class="date-label">颁发日期：</span>
                            <span>2024年02月01日</span>
                        </div>
                        <div class="date-item">
                            <span class="date-label">有效期至：</span>
                            <span>2027年01月31日</span>
                        </div>
                    </div>
                </div>
                <div class="seal"></div>
                <div class="certificate-number">证书编号：2024020100123</div>
            </div>
        </div>

        <div class="watermark">认证证书</div>
    </div>
</body>
</html> 