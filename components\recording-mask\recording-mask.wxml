<!-- components/recording-mask/recording-mask.wxml -->
<!-- 统一的录音遮罩 -->
<view
  class="recording-mask {{showCountdown ? 'countdown-mode' : ''}} {{isCancelling ? 'cancelling' : ''}}"
  wx:if="{{isRecording || showCountdown}}"
  bindtouchstart="onTouchStart"
  bindtouchmove="onTouchMove"
  bindtouchend="onTouchEnd"
  bindtouchcancel="onTouchEnd"
>
  <!-- 倒计时内容 -->
  <view class="countdown-content" wx:if="{{showCountdown}}">
    <view class="countdown-circle">
      <text class="countdown-number">{{countdownNumber}}</text>
    </view>
    <text class="countdown-text">准备录音中...</text>
    <text class="countdown-tip">松开取消</text>
  </view>

  <!-- 录音内容 -->
  <view class="recording-indicator" wx:if="{{!showCountdown}}">
    <view class="recording-icon {{isCancelling ? 'cancel-icon' : ''}}">
      <view class="recording-waves" wx:if="{{!isCancelling}}">
        <view class="wave" wx:for="{{3}}" wx:key="index" style="animation-delay: {{index * 0.2}}s"></view>
      </view>
      <view class="cancel-icon-inner" wx:if="{{isCancelling}}">
        <text class="iconfont icon-delete"></text>
      </view>
    </view>
    <text class="recording-text">{{isCancelling ? '松开手指，取消发送' : '录音中...'}}</text>
    <text class="cancel-tip" wx:if="{{!isCancelling}}">↑ 上滑取消发送</text>
    <view class="recording-time" wx:if="{{!isCancelling}}">
      <text>{{recordingTime}}</text>
    </view>
  </view>
</view>