.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 40rpx;
}

.promotion-title{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
}

.filter-button {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.1) 0%, rgba(251, 194, 235, 0.1) 100%);
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #7e57c2;
  border: 2rpx dashed rgba(126, 87, 194, 0.3);
  transition: all 0.3s;
  line-height: normal;
  height: 60rpx;
  width: auto;
  justify-content: center;
  padding:0 10rpx;
}

.filter-button::after {
  border: none;
}

.filter-button text {
  margin: 0 8rpx;
}

.filter-button .iconfont {
  font-size: 32rpx;
}


/* 页面标题 */
.page-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  padding: 0 40rpx;
  line-height: 1.3;
  position: relative;
}

.page-title::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -16rpx;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #a18cd1, #fbc2eb);
  border-radius: 3rpx;
}

/* 章节标题 */
.section-title {
  display: flex;
  align-items: center;
}

.section-title .iconfont {
  font-size: 40rpx;
  color: #a18cd1;
  margin-right: 16rpx;
}

.section-title .title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 成长路径样式 */
.career-path-container {
  padding: 0 40rpx;
  margin-bottom: 20rpx;
}

.career-path-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.career-path-card.current {
  border-left: 8rpx solid #28a745;
  background: linear-gradient(to right, rgba(40, 167, 69, 0.08), rgba(40, 167, 69, 0.03));
}

.career-path-card.next {
  border-left: 8rpx solid #a18cd1;
  background: linear-gradient(to right, rgba(161, 140, 209, 0.08), rgba(161, 140, 209, 0.03));

}

.career-path-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.career-info {
  flex: 1;
}

.career-path-title {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.career-path-title > text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.level-badge {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.level-badge.l1 {
  background-color: rgba(161, 140, 209, 0.2);
  color: #a18cd1;
}

.level-badge.l2 {
  background-color: rgba(251, 194, 235, 0.2);
  color: #fbc2eb;
}

.career-status {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  /* margin-left: auto; */
  color:#fff;
}


.career-status.current {
  background-color: #a18cd1;
}

.career-status.next {
  background:linear-gradient(135deg, rgba(161, 140, 209, 0.7), rgba(251, 194, 235, 0.7));
}
.career-status.next .iconfont{
  width:20rpx;
  height:20rpx; 
  border-radius:50%;
  background-color:#fff;
  transform: rotate(180deg);
  color:#a18cd1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.career-status .iconfont {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.career-date {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 24rpx;
}

.career-date .iconfont {
  margin-right: 8rpx;
}

.career-path-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 20rpx;
  color:#fff;
}

.career-path-icon.current {
  background-color:#a18cd1;
}

.career-path-icon.next {
  background:linear-gradient(135deg, rgba(161, 140, 209, 0.7), rgba(251, 194, 235, 0.7));
}

.career-path-icon .iconfont {
  font-size: 40rpx;
}

/* 连接器样式 */
.path-connector {
  display: flex;
  justify-content: center;
  margin: -10rpx 0;
  position: relative;
  z-index: 10;
}

.path-arrow {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 50%;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  animation: bounce 2s infinite, pulse 3s infinite alternate;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}
.path-arrow::before{
  content: '';
  display: block;
  width: 2px;
  height: 100%;
  /* background:linear-gradient(to bottom, #28a745, #a18cd1); */
  border-radius:2px;
  position: absolute;
  top:0;
  left:50%;
  z-index:-1;
}

.path-arrow .iconfont {
  color: #ccc;
  font-size: 24rpx;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

@keyframes pulse {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}




/* 考试模块样式 */
.exam-container {
  padding: 0 40rpx;
}

.exam-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.exam-title .iconfont {
  font-size: 40rpx;
  color: #a18cd1;
  margin-right: 16rpx;
}

.exam-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.certificate-cards {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
