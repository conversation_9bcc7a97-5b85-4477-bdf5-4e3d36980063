/* pages/notice/list.wxss */
.notice-list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 头部样式 */
.header {
  background: var( --primary-gradient);
  padding: 40rpx 30rpx 60rpx;
  color: white;
  text-align: center;
}

.header .title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header .subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 通知列表样式 */
.notice-list {
  padding: 20rpx;
}

.notice-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.notice-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 通知图标 */
.notice-icon {
  width: 80rpx;
  height: 80rpx;
  background:var( --primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.notice-icon .iconfont {
  font-size: 36rpx;
  color: white;
}

/* 通知内容 */
.notice-content {
  flex: 1;
  min-width: 0;
}

.notice-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notice-summary {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.notice-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notice-time {
  font-size: 24rpx;
  color: #999;
}

.notice-status {
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

/* 箭头图标 */
.notice-arrow {
  margin-left: 20rpx;
  flex-shrink: 0;
}

.notice-arrow .iconfont {
  font-size: 28rpx;
  color: #ccc;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  color: #999;
}

.empty-icon {
  margin-bottom: 30rpx;
}

.empty-icon .iconfont {
  font-size: 120rpx;
  color: #ddd;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 16rpx;
  color: #666;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-container {
  padding: 20rpx;
}

.loading-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f0f0f0;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.loading-content {
  flex: 1;
}

.loading-title {
  height: 32rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
  width: 60%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.loading-summary {
  height: 28rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 16rpx;
  width: 80%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.loading-meta {
  height: 24rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  width: 40%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

/* 加载更多提示 */
.load-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
