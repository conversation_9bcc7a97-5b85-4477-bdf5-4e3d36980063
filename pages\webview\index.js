Page({
  data: {
    url: '',
    title: '餐烤师',
    errorMsg: '',
    hasError: false
  },
  
  onLoad: function(options) {
    console.log('webview: onLoad, options:', options);
    
    if (options && options.url) {
      try {
        let url = decodeURIComponent(options.url);
        console.log('webview: 解码后的URL:', url);
        
        // 确保URL以http或https开头
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          url = 'https://' + url;
        }
        
        this.setData({
          url: url
        });
        
        // 根据URL设置页面标题
        // if (options.title) {
        //   const title = decodeURIComponent(options.title);
        //   this.setData({ title: title });
        // } else {
        //   // 根据URL自动判断是前厅还是后厨餐烤师
        //   if (url.includes('iAgUb9TXMUyTWGWJ')) {
        //     this.setData({ title: '后厨餐烤师' });
        //   } else if (url.includes('55dLh2po8AqIuktu')) {
        //     this.setData({ title: '前厅餐烤师' });
        //   }
        // }
        
        console.log('webview: 设置标题为:', this.data.title);
        
        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: this.data.title
        });
      } catch (error) {
        console.error('webview: URL解析错误:', error);
        this.setData({
          hasError: true,
          errorMsg: '链接地址无效，请返回重试'
        });
      }
    } else {
      console.error('webview: 缺少URL参数');
      this.setData({
        hasError: true,
        errorMsg: '缺少链接地址，请返回重试'
      });
    }
  },
  
  // 页面显示
  onShow: function() {
    console.log('webview: onShow');
  },
  
  // 监听网页加载完成
  onWebviewLoad: function(e) {
    console.log('webview: 页面加载完成');
  },
  
  // 监听网页加载错误
  onWebviewError: function(e) {
    console.error('webview: 页面加载错误:', e.detail);
    this.setData({
      hasError: true,
      errorMsg: '页面加载失败，请返回重试'
    });
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
}); 