Component({
  /**
   * 组件的属性列表
   */
  properties: {
    course: {
      type: Object,
      value: {}
    },
    type: {
      type: String,
      value: 'required'
    },
    showProgress: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件选项
   */
  options: {
    styleIsolation: 'apply-shared'  // 使用apply-shared让组件可以使用app.wxss中的样式
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 组件内部数据
    tip:''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleClick() {
      // 点击事件处理
      this.triggerEvent('click', this.data.course);
    },
    getTip(){
      return this.data.course.totalStudyDuration
    }
  }
}) 