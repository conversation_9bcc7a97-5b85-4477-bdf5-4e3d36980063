<view class="container">
  <!-- 筛选状态栏 -->
  <view class="filter-status-bar">
    <button class="filter-button" bindtap="showPositionDrawer">
      <text class="iconfont icon-user"></text>
      <text>岗位</text>
    </button>

    <view class="current-filter-status">
      <text>{{currentPosition}}</text>
      <text class="current-filter-badge">{{currentLevel}}</text>
    </view>

    <button class="filter-button" bindtap="showLevelDrawer">
      <text class="iconfont icon-layers-fill"></text>
      <text>级别</text>
    </button>
  </view>

  <!-- 引导提示 -->
  <view class="guide-tip" wx:if="{{showGuide}}">
    <view class="guide-tip-icon">
      <text class="iconfont icon-lightbulb-fill"></text>
    </view>
    <view class="guide-tip-text">
      亲爱的同学，点击上方的"岗位"和"级别"可以切换不同岗位和等级的学习内容哦~
    </view>
    <!-- <view class="guide-close" bindtap="closeGuide">
      <text class="iconfont icon-close"></text>
    </view> -->
  </view>

  <!-- 餐考分类列表 -->
  <view class="categories-container">
    <!-- 主岗位内容标题 -->
    <view class="content-section-title">
      <text class="iconfont icon-star"></text>
      <text>当前岗位学习内容</text>
    </view>

    <!-- 必学内容标记 -->
    <view class="course-type-divider">
      <view class="course-type-divider-text">
        <text class="iconfont icon-bookmark"></text> 必考内容
      </view>
    </view>

    <!-- 当前岗位必学餐考分类 -->
    <view class="current-position-cards" wx:if="{{requiredCourses.length > 0}}">
      <block wx:for="{{requiredCourses}}" wx:key="id">
        <course-card
          course="{{item}}"
          type="required"
          bindclick="goCourseDetail"
          data-course="{{item}}"
        />
      </block>
    </view>
    <view class="current-position-cards" wx:else>
      <empty-tip text="暂无必考内容"  />
    </view>

    <!-- 必练内容标记 -->
    <view class="course-type-divider" >
      <view class="course-type-divider-text">
        <text class="iconfont icon-bookmark"></text> 必练内容
      </view>
    </view>

    <!-- 必练练习 -->
    <view class="practice-courses-container" wx:if="{{practiceCourses.length > 0}}">
      <block wx:for="{{practiceCourses}}" wx:key="id">
        <course-card
          course="{{item}}"
          type="practice"
          bindclick="goCourseDetail"
          data-course="{{item}}"
          showProgress="{{false}}"
        />
      </block>
    </view>
    <view class="practice-courses-container" wx:else>
      <empty-tip text="暂无必练内容" />
    </view>
  </view>

  <!-- 岗位选择抽屉 -->
  <view class="drawer-mask" wx:if="{{positionDrawerVisible}}" bindtap="closePositionDrawer" catchtouchmove="catchTouchMove"></view>
  <view class="drawer position-drawer {{positionDrawerVisible ? 'drawer-show' : ''}}" catchtouchmove="catchTouchMove">
    <!-- <view class="drawer-header">
      <text class="drawer-title">当前岗位：{{currentPosition}}</text>
      <text class="iconfont icon-close" bindtap="closePositionDrawer"></text>
    </view> -->
    <!-- tab切换  晋升岗位其他岗位 -->
    <view class="drawer-header drawer-header-tab">
      <view class="tab-item  {{item.id === tabPositionId ? 'active' : ''}}" wx:for="{{tabItems}}" wx:key="id" bindtap="selectTab" data-tab="{{item.id}}">
        <text class="tab-text">{{item.name}}</text>
      </view>
      <text class="iconfont icon-close" bindtap="closePositionDrawer"></text>
    </view>
    <view class="drawer-body" wx:if="{{positions.length > 0}}">
      <view
        class="selector-item {{item.id === positionValue ? 'selector-item-active' : ''}}"
        wx:for="{{positions}}"
        wx:key="value"
        bindtap="selectPosition"
        data-position="{{item}}"
      >
        <view class="selector-icon">
          <text class="iconfont icon-user"></text>
        </view>
        <view class="selector-info">{{item.name}}
            <text class="current"  wx:if="{{userInfo.positionId==item.id}}">(当前岗位)</text>
          <!-- <text class="selector-subtitle">{{item.desc}}</text> -->
        </view>
        <view class="selector-check" wx:if="{{item.id === positionValue}}">
          <text class="iconfont icon-check"></text>
        </view>
      </view>
    </view>
    <view class="drawer-body" wx:else>
      <empty-tip text="暂无岗位" />
    </view>
  </view>

  <!-- 级别选择抽屉 -->
  <view class="drawer-mask" wx:if="{{levelDrawerVisible}}" bindtap="closeLevelDrawer" catchtouchmove="catchTouchMove"></view>
  <view class="drawer level-drawer {{levelDrawerVisible ? 'drawer-show' : ''}}" catchtouchmove="catchTouchMove">
    <view class="drawer-header">
      <text class="drawer-title">选择级别</text>
      <text class="iconfont icon-close" bindtap="closeLevelDrawer"></text>
    </view>
    <view class="drawer-body" wx:if="{{levels.length > 0}}">
      <view
        class="{{item.value === currentLevelId ? 'selector-item-active':''}} selector-item"
        wx:for="{{levels}}"
        wx:key="value"
        bindtap="selectLevel"
        data-level="{{item}}"
      >
        <view class="selector-icon">
          <text class="iconfont icon-star"></text>
        </view>
        <view class="selector-info">{{item.label}}
            <text class="current"  wx:if="{{userInfo.levelId==item.value&&userInfo.positionId==positionValue}}">(当前级别)</text>

          <!-- <text class="selector-subtitle">{{item.desc}}</text> -->
        </view>
        <view class="selector-check" wx:if="{{item.value === currentLevelId}}">
          <text class="iconfont icon-check"></text>
        </view>
      </view>
    </view>
    <view class="drawer-body" wx:else>
      <empty-tip text="暂无级别" />
    </view>
  </view>

  <!-- 授权弹窗组件 -->
  <auth-popup
    visible="{{showAuthPopup}}"
    nextPath="{{nextPath}}"
    bindauthsuccess="onAuthSuccess"
    bindclose="onAuthClose"
  />
  <!-- 身份认证弹窗组件 -->
  <identity-verify-popup
    visible="{{showAuthModal}}"
    nextPath="{{nextPath}}"
    bindverifysuccess="onVerifySuccess"
    bindclose="onVerifyClose"
  />
   <!-- 认证成功 -->
  <success-popup 
    visible="{{showSuccessModal}}"  
  />
</view>
