/* pages/profile/index.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 用户资料头部 */
.profile-header {
  position: relative;
  background: var( --primary-gradient);
  padding: 48rpx 32rpx;
  color: #fff;
  border-radius: 0 0 40rpx 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(161, 140, 209, 0.3);
  overflow: hidden;
}

.user-info-container {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.avatar-container {
  margin-right: 24rpx;
}

.avatar-border {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid rgba(255, 255, 255, 0.4);
}

.avatar-border .iconfont {
  font-size: 80rpx;
  color: rgba(255, 255, 255, 0.8);
}
.avatar-border image{
  width:100%;   
  height:100%;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.position-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  font-size: 24rpx;
}

.position-badge .iconfont {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.stats-container {
  display: flex;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
  display: block;
}
.stat-value-unit {    
  display: inline-block;
  font-size: 22rpx;
  font-weight: normal;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.8;
  display: block;
}

/* 装饰元素 */
.sparkle {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  box-shadow: 0 0 20rpx rgba(255, 255, 255, 0.8);
}

/* 内容区域 */
.content-section {
  padding: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.section-title .iconfont {
  margin-right: 16rpx;
  color: #a18cd1;
  font-size: 32rpx;
}

/* 雷达图 */
.radar-container {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.chart-container {
  height: 450rpx;
  width: 100%;
  position: relative;
  z-index: 0;
}

.radar-canvas {
  width: 100%;
  height: 100%;
  display: block;
  z-index:0;
}

.chart-legend {
  display: flex;
  justify-content: center;
  margin-top: 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 16rpx;
  font-size: 24rpx;
  color: #666;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
}

.current-level {
  background-color: #a18cd1;
}

.last-level {
  background-color: #8fc1ff;
}

/* 菜单分类 */
.menu-category {
  font-size: 28rpx;
  font-weight: 600;
  color: #666;
  margin: 40rpx 0 16rpx;
  padding-left: 16rpx;
  position: relative;
}

.menu-category::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background: linear-gradient(to bottom, #a18cd1, #fbc2eb);
  border-radius: 3rpx;
}

.menu-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  position: relative;
}

.menu-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 32rpx;
  right: 32rpx;
  bottom: 0;
  height: 1rpx;
  background: #f0f0f0;
}

.menu-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.1) 0%, rgba(251, 194, 235, 0.1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.menu-icon .iconfont {
  font-size: 32rpx;
  color: #a18cd1;
}

.menu-title {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-arrow {
  font-size: 30rpx;
  color: #ccc;
}

/* 版本信息 */
.version-info {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 64rpx;
  margin-bottom: 32rpx;
} 