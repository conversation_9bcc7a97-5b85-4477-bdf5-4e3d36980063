@font-face {
  font-family: "iconfont"; /* Project id 4885301 */
  src: url('iconfont.woff2?t=1745906492091') format('woff2'),
       url('iconfont.woff?t=1745906492091') format('woff'),
       url('iconfont.ttf?t=1745906492091') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-cuowu:before {
  content: "\e8e7";
}

.icon-AIassistant:before {
  content: "\e656";
}

.icon-icon-test:before {
  content: "\e606";
}

.icon-System-Fill:before {
  content: "\e729";
}

.icon-a-002:before {
  content: "\e608";
}

.icon-wode:before {
  content: "\e614";
}

.icon-quanbu:before {
  content: "\e684";
}

.icon-canting:before {
  content: "\e61d";
}

.icon-rili:before {
  content: "\e8b4";
}

.icon-bofan:before {
  content: "\e661";
}

.icon-jianpan:before {
  content: "\e65f";
}

.icon-zanwushuju:before {
  content: "\e61f";
}

.icon-qyll:before {
  content: "\e6b5";
}

.icon-history:before {
  content: "\e61b";
}

.icon-bookmark:before {
  content: "\e649";
}

.icon-i-left:before {
  content: "\e627";
}

.icon-tingzhi:before {
  content: "\e611";
}

.icon-yuyin:before {
  content: "\e623";
}

.icon-iconfontplay2:before {
  content: "\e719";
}

.icon-playfill:before {
  content: "\e74f";
}

.icon-Pause:before {
  content: "\e605";
}

.icon-thumbsup:before {
  content: "\e853";
}

.icon-chat_conversation:before {
  content: "\e604";
}

.icon-tool:before {
  content: "\e70c";
}

.icon-book:before {
  content: "\e7c7";
}

.icon-reset:before {
  content: "\e603";
}

.icon-eye:before {
  content: "\e622";
}

.icon-down:before {
  content: "\e839";
}

.icon-info-fill:before {
  content: "\e83e";
}

.icon-layers-fill:before {
  content: "\e85c";
}

.icon-search:before {
  content: "\e621";
}

.icon-phone:before {
  content: "\e607";
}

.icon-location:before {
  content: "\e76f";
}

.icon-Mail:before {
  content: "\eae4";
}

.icon-filter:before {
  content: "\e61a";
}

.icon-comment:before {
  content: "\e66f";
}

.icon-close:before {
  content: "\e601";
}

.icon-user-shield:before {
  content: "\f13d";
}

.icon-chef-hat:before {
  content: "\e6ad";
}

.icon-trophy1:before {
  content: "\e651";
}

.icon-info:before {
  content: "\e83d";
}

.icon-bell1:before {
  content: "\e602";
}

.icon-certificate:before {
  content: "\e654";
}

.icon-shield:before {
  content: "\e669";
}

.icon-fire:before {
  content: "\e6a3";
}

.icon-bell:before {
  content: "\e61c";
}

.icon-right:before {
  content: "\e840";
}

.icon-play:before {
  content: "\e751";
}

.icon-cashier:before {
  content: "\e707";
}

.icon-usertie:before {
  content: "\e652";
}

.icon-trophy:before {
  content: "\e745";
}

.icon-award:before {
  content: "\ef2e";
}

.icon-book-open:before {
  content: "\ef48";
}

.icon-lightbulb-fill:before {
  content: "\e7ac";
}

.icon-clock:before {
  content: "\e819";
}

.icon-star:before {
  content: "\e67b";
}

.icon-paper-plane:before {
  content: "\e907";
}

.icon-check:before {
  content: "\e619";
}

.icon-shouye:before {
  content: "\e6ef";
}

.icon-liaotian:before {
  content: "\e75e";
}

.icon-layer-group:before {
  content: "\eb57";
}

.icon-list-ol:before {
  content: "\eb59";
}

.icon-rocket:before {
  content: "\eb87";
}

.icon-edit:before {
  content: "\e60f";
}

.icon-chart-bar:before {
  content: "\e600";
}

.icon-user:before {
  content: "\e7b0";
}

