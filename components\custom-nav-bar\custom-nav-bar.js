Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    desc: {
      type: String,
      value: ''
    },
    statusBarHeight: {
      type: Number,
      value: 0
    },
    navBarHeight: {
      type: Number,
      value: 44
    },
    formatTime: {
      type: String,
      value: '00:00'
    },
    showTime:{
      type:Boolean,
      default:true,
    },
    titleClass:{
      type:String,
      value:'',
    },
    showBack:{
      type:Boolean,
      value:true,
    },
  },
  options: {
    styleIsolation: 'apply-shared',
    multipleSlots: true
  },
  methods: {
    goBack() {
      this.triggerEvent('back');
    },
    onEndExam() {
      this.triggerEvent('endExam');
    }
  }
}) 