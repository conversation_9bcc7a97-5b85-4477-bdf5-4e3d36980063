.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #f8f9fa;
  position: relative;
  padding-bottom:0px;
}

#statsSummary{
  overflow:hidden;
}
.filter-container {
  display: flex;
  padding: 16rpx 32rpx;
  justify-content: space-between;
  background-color: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.filter-select {
  padding: 12rpx 24rpx;
  box-sizing: border-box;
  border-radius: 32rpx;
  font-size: 26rpx;
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #c7b4f5;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48%;
}
.filter-select .iconfont {
  font-size: 22rpx;
  margin-left: 10rpx;
}

.records-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  /* padding-bottom: 20rpx; */
  transition: opacity 0.3s;
}

.records-container.hidden {
  opacity: 0.5;
  pointer-events: none;
}

.records-wrapper {
  /* padding-bottom: 30rpx; */
  width: 100%;
  min-height: 100%;
  box-sizing: border-box;
}

/* scroll-view 样式 */
scroll-view {
  height: 100%;
  width: 100%;
}

.record-card {
  background: #fff;
  border-radius: 24rpx;
  margin: 32rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.record-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
}

.record-header {
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.15) 0%, rgba(251, 194, 235, 0.15) 100%);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  /* display: flex;
  justify-content: space-between;
  align-items: center; */
}

.record-summary {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

}
.record-summary > view {
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}
.record-tag{
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fbc2eb 0%, #a18cd1 100%);
  color: #fff;
}

.category-tag {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  background-color: rgba(161, 140, 209, 0.2);
  color: #a18cd1;
  border-radius: 24rpx;
}


.record-content {
  padding: 32rpx;
}

.record-detail {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  color: #666;
}

.record-detail:last-child {
  margin-bottom: 0;
}

.record-label {
  width: 160rpx;
  color: #999;
}

.accuracy.high {
  color: #19b151;
}



.position-badge,
.level-badge {
  display: inline-flex;
  align-items: center;
  background-color: rgba(161, 140, 209, 0.1);
  color: #a18cd1;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
  color: #999;
}

.empty-state .iconfont {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  color: #ddd;
}


/* 没有更多数据提示 */
.no-more-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 页面全局加载状态遮罩 */
.page-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
