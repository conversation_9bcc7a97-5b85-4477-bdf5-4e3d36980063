<view class="container">
  <!-- 考试横幅 -->
  <view class="promotion-banner">
    <text class="level-badge">{{examInfo.nextLevel}} 考试</text>
    <text class="banner-title">{{examInfo.title}}</text>
    <text class="banner-desc">完成本次认证证书考试，距离您晋升更进一步</text>
  </view>

  <!-- 考试信息 -->
  <view class="exam-info">
    <view class="info-item">
      <view class="info-icon">
        <text class="iconfont icon-clock"></text>
      </view>
      <view class="info-content">
        <text class="info-title">考试时长</text>
        <text class="info-desc">{{examInfo.examDuration}}分钟</text>
      </view>
    </view>

    <view class="info-item">
      <view class="info-icon">
        <text class="iconfont icon-list-ol"></text>
      </view>
      <view class="info-content">
        <text class="info-title">题目数量</text>
        <text class="info-desc">{{examInfo.questionCount}}题</text>
      </view>
    </view>

    <!-- <view class="info-item">
      <view class="info-icon">
        <text class="iconfont icon-award"></text>
      </view>
      <view class="info-content">
        <text class="info-title">考试通过标准</text>
        <text class="info-desc">/{{examInfo.questionCount}}题</text>
      </view>
    </view> -->
     <view class="info-item">
      <view class="info-icon">
        <text class="iconfont icon-reset"></text>
      </view>
      <view class="info-content">
        <text class="info-title">考试次数</text>
        <text class="info-desc">{{examInfo.examinedCount || 0}}/{{examInfo.examCount || 0}}次</text>
      </view>
    </view>
  </view>

  <!-- 考试规则 -->
  <view class="rule-card">
    <view class="rule-title">
      <text class="iconfont icon-info"></text>
      <text>考试须知</text>
    </view>

    <view class="rule-list">
      <view class="rule-item">
        <text class="iconfont icon-check"></text>
        <text class="rule-text">考试过程中请保持网络通畅</text>
      </view>
      <view class="rule-item">
        <text class="iconfont icon-check"></text>
        <text class="rule-text">答题期间不要切换到其他应用,否则将自动结束考试</text>
      </view>
      <view class="rule-item">
        <text class="iconfont icon-check"></text>
        <text class="rule-text">请独立完成考试，不得查阅资料</text>
      </view>
      <!-- <view class="rule-item">
        <text class="iconfont icon-check"></text>
        <text class="rule-text">考试结束前请认真检查答案</text>
      </view> -->
    </view>
  </view>

  <!-- 开始按钮 -->
  <view class="start-button" bindtap="startExam">
    <text class="iconfont icon-play"></text>
    <text>开始考试</text>
  </view>
</view>
