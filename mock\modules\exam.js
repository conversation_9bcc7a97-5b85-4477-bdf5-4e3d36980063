/**
 * 考试相关API的模拟数据
 */

// 试题类型
const questionTypes = {
  SINGLE_CHOICE: 'single_choice',
  MULTIPLE_CHOICE: 'multiple_choice',
  TRUE_FALSE: 'true_false',
  FILL_BLANK: 'fill_blank',
  SHORT_ANSWER: 'short_answer'
};

// 考试信息示例
const examExample = {
  id: 7,
  title: '食品安全管理认证',
  description: '评估食品安全知识和管理能力，包括卫生标准、危害分析和关键控制点',
  level: 2,
  type: 3,
  totalScore: 100,
  passingScore: 80,
  duration: 100, // 分钟
  questionCount: 60,
  startTime: '2024-01-15 09:00:00',
  endTime: '2024-01-15 10:40:00',
  status: 'ongoing', // 'upcoming', 'ongoing', 'ended'
  instructions: '本考试旨在评估您的食品安全管理知识和能力。考试共60题，总分100分，通过分数为80分，考试时间100分钟。请仔细阅读每道题目，根据您的知识和经验选择最合适的答案。',
  sections: [
    {
      id: 1,
      title: '食品安全基础知识',
      description: '评估对食品安全基本概念和原则的理解',
      questionCount: 20,
      totalScore: 30
    },
    {
      id: 2,
      title: '危害分析与关键控制点',
      description: '评估对HACCP体系原理和应用的掌握',
      questionCount: 15,
      totalScore: 25
    },
    {
      id: 3,
      title: '食品安全管理实务',
      description: '评估在实际工作中处理食品安全问题的能力',
      questionCount: 25,
      totalScore: 45
    }
  ]
};

// 试题数据（部分试题样例）
const examQuestions = [
  {
    id: 701,
    examId: 7,
    sectionId: 1,
    type: questionTypes.SINGLE_CHOICE,
    content: '以下哪项不属于食品安全的物理性危害？',
    options: [
      { id: 'A', content: '玻璃碎片' },
      { id: 'B', content: '金属异物' },
      { id: 'C', content: '沙门氏菌' },
      { id: 'D', content: '塑料碎片' }
    ],
    correctAnswer: 'C',
    explanation: '沙门氏菌是一种常见的致病菌，属于生物性危害，而不是物理性危害。物理性危害包括玻璃碎片、金属异物、塑料碎片等可能混入食品的异物。',
    score: 1.5
  },
  {
    id: 702,
    examId: 7,
    sectionId: 1,
    type: questionTypes.MULTIPLE_CHOICE,
    content: '以下哪些是预防食品交叉污染的有效措施？（多选）',
    options: [
      { id: 'A', content: '使用不同的砧板分别处理生熟食品' },
      { id: 'B', content: '在同一工作台面上同时处理生熟食品' },
      { id: 'C', content: '处理完生食后立即洗手再处理熟食' },
      { id: 'D', content: '将生食和熟食放在同一容器中存放' },
      { id: 'E', content: '使用不同的工具处理不同类型的食品' }
    ],
    correctAnswer: ['A', 'C', 'E'],
    explanation: '预防交叉污染的关键是隔离生熟食品，包括使用不同的砧板和工具，以及处理不同食品之间正确洗手。而在同一工作台同时处理生熟食品或将生熟食品放在同一容器存放都会增加交叉污染风险。',
    score: 2
  },
  {
    id: 703,
    examId: 7,
    sectionId: 1,
    type: questionTypes.TRUE_FALSE,
    content: '冷藏设备的温度应保持在0℃至5℃之间，可以有效抑制大多数病原菌的生长。',
    correctAnswer: true,
    explanation: '0℃至5℃的冷藏温度可以有效抑制（但不能杀死）大多数病原菌的生长。正确的冷藏温度控制是确保食品安全的关键措施之一。',
    score: 1.5
  },
  {
    id: 704,
    examId: 7,
    sectionId: 2,
    type: questionTypes.SINGLE_CHOICE,
    content: 'HACCP体系中，CCP是指什么？',
    options: [
      { id: 'A', content: '控制程序点' },
      { id: 'B', content: '危害分析点' },
      { id: 'C', content: '关键控制点' },
      { id: 'D', content: '卫生检查点' }
    ],
    correctAnswer: 'C',
    explanation: 'CCP是Critical Control Point的缩写，中文译为关键控制点，是指在食品生产过程中能够采取控制措施，并且是预防、消除或将食品安全危害减少到可接受水平所必需的步骤或程序。',
    score: 1.5
  },
  {
    id: 705,
    examId: 7,
    sectionId: 2,
    type: questionTypes.MULTIPLE_CHOICE,
    content: 'HACCP体系的七个原理包括以下哪些？（多选）',
    options: [
      { id: 'A', content: '进行危害分析' },
      { id: 'B', content: '确定关键控制点' },
      { id: 'C', content: '制定食品标准' },
      { id: 'D', content: '建立监控系统' },
      { id: 'E', content: '确定纠偏措施' },
      { id: 'F', content: '实施员工培训' }
    ],
    correctAnswer: ['A', 'B', 'D', 'E'],
    explanation: 'HACCP的七个原理包括：进行危害分析、确定关键控制点、建立关键限值、建立监控系统、确定纠偏措施、建立验证程序、建立文件和记录保持。其中"制定食品标准"和"实施员工培训"不是HACCP的原理。',
    score: 2
  },
  {
    id: 706,
    examId: 7,
    sectionId: 3,
    type: questionTypes.FILL_BLANK,
    content: '食品接触表面的消毒液浓度应定期检测，确保有效氯浓度不低于____ppm。',
    correctAnswer: '200',
    explanation: '根据一般食品卫生规范，食品接触表面的消毒液有效氯浓度通常不应低于200ppm，以确保有效杀灭表面微生物。',
    score: 1.5
  },
  {
    id: 707,
    examId: 7,
    sectionId: 3,
    type: questionTypes.SHORT_ANSWER,
    content: '简述餐饮企业如何建立完善的食品可追溯体系。',
    modelAnswer: '建立食品可追溯体系需要：\n1. 建立完整的供应商评估和管理体系，确保所有原料来源可查。\n2. 实施批次管理，对每批原料和成品进行编码。\n3. 记录原料进货信息，包括供应商、批次、数量、进货日期等。\n4. 记录生产过程信息，包括使用的原料批次、生产日期、生产工艺等。\n5. 记录产品销售信息，包括销售对象、数量、日期等。\n6. 建立信息管理系统，实现信息的有效存储和快速检索。\n7. 定期进行可追溯性演练，验证系统有效性。\n8. 确保记录保存时间符合法规要求，通常不少于产品保质期后6个月。',
    score: 5,
    gradingCriteria: [
      { point: '提及供应商管理', score: 1 },
      { point: '提及批次管理', score: 1 },
      { point: '提及进货、生产、销售记录', score: 1 },
      { point: '提及信息管理系统', score: 1 },
      { point: '提及演练和记录保存', score: 1 }
    ]
  }
];

// 考生答题记录
const answerRecords = {
  123456: { // userId
    7: { // examId
      startTime: '2024-01-15 09:10:15',
      remainingTime: 5400, // 剩余秒数
      answers: {
        701: 'C',
        702: ['A', 'C'],
        703: true,
        704: 'C',
        705: ['A', 'B', 'D'],
        706: '200',
        707: '建立食品可追溯体系需要：\n1. 实施严格的供应商管理\n2. 对原料和产品进行批次管理\n3. 记录原料进货信息\n4. 记录生产加工过程\n5. 记录销售信息\n6. 使用信息化系统管理追溯数据\n7. 定期进行追溯演练'
      },
      submittedQuestions: [701, 702, 703, 704]
    }
  }
};

// 考试结果
const examResults = {
  123456: { // userId
    5: { // examId (餐厅管理基础认证)
      examId: 5,
      title: '餐厅管理基础认证',
      submitTime: '2023-11-30 15:45:23',
      score: 78,
      passingScore: 70,
      isPassed: true,
      timeUsed: 105, // 分钟
      sections: [
        {
          id: 1,
          title: '管理基础知识',
          score: 25,
          totalScore: 30,
          correctCount: 12,
          totalCount: 15
        },
        {
          id: 2,
          title: '人员管理',
          score: 22,
          totalScore: 30,
          correctCount: 11,
          totalCount: 15
        },
        {
          id: 3,
          title: '运营与成本控制',
          score: 31,
          totalScore: 40,
          correctCount: 15,
          totalCount: 20
        }
      ],
      wrongQuestions: [
        {
          id: 505,
          content: '以下哪种激励方式最适合提高餐厅员工的长期服务质量？',
          yourAnswer: 'A',
          correctAnswer: 'D',
          explanation: '员工职业发展路径是提高长期服务质量的最有效方式，因为它给予员工明确的成长目标和前景，而短期奖金、即时表扬和惩罚机制主要影响短期行为。'
        },
        {
          id: 512,
          content: '餐厅的毛利率计算公式是什么？',
          yourAnswer: '(总收入-成本)/总成本×100%',
          correctAnswer: '(总收入-成本)/总收入×100%',
          explanation: '毛利率应为毛利润与销售收入的比率，而非与成本的比率。正确计算方式为：(销售收入-销售成本)/销售收入×100%。'
        }
      ],
      certificate: {
        id: 'CERT-RM2311300004',
        name: '餐厅管理基础认证',
        issueDate: '2023-12-01',
        expiryDate: '2025-12-01',
        downloadUrl: '/pages/profile/certificate-download?id=CERT-RM2311300004'
      }
    }
  }
};

// API调用处理函数
const examMock = {
  // 获取考试信息
  'GET /api/exam/info': (data) => {
    const { examId } = data;
    
    if (examId === '7') {
      return {
        code: 0,
        message: 'success',
        data: examExample
      };
    } else {
      return {
        code: 404,
        message: '考试不存在',
        data: null
      };
    }
  },
  
  // 获取考试题目
  'GET /api/exam/questions': (data) => {
    const { examId, sectionId } = data;
    
    let result = [...examQuestions];
    
    // 按考试ID筛选
    if (examId) {
      result = result.filter(item => item.examId === parseInt(examId));
    }
    
    // 按章节ID筛选
    if (sectionId) {
      result = result.filter(item => item.sectionId === parseInt(sectionId));
    }
    
    // 去除答案信息，仅保留题目内容
    result = result.map(question => {
      const { correctAnswer, explanation, ...rest } = question;
      return rest;
    });
    
    return {
      code: 0,
      message: 'success',
      data: result
    };
  },
  
  // 获取答题记录
  'GET /api/exam/answer-record': (data) => {
    const { examId, userId } = data;
    
    // 使用固定的用户ID作为演示
    const userIdToUse = userId || '123456';
    const userAnswers = answerRecords[userIdToUse];
    
    if (!userAnswers) {
      return {
        code: 404,
        message: '未找到答题记录',
        data: null
      };
    }
    
    const examAnswers = userAnswers[examId];
    
    if (!examAnswers) {
      return {
        code: 404,
        message: '未找到该考试的答题记录',
        data: null
      };
    }
    
    return {
      code: 0,
      message: 'success',
      data: examAnswers
    };
  },
  
  // 保存答题
  'POST /api/exam/save-answer': (data) => {
    const { examId, questionId, answer, userId } = data;
    
    // 使用固定的用户ID作为演示
    const userIdToUse = userId || '123456';
    
    // 确保用户记录存在
    if (!answerRecords[userIdToUse]) {
      answerRecords[userIdToUse] = {};
    }
    
    // 确保考试记录存在
    if (!answerRecords[userIdToUse][examId]) {
      answerRecords[userIdToUse][examId] = {
        startTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
        remainingTime: examExample.duration * 60, // 转换为秒
        answers: {},
        submittedQuestions: []
      };
    }
    
    // 保存答案
    answerRecords[userIdToUse][examId].answers[questionId] = answer;
    
    // 添加到已提交题目列表
    if (!answerRecords[userIdToUse][examId].submittedQuestions.includes(parseInt(questionId))) {
      answerRecords[userIdToUse][examId].submittedQuestions.push(parseInt(questionId));
    }
    
    return {
      code: 0,
      message: 'success',
      data: {
        examId,
        questionId,
        saved: true
      }
    };
  },
  
  // 更新剩余时间
  'POST /api/exam/update-time': (data) => {
    const { examId, remainingTime, userId } = data;
    
    // 使用固定的用户ID作为演示
    const userIdToUse = userId || '123456';
    
    // 检查记录是否存在
    if (!answerRecords[userIdToUse] || !answerRecords[userIdToUse][examId]) {
      return {
        code: 404,
        message: '未找到答题记录',
        data: null
      };
    }
    
    // 更新剩余时间
    answerRecords[userIdToUse][examId].remainingTime = parseInt(remainingTime);
    
    return {
      code: 0,
      message: 'success',
      data: {
        examId,
        remainingTime: parseInt(remainingTime),
        updated: true
      }
    };
  },
  
  // 提交考试
  'POST /api/exam/submit': (data) => {
    const { examId, userId } = data;
    
    // 使用固定的用户ID作为演示
    const userIdToUse = userId || '123456';
    
    // 检查记录是否存在
    if (!answerRecords[userIdToUse] || !answerRecords[userIdToUse][examId]) {
      return {
        code: 404,
        message: '未找到答题记录',
        data: null
      };
    }
    
    // 模拟评分计算
    // 获取总题目数
    const totalQuestions = examQuestions.filter(q => q.examId === parseInt(examId)).length;
    // 获取已答题数
    const answeredQuestions = Object.keys(answerRecords[userIdToUse][examId].answers).length;
    
    // 计算答题率
    const answerRate = answeredQuestions / totalQuestions;
    
    // 随机生成一个合理的分数（60-95之间）
    const score = Math.floor(Math.random() * 35) + 60;
    
    // 检查是否通过
    const isPassed = score >= examExample.passingScore;
    
    // 计算用时（分钟）
    const timeUsed = examExample.duration - Math.ceil(answerRecords[userIdToUse][examId].remainingTime / 60);
    
    const result = {
      examId: parseInt(examId),
      title: examExample.title,
      submitTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      score,
      passingScore: examExample.passingScore,
      isPassed,
      timeUsed,
      message: isPassed ? '恭喜！您已通过考试。' : '很遗憾，您未能通过考试，请继续努力。'
    };
    
    return {
      code: 0,
      message: 'success',
      data: result
    };
  },
  
  // 获取考试结果
  'GET /api/exam/result': (data) => {
    const { examId, userId } = data;
    
    // 使用固定的用户ID作为演示
    const userIdToUse = userId || '123456';
    
    // 检查结果是否存在
    if (!examResults[userIdToUse] || !examResults[userIdToUse][examId]) {
      return {
        code: 404,
        message: '未找到考试结果',
        data: null
      };
    }
    
    return {
      code: 0,
      message: 'success',
      data: examResults[userIdToUse][examId]
    };
  }
};

export default examMock; 