<!--pages/practice/record-detail/record-detail.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
   <!-- {{practiceInfo.date}} -->
  <custom-nav-bar 
    title=" {{practiceInfo.duration}}"
    desc=""
    show-back="{{false}}"
    nav-bar-height="{{navBarHeight}}"
    bind:back="goBack"
  >
    <view slot="rightBtn" class="nav-time">
      题目数量：{{totalNumber}}题
    </view>
  </custom-nav-bar>
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 聊天内容区域 -->
  <chat-container
    wx:else
    messages="{{messages}}"
    total-nav-height="{{navBarHeight}}"
    is-loading="{{isLoading}}"
    page-type="record"
    is-practice-started="{{true}}"
    bind:scrollToUpper="onScrollToUpper"
    isRecordDetail="{{true}}"
  />
  
  <!-- 底部安全区域，适配全面屏手机 -->
  <view class="safe-area-bottom"></view>
</view> 