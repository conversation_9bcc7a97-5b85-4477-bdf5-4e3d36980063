<view class="auth-popup {{visible ? 'visible' : ''}}" ref="authPopup">
  <view class="auth-popup-content" catchtap="preventBubble">
    <view class="auth-popup-header">
      <text class="auth-popup-title">请完成微信授权</text>
      <!-- <view class="auth-popup-close" catchtap="hidePopup">×</view> -->
    </view>
    
    <view class="auth-popup-body">
      <view class="auth-form">
        <!-- 微信头像和昵称 -->
        <view class="wechat-profile">
          <view class="avatar-container">
            <image class="default-avatar" src="{{avatarUrl || '/static/images/user-select.png'}}" mode="aspectFill"></image>
            <button class="avatar-button" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
              <text class="avatar-button-text">更换头像</text>
            </button>
            <text wx:if="{{formErrors.avatar}}" class="error-message">{{formErrors.avatar}}</text>
          </view>
          
          <view class="nickname-container">
            <text class="form-label">微信昵称</text>
            <input 
              class="form-input" 
              wx:if="{{visible}}"
              type="nickname" 
              placeholder="请输入微信昵称" 
              maxlength="10" 
              bindinput="onInputNickname"
              value="{{nickname}}"
            />
            <text wx:if="{{formErrors.nickname}}" class="error-message">{{formErrors.nickname}}</text>
          </view>
        </view>
        
        <!-- 手机号获取 -->
        <!-- <view class="form-item {{formErrors.phone ? 'has-error' : ''}}">
          <text class="form-label">手机号</text>
          <view class="phone-container">
            <text class="phone-display" wx:if="{{phone}}">{{phone}}</text>
            <button 
              class="wechat-phone-button {{phone ? 'hidden' : ''}}" 
              open-type="getPhoneNumber" 
              bindgetphonenumber="getPhoneNumber"
            >获取微信绑定手机号</button>
          </view>
          <text wx:if="{{formErrors.phone}}" class="error-message">{{formErrors.phone}}</text>
        </view> -->
        
        <!-- 错误提示 -->
        <!-- <view class="error-container" wx:if="{{showError}}">
          <text class="global-error">{{errorMessage}}</text>
        </view> -->
      </view>
    </view>
    
    <view class="auth-popup-footer">
      <button 
        class="auth-btn auth-cancel" 
        catchtap="hidePopup"
      >取消</button>
      <button 
        class="auth-btn auth-submit" 
        catchtap="submitAuth"
        loading="{{loading}}"
        disabled="{{loading}}"
      >授权并继续</button>
    </view>
  </view>
</view> 