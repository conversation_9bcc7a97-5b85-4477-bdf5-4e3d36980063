<!--pages/promotion/report.wxml-->
<view class="container">
<!-- 自定义导航 -->
  <!-- <custom-nav-bar 
    title="{{title}}"
    desc="{{desc}}"
    status-bar-height="{{statusBarHeight}}"
    nav-bar-height="{{navBarHeight}}"
    format-time="{{formatTime}}"
    showTime="{{false}}"
    bind:back="goBack"
    titleClass="report-title"
  /> -->
  <view >
  <!-- 考试结果横幅 -->
  <view class="result-banner">
    <!-- 状态标签 -->
    <view class="status-container" wx:if="{{reportData.scoreApprovalStatus=='审核中'&&correctNum>=passScore}}">
      <view class="status-badge  status-pending">
        <text class="iconfont icon-clock"></text>
        <text>成绩审核中</text>
      </view>
    </view>

    <view class="score-circle">
      <text class="score-value">{{accuracy}}%</text>
      <text class="score-label">正确率</text>
    </view>
    <view class="result-title" wx:if="{{reportData}}">{{correctNum>=passScore?'恭喜你，完成考试！':'很遗憾，再接再厉！'}}</view>
    <!-- <view class="result-subtitle">你已成功考试为中级服务员</view> -->
    <view class="pass-badge {{correctNum>=passScore?'pass-badge-pass':'pass-badge-fail'}}"
    wx:if="{{reportData}}">
      <text class="iconfont {{correctNum>=passScore?'icon-check':'icon-cuowu'}}"></text>
      <text>{{correctNum>=passScore?'通过':'未通过'}}</text>
    </view>
  </view>

  <!-- 分数详情 -->
  <!-- <view class="section-title">
    <text class="iconfont icon-chart-bar"></text>
    <text>能力分析</text>
  </view>

  <view class="score-card">
        <view id="radar-container" class="radar-container">
          <canvas canvas-id="radarCanvas" class="radar-canvas"></canvas>
        </view>
  </view> -->
  <view class="section-title">
    <text class="iconfont icon-chart-bar"></text>
    <text>考试结果</text>
  </view>

  <view class="feedback-card">
    <view class="result-container">
      <view class="item">
        <view>题目总数</view>
        <text class="num">{{totalNum}}<text class="unit">题</text></text>
      </view>
        <view class="item correctItem">
          <view>答对</view>
          <text class="num">{{correctNum}}<text class="unit">题</text></text>
        </view>
        <view class="item errorItem">
          <view>答错</view>
          <text class="num">{{errorNum}}<text class="unit">题</text></text>
        </view>
    </view>
    <!-- wx:if="{{from!=='examRecords'}}" -->
    <button class="btn record-btn"  bindtap="goToRecord" >
      <text>查看试卷</text>
    </button>
  </view>

  <!-- AI指导建议 -->
  <view class="section-title">
    <text class="iconfont icon-lightbulb-fill"></text>
    <text>AI指导建议</text>
  </view>

  <view class="feedback-card">
    <view class="feedback-title">
      <text class="iconfont icon-thumbsup"></text>
      <text>整体分析</text>
    </view>
    <view class="feedback-content">
      <!-- <view wx:for="{{feedback.strengths}}" wx:key="index" class="feedback-point">
        <text>{{index + 1}}. {{item}}</text>
      </view> -->
      <view>
        {{reportScore.strengths}}
      </view>
    </view>
  </view>

  <view class="feedback-card">
    <view class="feedback-title">
      <text class="iconfont icon-tool"></text>
      <text>改进建议</text>
    </view>
    <view class="feedback-content">
      <!-- <view wx:for="{{feedback.improvements}}" wx:key="index" class="feedback-point">
        <text>{{index + 1}}. {{item}}</text>
      </view> -->
      <view>
        {{reportScore.improvement_areas}}
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="next-steps">
    <button class="btn back-btn" disabled="{{statusConfirmed}}" bindtap="goBack">
      <!-- <text class="iconfont icon-check"></text> -->
      <text>返回列表</text>
    </button>
  </view>
  </view>
</view>