// api/practice.js
import { get, post } from '../utils/request';


/**
 * 获取练习列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} 练习列表
 */
export const getPracticeList = (params) => {
  return get('/wechat/practice/list', params);
};

/**
 * 获取练习详情
 * @param {String} id - 练习ID
 * @returns {Promise<Object>} 练习详情
 */
export const getPracticeDetail = (id) => {
  return get('/practice/detail', { id });
};

// 提交练习记录
export const submitPracticeRecord = (data) => {
  return post('/practice/submit-record', data);
};

/**
 * 获取练习记录
 * @returns {Promise<Array>} 练习记录列表
 */
export const getPracticeRecords = () => {
  return get('/practice/records');
};

/**
 * 更新用户级别
 * @param {Object} data - 用户数据
 * @returns {Promise<Object>} 更新结果
 */
export const postLevel = (data) => {
  return post('/wechat/user/update-level', data);
};


/**
 * 更新用户岗位
 * @param {Object} data - 用户数据
 * @returns {Promise<Object>} 更新结果
 */
export const postPosition = (data) => {
  return post('/wechat/user/update-position', data);
};

/**
 * 获取练习消息记录
 * @param {String} courseId - 练习ID
 * @returns {Promise<Array>} 消息记录列表
 */
export const getCourseMessages = (courseId) => {
  return get('/practice/course/messages', { courseId });
};

/**
 * 发送用户消息
 * @param {string} courseId - 练习ID
 * @param {string} content - 消息内容
 * @returns {Promise<Object>} - AI回复
 */
export const sendUserMessage = (courseId, content) => {
  return post('/practice/course/send-message', { courseId, content });
};

/**
 * 获取推荐练习
 * @param {Number} limit - 限制数量
 * @returns {Promise<Array>} 推荐练习列表
 */
export const getRecommendedCourses = (limit = 3) => {
  return get('/practice/recommended', { limit });
};

/**
 * 调用Dify出题工作流，获取练习题目
 * @param {Object} params - 请求参数
 * @param {string} params.practiceId - 练习ID
 * @param {string} params.userId - 用户ID
 * @param {string} params.difficulty - 难度级别(可选)
 * @returns {Promise<Object>} - 返回题目信息
 */
export const getPracticeQuestion = (params) => {
  return post(
    '/wechat/practice/question',
    params,
    {
      isDify:false
    },
  );
};

/**
 * 调用Dify练习工作流，提交用户回答并获取评估
 * @param {Object} params - 请求参数
 * @param {string} params.questionId - 题目ID
 * @param {string} params.question - 题目内容
 * @param {string} params.standardAnswer - 标准答案
 * @param {string} params.userAnswer - 用户回答
 * @param {string} params.userId - 用户ID
 * @returns {Promise<Object>} - 返回评估结果
 */
export const evaluatePracticeAnswer = (params) => {
  return post(
    '/wechat/practice/analysis',
    params,
    {
      isDify:false
    },
  );
};

/**
 * 获取实践练习列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} - 练习列表
 */
export const getPracticeCourses = (params) => {
  return get('/practice/courses', params);
};

/**
 * 获取练习详情
 * @param {string} id - 练习ID
 * @returns {Promise<Object>} - 练习详情
 */
export const getPracticeCourseDetail = (id) => {
  return get('/practice/course/detail', { id });
};

/**
 * 获取字典列表
 */
export const getDictList = (params) => {
  return get('/system/dict/data/list', { params });
};

/**
 * 获取我的练习记录
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 练习记录及统计数据
 */
export const getMyInfoPractice = (params) => {
  return get('/wechat/myinfo/practice', params);
};

/**
 * 获取练习记录详情
 * @param {String} id - 记录ID
 * @returns {Promise<Object>} 练习记录详情信息
 */
export const getPracticeRecordDetail = (id) => {
  return get(`/wechat/myinfo/practice/${id}/details`);
};
