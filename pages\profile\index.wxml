<view class="container">
  <!-- 用户资料头部 -->
  <view class="profile-header">
    <view class="user-info-container" bindtap="gotoAuth">
      <view class="avatar-container">
        <view class="avatar-border">
          <text wx:if="{{!userInfo.avatar}}"  class="iconfont icon-user"></text>
          <image wx:else class="avatar-image" src="{{userInfo.avatar}}" mode="aspectFill"></image>
        </view>
      </view>
      <view class="user-details">
        <text class="user-name">{{userInfo.realName}}</text>
        <view class="position-badge" wx:if="{{currentPosition}}">
          <!-- <text class="iconfont icon-utensils"></text> -->
          <text >{{currentPosition}}（{{currentLevel}}）</text>
        </view>
      </view>
    </view>

    <view class="stats-container">
      <view class="stat-item">
        <text class="stat-value">{{currentLevel?currentLevel:'无'}}</text>
        <text class="stat-label">当前级别</text>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{statistic.minutes}}<view class="stat-value-unit">分钟</view></view>
        <text class="stat-label">总练习时长</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{statistic.obtained}}个</text>
        <text class="stat-label">证书数量</text>
      </view>
    </view>

    <!-- 装饰元素 -->
    <view class="sparkle" style="top: 25%; left: 20%;"></view>
    <view class="sparkle" style="top: 15%; right: 25%;"></view>
    <view class="sparkle" style="bottom: 30%; left: 35%;"></view>
    <view class="sparkle" style="bottom: 20%; right: 15%;"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content-section">
    <!-- 能力雷达图 -->
    <view class="radar-container">
      <view class="section-title">
        <text class="iconfont icon-chart-radar"></text>
        <text>当前岗位能力餐考分析</text>
      </view>
      <view class="chart-container" id="radar-container1">
        <!-- {{!showAuthPopup}}{{!showAuthModal}}{{!showSuccessModal}} -->
        <!-- 注意: 需要使用微信小程序兼容的雷达图组件 -->
        <canvas canvas-id="radarCanvas" wx:if="{{!showAuthPopup&&!showAuthModal&&!showSuccessModal}}" class="radar-canvas"></canvas>
      </view>
      <view class="chart-legend">
        <view class="legend-item">
          <view class="legend-color current-level"></view>
          <text>当前水平</text>
        </view>
        <!-- <view class="legend-item">
          <view class="legend-color last-level"></view>
          <text>上次评估</text>
        </view> -->
      </view>
    </view>

    <!-- 菜单项 - 使用循环方式 -->
    <block wx:for="{{menuCategories}}" wx:key="title" wx:for-item="category">
      <view wx:if="{{category.isShow}}">
        <view class="menu-category">{{category.title}}</view>
        <view class="menu-card">
          <view class="menu-item" wx:for="{{category.items}}" wx:key="title" wx:if="{{item.isShow !== false}}" bindtap="navigateToUrl" data-url="{{item.url}}" data-type="{{item.type}}">
            <view class="menu-icon">
              <text class="iconfont {{item.icon}}"></text>
            </view>
            <text class="menu-title">{{item.title}}</text>
            <text class="iconfont icon-right menu-arrow"></text>
          </view>
        </view>
        </view>
    </block>

    <!-- <view class="version-info">
      <text>餐烤餐考 v1.0.0</text>
    </view> -->
  </view>
  <!-- 授权弹窗组件 -->
  <auth-popup
    visible="{{showAuthPopup}}"
    nextPath="{{nextPath}}"
    bindauthsuccess="onAuthSuccess"
    bindclose="onAuthClose"
  />
  <!-- 身份认证弹窗组件 -->
  <identity-verify-popup
    visible="{{showAuthModal}}"
    nextPath="{{nextPath}}"
    bindverifysuccess="onVerifySuccess"
    bindclose="onVerifyClose"
  />
   <!-- 认证成功 -->
  <success-popup
    visible="{{showSuccessModal}}"
  />
</view>
