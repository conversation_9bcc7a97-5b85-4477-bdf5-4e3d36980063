/**
 * 晋升考试相关API的模拟数据
 */

// 晋升考试统计数据
const promotionStats = {
  totalExams: 8,
  completedExams: 5,
  passedExams: 4,
  averageScore: 86.5,
  nextExam: {
    id: 3,
    title: '高级餐厅服务员认证考试',
    deadline: '2024-04-15'
  }
};

// 考试等级
const examLevels = [
  {
    id: 1,
    name: '初级',
    color: '#52c41a'
  },
  {
    id: 2,
    name: '中级',
    color: '#1890ff'
  },
  {
    id: 3,
    name: '高级',
    color: '#faad14'
  },
  {
    id: 4,
    name: '专家',
    color: '#ff4d4f'
  }
];

// 考试类型
const examTypes = [
  {
    id: 1,
    name: '技能认证',
    icon: '/static/icons/exam-skill.png'
  },
  {
    id: 2,
    name: '岗位晋升',
    icon: '/static/icons/exam-promotion.png'
  },
  {
    id: 3,
    name: '专项能力',
    icon: '/static/icons/exam-special.png'
  }
];

// 晋升考试列表
const promotionExams =  [
  {
    id: 1,
    title: '菜品知识',
    subtitle: '了解菜品特点与原料',
    icon: 'icon-food',
    status: 'approved',
    statusText: '已通过',
    iconBg: 'var( --primary-gradient)',
    buttonType: 'take-exam',
    buttonText: '考试',
    buttonIcon: 'icon-edit'
  },
  {
    id: 2,
    title: '客户沟通',
    subtitle: '提升顾客沟通技巧',
    icon: 'icon-paper-plane',
    status: 'waiting',
    statusText: '待申请',
    iconBg: 'linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)',
    buttonType: 'apply-exam',
    buttonText: '申请',
    buttonIcon: 'icon-paper-plane'
  },
  {
    id: 3,
    title: '服务流程',
    subtitle: '顾客服务全流程',
    icon: 'icon-service',
    status: 'pending',
    statusText: '审核中',
    iconBg: 'linear-gradient(135deg, #f6d365 0%, #fda085 100%)',
    buttonType: 'disabled',
    buttonText: '考试',
    buttonIcon: 'icon-edit'
  },
  {
    id: 4,
    title: '投诉处理',
    subtitle: '高效解决顾客投诉',
    icon: 'icon-warning',
    status: 'rejected',
    statusText: '已驳回',
    iconBg: 'linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%)',
    buttonType: 'disabled',
    buttonText: '考试',
    buttonIcon: 'icon-edit'
  },
  {
    id: 5,
    title: '顾客心理',
    subtitle: '洞察顾客消费心理',
    icon: 'icon-heart',
    status: 'approved',
    statusText: '已完成',
    iconBg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    buttonType: 'view-cert',
    buttonText: '查看',
    buttonIcon: 'icon-eye'
  }
];

// 题目类型
const questionTypes = {
  SINGLE_CHOICE: 'single_choice',
  MULTIPLE_CHOICE: 'multiple_choice',
  TRUE_FALSE: 'true_false',
  FILL_BLANK: 'fill_blank',
  SHORT_ANSWER: 'short_answer'
};

// 考试详情（包含题目）
const examDetails = {
  // 初级厨师助理认证考试
  1: {
    basicInfo: promotionExams.find(item => item.id === 1),
    instructions: '本考试旨在评估您掌握的厨房基础操作和烹饪技能。请仔细阅读每道题目，根据您的知识和经验选择最合适的答案。',
    sections: [
      {
        id: 1,
        title: '理论知识',
        questionCount: 30,
        totalScore: 60
      },
      {
        id: 2,
        title: '操作技能',
        questionCount: 20,
        totalScore: 40
      }
    ],
    questions: [
      {
        id: 1,
        sectionId: 1,
        type: questionTypes.SINGLE_CHOICE,
        content: '以下哪种刀具最适合进行精细切割和雕刻工作？',
        options: [
          { id: 'A', content: '菜刀' },
          { id: 'B', content: '砍刀' },
          { id: 'C', content: '水果刀' },
          { id: 'D', content: '雕花刀' }
        ],
        correctAnswer: 'D',
        score: 2
      },
      {
        id: 2,
        sectionId: 1,
        type: questionTypes.MULTIPLE_CHOICE,
        content: '以下哪些是正确的刀具维护方法？（多选）',
        options: [
          { id: 'A', content: '使用后立即清洗并擦干' },
          { id: 'B', content: '定期磨刀以保持锋利' },
          { id: 'C', content: '长时间浸泡在水中消毒' },
          { id: 'D', content: '使用专用刀架存放' },
          { id: 'E', content: '放入洗碗机清洗' }
        ],
        correctAnswer: ['A', 'B', 'D'],
        score: 3
      },
      // 更多题目...
    ]
  },
  // 其他考试详情...
};

// 考试结果详情
const examResults = {
  1: {
    examId: 1,
    title: '初级厨师助理认证考试',
    takenAt: '2023-08-15 10:00:00',
    score: 85,
    passingScore: 60,
    isPassed: true,
    timeUsed: 82, // 分钟
    sections: [
      {
        id: 1,
        title: '理论知识',
        score: 52,
        totalScore: 60,
        correctCount: 26,
        totalCount: 30
      },
      {
        id: 2,
        title: '操作技能',
        score: 33,
        totalScore: 40,
        correctCount: 16,
        totalCount: 20
      }
    ],
    wrongQuestions: [
      {
        id: 5,
        content: '以下哪种调味料最适合提鲜但不增加咸味？',
        yourAnswer: 'A',
        correctAnswer: 'C',
        explanation: '鸡精含有较高钠含量会增加咸味，而味精（谷氨酸钠）主要提供鲜味而不明显增加咸味。'
      },
      {
        id: 12,
        content: '厨房中"mise en place"的含义是什么？',
        yourAnswer: 'B',
        correctAnswer: 'D',
        explanation: '"Mise en place"是法语，意为"把东西放在适当的位置"，在厨房中指的是在烹饪前准备和组织所有原料与工具。'
      }
      // 更多错题...
    ],
    certificate: {
      id: 'CERT-KC2308150001',
      name: '初级厨师助理认证',
      issueDate: '2023-08-16',
      expiryDate: '2025-08-16',
      issuer: '餐烤培训认证中心',
      holderName: '张明',
      level: '初级',
      skills: ['基础刀工', '食材处理', '简单烹饪技巧', '厨房安全操作'],
      imageUrl: '/static/images/certificates/junior-chef-assistant.png',
      verificationCode: 'KC2308150001ZM'
    }
  },
  // 其他考试结果...
};

// API调用处理函数
const promotionMock = {
  // 获取晋升考试统计数据
  'GET /api/promotion/stats': (data) => {
    return {
      code: 0,
      message: 'success',
      data: promotionStats
    };
  },
  
  // 获取考试列表
  'GET /api/promotion/modules': (data) => {
    console.log('获取考试列表')
    let result = [...promotionExams];
    
    return {
      code: 0,
      message: 'success',
      data: result
    };
  },
  
  // 获取考试等级列表
  'GET /api/promotion/levels': (data) => {
    return {
      code: 0,
      message: 'success',
      data: examLevels
    };
  },
  
  // 获取考试类型列表
  'GET /api/promotion/types': (data) => {
    return {
      code: 0,
      message: 'success',
      data: examTypes
    };
  },
  
  // 获取考试详情
  'GET /api/promotion/exam/detail': (data) => {
    const examId = parseInt(data.id);
    const detail = examDetails[examId];
    
    if (detail) {
      return {
        code: 0,
        message: 'success',
        data: detail
      };
    } else {
      return {
        code: 404,
        message: '考试不存在',
        data: null
      };
    }
  },
  
  // 获取考试结果
  'GET /api/promotion/exam/result': (data) => {
    const examId = parseInt(data.id);
    const result = examResults[examId];
    
    if (result) {
      return {
        code: 0,
        message: 'success',
        data: result
      };
    } else {
      return {
        code: 404,
        message: '考试结果不存在',
        data: null
      };
    }
  },
  
  // 提交考试答案
  'POST /api/promotion/exam/submit': (data) => {
    const { examId, answers, timeUsed } = data;
    
    // 模拟评分
    const exam = promotionExams.find(item => item.id === parseInt(examId));
    const passingScore = exam?.passingScore || 60;
    
    // 随机生成一个及格的分数
    const score = Math.floor(Math.random() * (100 - passingScore + 1)) + passingScore;
    const isPassed = score >= passingScore;
    
    return {
      code: 0,
      message: 'success',
      data: {
        examId,
        score,
        passingScore,
        isPassed,
        timeUsed,
        message: isPassed ? '恭喜您通过考试！' : '很遗憾，您未能通过考试。',
        certificate: isPassed ? {
          id: `CERT-EX${examId}${Date.now().toString().substr(-6)}`,
          name: exam?.title.replace('考试', '证书'),
          issueDate: new Date().toISOString().split('T')[0]
        } : null
      }
    };
  },
  
  // 预约考试
  'POST /api/promotion/exam/register': (data) => {
    const { examId } = data;
    
    return {
      code: 0,
      message: 'success',
      data: {
        examId,
        registrationId: `REG${Date.now()}`,
        status: 'registered',
        message: '考试预约成功！'
      }
    };
  }
};

module.exports = {
  promotionMock
}; 