// api/user.js
import { get, post } from '../utils/request';

/**
 * 检查微信授权状态
 * @param {string} openId - 用户openId
 * @returns {Promise<Object>} 授权状态
 */
export const checkAuthorization = (openId) => {
  return get('/wechat/check-authorization', { openId });
};

/**
 * 微信登录获取openId
 * @param {string} code - 微信登录返回的临时登录凭证
 * @returns {Promise<Object>} 包含openid和session_key的对象
 */
export const wxLogin = (code) => {
  return get('/wechat/openid', { jsCode:code });
};

/**
 * 获取用户信息
 * @param {string} openId - 用户openId（可选）
 * @returns {Promise<Object>} 用户信息
 */
export const getUserInfo = (openId) => {
  // const headers = {};
  // if (openId) {
  //   headers['X-WX-OPENID'] = openId;
  // }
  return get('/wechat/user',{openId});
};

/**
 * 验证令牌有效性
 * @param {string} token - 授权令牌
 * @returns {Promise<Object>} 验证结果
 */
export const validateToken = (token) => {
  const headers = {
    'Authorization': `Bearer ${token}`
  };
  return post('/user/validate-token', {}, headers);
};

/**
 * 更新用户信息
 * @param {Object} data - 用户信息数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateUserInfo = (data) => {
  return post('/user/update', data);
};

/**
 * 获取用户练习记录
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} 练习记录列表
 */
export const getUserPracticeRecords = (params) => {
  return get('/user/practice-records', params);
};

/**
 * 获取用户练习统计数据
 * @returns {Promise<Object>} 练习统计数据
 */
export const getUserPracticeStats = () => {
  return get('/user/practice-stats');
};

/**
 * 获取用户徽章列表
 * @returns {Promise<Array>} 徽章列表
 */
export const getUserBadges = () => {
  return get('/user/badges');
};

/**
 * 更新用户通知设置
 * @param {Object} settings - 通知设置
 * @returns {Promise<Object>} 更新结果
 */
export const updateNotificationSettings = (settings) => {
  return post('/user/notification/settings', settings);
};

/**
 * 提交反馈
 * @param {Object} data - 反馈数据
 * @returns {Promise<Object>} 提交结果
 */
export const submitFeedback = (data) => {
  return post('/wechat/myinfo/createFeedback', data);
};

/**
 * 获取历史反馈列表
 * @returns {Promise<Array>} 历史反馈列表
 */
export const getHistoryFeedback = () => {
  return get('/wechat/myinfo/feedbacks');
};

/**
 * 获取用户技能雷达数据
 * @returns {Promise<Object>} 技能雷达数据
 */
export const getUserSkillRadar = (data) => {
  // return get('/user/skill-radar');
  return get('/dimension/data',data);
};

/**
 * 获取用户徽章统计
 * @returns {Promise<Object>} 徽章统计
 */
export const getUserBadgeStats = () => {
  return get('/user/badge-stats');
};

/**
 * 获取徽章详情
 * @param {string} id - 徽章ID
 * @returns {Promise<Object>} 徽章详情
 */
export const getBadgeDetail = (id) => {
  return get('/user/badge/detail', { id });
};

/**
 * 用户授权
 * @param {Object} data - 授权信息
 * @param {string} data.nickname - 用户昵称
 * @param {string} data.phone - 手机号码
 * @returns {Promise<Object>} 授权结果
 */
export const authorize = (data) => {
  return post('/wechat/authenticate', data);
};

/**
 * 解析微信获取的手机号
 * @param {string} code - 微信获取手机号返回的code
 * @returns {Promise<Object>} 解析结果，包含phoneNumber字段
 */
export const decodePhone = (code) => {
  return post('/wechat/phone-number', { code });
};

// 获取用户考试统计数据
export const getUserExamStats = () => {
  return get('/wechat/myinfo/exam/statistics');
};

// 判断是否已认证
export const checkIdentity = (data) => {
  return get('/wechat/check-identity',data);
};

// 提交身份认证
export const submitIdentity = (data) => {
  return post('/wechat/verify-identity', data);
};


/**
 * 获取级别列表
 * @returns {Promise<Array>} 级别列表
 */
export const getLevels = () => {
  return get('/organization/level/options');
};

/**
 * 获取岗位列表
 * @returns {Promise<Array>} 岗位列表
 */
export const getPositions = () => {
  return get('/wechat/practice/getPositionName');
};

/**
 *  晋升岗位列表
 *  */
export const getPositionList = () => {
  return get('/wechat/employee/promotion-positions');
};


/**其他岗位列表 */
export const getOtherPosition = () => {
  return get('/wechat/employee/available-position-names');
};

/**
 * 根据企业邀请码获取企业信息
 * @param {string} inviteCode - 企业邀请码
 * @returns {Promise<Object>} 企业信息
 */
export const getEnterpriseByInviteCode = (inviteCode) => {
  return get(`/wechat/enterprise/info/${inviteCode}`,);
};

/**
 * 获取岗位名称列表（用于申请加入企业）
 * @param {string} enterpriseId - 企业ID（可选）
 * @returns {Promise<Array>} 岗位名称列表
 */
export const getPositionWithLevels = (adminAccessUrl) => {
  return get('/wechat/position/getPositionWithLevels', {},{},{ url:adminAccessUrl });
};

/**
 * 获取岗位等级列表（用于申请加入企业）
 * @param {string} positionName - 岗位名称（可选）
 * @returns {Promise<Array>} 岗位等级列表
 */
export const getPositionLevels = (positionName) => {
  return get('/wechat/enterprise/position-levels', { positionName });
};

/**
 * 提交申请加入企业
 * @param {Object} data - 申请数据
 * @param {string} data.inviteCode - 企业邀请码
 * @param {string} data.realName - 姓名
 * @param {string} data.phone - 手机号
 * @param {string} data.idNumber - 身份证号
 * @param {string} data.department - 归属部门
 * @param {string} data.positionName - 岗位名称
 * @param {string} data.positionLevel - 岗位等级
 * @returns {Promise<Object>} 申请结果
 */
export const applyToEnterprise = (data) => {
  return post('/wechat/apply-to-enterprise', data);
};


// 获取等级列表
export const getLevelList = (positionId) => {
  return get(`/wechat/position/${positionId}/levels`);
};

/** 
 * 判断是否绑定企业的接口(未离职已认证)
*/
export const checkEnterpriseBinding = (openId) => {
  return get('/wechat/check-enterprise-binding', { openId });
};


// 获取部门
export const getTopLevelDepartments = () => {
  return get('/wechat/department/getTopLevelDepartments');
};