import { get } from '../utils/request';

/**
 * 获取徽章列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} 徽章列表
 */
export const getBadgeList = (params) => {
  return get('/badge/list', params);
};

/**
 * 获取徽章分类
 * @returns {Promise<Array>} 徽章分类列表
 */
export const getBadgeCategories = () => {
  return get('/badge/categories');
};

/**
 * 获取徽章详情
 * @param {String} id - 徽章ID
 * @returns {Promise<Object>} 徽章详情
 */
export const getBadgeDetail = (id) => {
  return get(`/badge/detail/${id}`);
};

/**
 * 获取徽章统计信息
 * @returns {Promise<Object>} 徽章统计信息
 */
export const getBadgeStats = () => {
  return get('/badge/stats');
}; 