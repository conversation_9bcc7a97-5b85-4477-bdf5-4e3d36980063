/* pages/practice/record-detail/record-detail.wxss */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;

}

.nav-back {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}


/* 聊天容器 */
.chat-container {
  flex: 1;
  padding: 20rpx;
  padding-top: 60rpx;  /* 减小顶部空白区域 */
  box-sizing: border-box;
  margin-top: 44px;  /* 匹配顶部导航高度 */
}

.chat-list {
  padding-bottom: 30rpx;
}



/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #ffffff;
}




/* 导航栏右侧的时间显示 */
.nav-time {
  font-size: 24rpx;
  color: #666;
} 