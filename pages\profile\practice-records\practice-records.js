// pages/profile/practice-records/practice-records.js
import { getMyInfoPractice } from '../../../api/practice'
import { getOtherPosition,getPositionList,getPositions,getLevels } from '../../../api/user'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabs: ['全部', '本周', '本月', '本年'],
    currentTab: 0,
    positions: ['所有岗位'],
    positionOptions: [], // 用于存储岗位选项对象数组
    positionMap: {}, // 用于存储岗位name-id映射
    positionIndex: 0, // 当前选中的岗位索引
    levels: [],
    levelOptions: [], // 用于存储等级选项对象数组
    levelMap: {}, // 用于存储等级name-id映射
    levelIndex: 0, // 当前选中的等级索引
    selectedPosition: wx.getStorageSync('userInfo').positionName,
    selectedPositionId: wx.getStorageSync('userInfo').positionId, // 存储选中岗位的ID
    selectLevel: wx.getStorageSync('userInfo').currentLevelName,
    selectedLevelId: wx.getStorageSync('userInfo').currentLevel, // 存储选中等级的ID
    records: [],
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    loadingMore: false, // 专门用于底部加载更多的loading状态
    statsArray: [],
    scrollViewHeight: 0, // scroll-view的高度
    // defaultsSet: false // 标记是否已设置默认值
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
     // 只有在未设置过默认值的情况下才重新加载数据
    // if (!this.data.defaultsSet) {
      this.loadData();
    // }

    // 页面显示时重新计算高度
      setTimeout(() => {
      this.calculateScrollViewHeight();
      }, 1000);

  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
  },


  // 计算scroll-view的高度
  calculateScrollViewHeight() {
    const that = this;

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;


    // 创建查询选择器
    const query = wx.createSelectorQuery().in(this);

    // 查询需要测量的元素
    query.select('#statsSummary').boundingClientRect();
    query.select('#filterContainer').boundingClientRect();
    query.select('#tabNavigator').boundingClientRect();

    query.exec(function(res) {

      if (res && res.length >= 3) {
        // 获取各元素的高度
        const statsSummaryHeight = res[0] ? res[0].height : 0;
        const filterContainerHeight = res[1] ? res[1].height : 0;
        const tabNavigatorHeight = res[2] ? res[2].height : 0;

        // 计算其他元素的总高度
        const otherElementsHeight = statsSummaryHeight + filterContainerHeight + tabNavigatorHeight;

        // 计算scroll-view的高度 (窗口高度 - 其他元素高度)
        // 额外减去20是为了留一些底部安全距离和可能的边距
        const scrollViewHeight = windowHeight - otherElementsHeight - 20;

        // 更新scroll-view的高度
        that.setData({
          scrollViewHeight: scrollViewHeight > 0 ? scrollViewHeight : 300 // 确保不会是负值，如果计算错误则给一个默认值
        });
      } else {
        // 如果查询失败，使用默认高度
        that.setData({
          scrollViewHeight: windowHeight * 0.6 // 使用窗口高度的60%作为默认值
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
   
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 实现下拉刷新
    this.refreshRecords();
  },

  // 刷新记录列表
  refreshRecords() {
    this.loadRecords(true);

    // 停止下拉刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 800); // 延迟停止，让刷新动画显示更自然
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  async loadData() {
    try {
      // 先显示加载状态
      wx.showLoading({
        title: '加载中...',
        mask: true
      });
      this.setData({
        selectedPosition: wx.getStorageSync('userInfo').positionName,
        selectedPositionId: wx.getStorageSync('userInfo').positionId, // 存储选中岗位的ID
        selectLevel: wx.getStorageSync('userInfo').currentLevelName,
        selectedLevelId: wx.getStorageSync('userInfo').currentLevel,
      })
     
    

      // 先加载岗位和等级数据
      await Promise.all([
        this.fetchPositions(),
        this.fetchLevels()
      ]);

      // 设置默认值
      this.setDefaultPosition();
      this.setDefaultLevel();

      // 最后加载记录
      this.setData({ defaultsSet: true });
    this.getStaticData();
      this.loadRecords(true);

      // 隐藏加载状态
      wx.hideLoading();
    } catch (error) {
      console.error('加载数据失败：', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载数据失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 获取岗位列表
  async fetchPositions() {
    try {
      const response = await getPositionList();
      const response2= await getOtherPosition()
      // const otherPosition = await getOtherPosition();
      if (response && response.rows) {
        const rows = [...response.rows,...response2.rows];
        // 构建岗位名称数组和映射对象
        const positionOptions = rows.map(item => item.name);
        const positionMap = {};
        // const otherPositionOptions = otherPosition.rows&&otherPosition.rows.map(item => item.name);

        rows.forEach(item => {
          positionMap[item.name] = item.id;
        });
        // otherPosition.rows&&otherPosition.rows.forEach(item => {
        //   positionMap[item.name] = item.id;
        // });
        // 添加所有岗位前检查一下当前岗位是否在列表中
        const positions = ['所有岗位', ...positionOptions];
        let positionIndex = 0; // 默认是"所有岗位"
        console.log("this.data.selectedPosition",this.data.selectedPosition,positions); 
        if (this.data.selectedPosition) {
          // 当前岗位在列表中，计算索引（+1是因为前面加了"所有岗位"）
          positionIndex = rows.findIndex(item => item.name === this.data.selectedPosition) + 1;
          console.log("positionIndex",positionIndex); 
          if (positionIndex <= 0) positionIndex = 0;
        }

        this.setData({
          positions: positions,
          positionOptions,
          positionMap,
          positionIndex: positionIndex
        }, () => {
          // 在数据更新完成后，设置默认岗位
          this.setDefaultPosition();
        });
      }
    } catch (error) {
      console.error('获取岗位列表失败：', error);
    }
  },

  // 获取等级列表
  async fetchLevels() {
    try {
      const response = await getLevels();

      if (response) {

        // 构建等级名称数组和映射对象
        const levelOptions = response.map(item => item.name);
        const levelMap = {};

        response.forEach(item => {
          levelMap[item.name] = item.id;
        });


        // 添加所有等级前检查一下当前等级是否在列表中
        const levels = ['所有等级', ...levelOptions];
        let levelIndex = 0; // 默认是"所有等级"

        if (this.data.selectLevel) {
          // 当前等级在列表中，计算索引（+1是因为前面加了"所有等级"）
          levelIndex = levelOptions.findIndex(item => item === this.data.selectLevel) + 1;
          if (levelIndex <= 0) levelIndex = 0;
        }

        this.setData({
          levels: levels,
          levelOptions,
          levelMap,
          levelIndex: levelIndex
        }, () => {
          // 在数据更新完成后，设置默认等级
          this.setDefaultLevel();
        });
      }
    } catch (error) {
      console.error('获取等级列表失败：', error);
    }
  },

  // 设置默认岗位
  setDefaultPosition() {
    // 如果已经设置过默认值，则不再设置
    // if (this.data.defaultsSet) return;


    // 如果有岗位名称，直接使用
    if (this.data.selectedPosition) {
      // 查找名称在positions数组中的索引
      const positionIndex = this.data.positions.findIndex(item => item === this.data.selectedPosition);

      if (positionIndex >= 0) {
        // 获取对应的ID
        const positionId = this.data.positionMap[this.data.selectedPosition];

        // 先直接设置名称，确保UI显示
        this.setData({
          selectedPosition: this.data.selectedPosition
        });

        // 然后设置索引和ID
        setTimeout(() => {
          this.setData({
            selectedPositionId: positionId,
            positionIndex: positionIndex
          });
        }, 100);
      }
    }
  },

  // 设置默认等级
  setDefaultLevel() {
    // 如果已经设置过默认值，则不再设置
    // if (this.data.defaultsSet) return;


    // 如果有等级名称，直接使用
    if (this.data.selectLevel) {
      // 查找名称在levels数组中的索引
      const levelIndex = this.data.levels.indexOf(this.data.selectLevel);

      if (levelIndex >= 0) {
        // 获取对应的ID
        const levelId = this.data.levelMap[this.data.selectLevel];

        // 先直接设置名称，确保UI显示
        this.setData({
          selectLevel: this.data.selectLevel
        });

        // 然后设置索引和ID
        setTimeout(() => {
          this.setData({
            selectedLevelId: levelId,
            levelIndex: levelIndex
          });
        }, 100);
      }
    }

    // 在设置完默认值后，加载记录
    // if (!this.data.defaultsSet) {
    //   // 延迟设置，确保上面的UI更新已经完成
    //   setTimeout(() => {
    //     this.setData({ defaultsSet: true });
    //     this.loadRecords(true);
    //   }, 200);
    // }
  },

  // 查找部分匹配的项目
  findPartialMatch(name, list) {
    if (!name || !list || list.length === 0) return null;

    // 尝试全字匹配
    for (const item of list) {
      if (item === name) return item;
    }

    // 尝试包含匹配
    for (const item of list) {
      if (item.includes(name) || name.includes(item)) return item;
    }

    return null;
  },

  async getStaticData() {
    // 模拟请求延迟
    const result = await getMyInfoPractice();
    if(result.stats){
      this.setData({
        statsArray: [
          { label: '总练习题数', value: result.stats.totalQuestions },
          { label: '累计时长', value: result.stats.totalDurationFormatted }
        ]
      });
    }


  },
  // 加载练习记录
  async loadRecords(refresh = false) {
    // 如果是加载更多，则使用loadingMore状态
    // 如果是刷新或首次加载，则使用loading状态
    if (refresh) {
      if (this.data.loading) return;
      this.setData({ loading: true });
    } else {
      if (this.data.loadingMore) return;
      this.setData({ loadingMore: true });
    }

    try {
      // 如果是刷新，则重置页码
      if (refresh) {
        this.setData({
          currentPage: 1,
          records: [], // 清空现有记录
          hasMore: true // 重置加载更多状态
        });
      }

      // 组装参数
      const params = {
        page: this.data.currentPage,
        pageSize: this.data.pageSize,
        positionid: this.data.selectedPositionId === -1 ? '' : this.data.selectedPositionId,
        levelid: this.data.selectedLevelId === -1 ? '' : this.data.selectedLevelId,
        timeRange: this.data.tabs[this.data.currentTab] !== '全部' ? this.data.tabs[this.data.currentTab] : ''
      };

      // 模拟请求延迟
      const startTime = Date.now();
      const result = await getMyInfoPractice(params);

      // 确保加载时间至少为800ms，让loading效果显示足够长
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, 800 - elapsedTime);

      setTimeout(() => {
        if (result) {
          const { records, stats } = result;

          // 更新练习记录
          if (refresh) {
            this.setData({
              records: records || []
            });
          } else {
           this.setData({
              records: [...this.data.records, ...(records || [])]
            });
          }

          // 更新统计数据
          // if (stats) {
            // this.setData({
            //   statsArray: [
            //     { label: '总练习次数', value: stats.totalPracticeCount.toString() },
            //     { label: '累计时长', value: stats.totalDurationFormatted }
            //   ]
            // });
          // }
          // 更新分页信息
          const hasMore = records && records.length === this.data.pageSize;


          this.setData({
            hasMore: hasMore,
            currentPage: this.data.currentPage + 1
          });
        } else {
          this.setData({ hasMore: false });
        }

        // 重置loading状态
        if (refresh) {
          this.setData({ loading: false });
        } else {
          this.setData({ loadingMore: false });
        }

      }, remainingTime);

    } catch (error) {
      console.error('获取练习记录失败：', error);
      wx.showToast({
        title: '获取记录失败，请重试',
        icon: 'none',
        duration: 2000
      });

      // 重置loading状态
      if (refresh) {
        this.setData({ loading: false });
      } else {
        this.setData({ loadingMore: false });
      }
    }
  },

  switchTab(e) {
    const { value } = e.detail;
    this.setData({
      currentTab: value
    });
    this.loadRecords(true);
  },

  onPositionChange(e) {
    const index = e.detail.value;
    const position = this.data.positions[index];
    const positionId = position === '所有岗位' ? -1 : this.data.positionMap[position];

    this.setData({
      selectedPosition: position,
      selectedPositionId: positionId,
      positionIndex: index
    });
    this.loadRecords(true);
  },

  onCategoryChange(e) {
    const index = e.detail.value;
    const level = this.data.levels[index];
    const levelId = level === '所有等级' ? -1 : this.data.levelMap[level];

    this.setData({
      selectLevel: level,
      selectedLevelId: levelId,
      levelIndex: index
    });
    this.loadRecords(true);
  },

  // 滚动事件监听
  onScroll(e) {
    const { scrollTop, scrollHeight } = e.detail;
    // 延迟触发加载更多，避免频繁触发
    if (this.data.hasMore && !this.data.loading && !this.data.loadingMore) {
      this.loadRecords(false);
    } 
  },

  continuePractice(e) {
    const record = e.currentTarget.dataset.record;
    this.navigateToPractice(record);
  },

  /**
   * 跳转到练习记录详情页面
   */
  navigateToPractice(record) {
    if (!record || !record.id) {
      wx.showToast({
        title: '记录数据无效',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/practice/record-detail/record-detail?id=${record.id}&date=${record.createTime || ''}&duration=${record.totalDuration || ''}`,
      fail: (err) => {
        console.error('跳转到练习记录详情页面失败:', err);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  }
})
