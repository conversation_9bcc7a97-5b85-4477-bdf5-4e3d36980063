.tab-container {
  display: flex;
  padding: 0 32rpx;
  background: #fff;
  white-space: nowrap;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.tab {
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  display: inline-block;
}

.tab.active {
  color: #a18cd1;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 32rpx;
  right: 32rpx;
  height: 4rpx;
  background: #a18cd1;
  border-radius: 4rpx;
} 