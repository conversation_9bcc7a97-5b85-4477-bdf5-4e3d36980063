// api/exam.js
import { get, post } from '../utils/request';

/**
 * 获取考试列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} 考试列表
 */
export const getExamList = (params) => {
  return get('/exam/list', params);
};

/**
 * 获取考试详情
 * @param {String} examId - 考试ID
 * @returns {Promise<Object>} 考试详情
 */
export const getExamDetail = (examId) => {
  return get(`/exam/detail/${examId}`);
};

/**
 * 开始考试
 * @param {String} examId - 考试ID
 * @returns {Promise<Object>} 开始考试结果
 */
export const startExam = (examId) => {
  return post(`/exam/start/${examId}`);
};

/**
 * 提交考试答案
 * @param {Object} data - 答案数据
 * @returns {Promise<Object>} 提交结果
 */
export const submitExamAnswer = (data) => {
  return post('/exam/submit', data);
};

/**
 * 获取考试报告
 * @param {String} reportId - 报告ID
 * @returns {Promise<Object>} 考试报告
 */
export const getExamReport = (reportId) => {
  return get(`/exam/report/${reportId}`);
}; 
