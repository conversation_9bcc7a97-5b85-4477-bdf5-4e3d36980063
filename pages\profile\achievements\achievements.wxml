<!--pages/profile/achievements/achievements.wxml-->
<view class="page-container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <view class="user-avatar">
          <text wx:if="{{!userInfo.avatar}}"  class="iconfont icon-user"></text>
        <image  wx:else
          src="{{userInfo.avatar}}" 
          mode="aspectFill"
          lazy-load="{{true}}"
        ></image>
      </view>
      <view class="user-details">
        <text class="user-name">{{userInfo.realName}}</text>
        <view class="user-subtitle">
          <text class="iconfont icon-award"></text>
          已点亮勋章2/30</view>
      </view>
    </view>
    
    <!-- <view class="achievement-stats">
      <view class="stat-item">
        <text class="stat-number">{{achievementStats.obtainedCount}}</text>
        <text class="stat-label">已获得</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{achievementStats.totalCount}}</text>
        <text class="stat-label">总勋章</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{achievementStats.completionRate}}%</text>
        <text class="stat-label">完成度</text>
      </view>
    </view> -->
  </view>

  <!-- 勋章列表卡片 -->
  <view class="achievements-card">
    <view class="card-header">
      <text class="card-title">成就勋章</text>
      <!-- <text class="card-subtitle">点击查看获取条件</text> -->
    </view>
    
    <view class="achievements-grid">
      <view 
        wx:for="{{achievements}}" 
        wx:key="id"
        class="achievement-item {{item.isObtained ? 'obtained' : 'not-obtained'}}"
        bindtap="onAchievementTap"
        data-achievement="{{item}}"
      >
        <view class="achievement-icon-wrapper">
          <image 
            class="achievement-icon {{!item.isObtained?'gray':''}}" 
            src="{{item.icon}}" 
            mode="aspectFit"
            lazy-load="{{true}}"
          ></image>
          <!-- <view class="achievement-overlay" wx:if="{{!item.isObtained}}"></view> -->
        </view>
        <text class="achievement-name">{{item.name}}</text>
        <text class="achievement-tit">{{item.obtainedTime?item.obtainedTime+' 获得':'暂未获得'}}</text>
      </view>
    </view>
    
    <!-- 空状态 -->
     <empty-tip text="暂无勋章数据" wx:if="{{achievements.length === 0 && !isLoading}}"></empty-tip>
  </view>

</view>

<!-- 勋章详情弹出层 -->
<view class="modal-mask" wx:if="{{showDetailModal}}" bindtap="closeDetailModal" catchtouchmove="preventTouchMove">
  <view class="modal-container" catchtap="">
    <view class="modal-content" wx:if="{{selectedAchievement}}">
      <view class="achievement-detail">
        <view class="detail-icon-wrapper">
          <image 
            class="detail-icon {{!item.isObtained?'gray':''}}" 
            src="{{selectedAchievement.icon}}" 
            mode="aspectFit"
          ></image>
          <!-- <view class="detail-overlay" wx:if="{{!selectedAchievement.isObtained}}"></view> -->
        </view>
        
        <text class="detail-name">{{selectedAchievement.name}}</text>
        <!-- <text class="detail-description">{{selectedAchievement.description}}</text> -->
        
        <view class="detail-info">
          <view class="info-section" wx:if="{{!selectedAchievement.isObtained}}">
            <text class="info-content">获取条件：{{selectedAchievement.condition}}</text>
          </view>
          
          <view class="info-section" wx:if="{{selectedAchievement.isObtained}}">
            <text class="info-content">获得时间：{{selectedAchievement.obtainedTime}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="modal-close">
      <text class="iconfont icon-close " bindtap="closeDetailModal"></text>
    </view>
  </view>
</view>
