import authBehavior from '../behaviors/auth-behavior'
Component({
  behaviors: [authBehavior],
  data: {
    selected:  0,
    isIdentityVerified: false, // 是否已认证标志
    list: [], // 实际展示的tab列表，将根据授权状态动态生成
    // 安全区域相关
    statusBarHeight: 0,
    isIOS: false,
    isFullScreen: false,
    safeAreaBottom: 0
  },
  options: {
    styleIsolation: 'apply-shared'  // 使用apply-shared让组件可以使用app.wxss中的样式
  },

  lifetimes: {
    attached: function() {
      this.getSystemInfo();
      // 检查认证状态
      this.checkStatus();
      // 初始化时从本地存储获取选中的tab
      const selectedTab = wx.getStorageSync('selectedTab');
      if (selectedTab !== undefined && selectedTab !== null) {
        this.setData({
          selected: parseInt(selectedTab, 10) || 0
        });
      }
    },
    ready: function() {
      // this.setCurrentTab();
    }
  },
  pageLifetimes: {
    show: function() {
      // 检查全局状态中的授权状态
      // const app = getApp();
      // if (app && app.globalData && app.globalData.isIdentityVerified !== undefined) {
      //   const isIdentityVerified = !!app.globalData.isIdentityVerified;
      //   if (isIdentityVerified !== this.data.isIdentityVerified) {
      //     this.checkStatus();
      //   }
      // }
      
      // // 检查是否需要刷新TabBar (由auth.js中的updateTabBarAfterAuth设置)
      // const needsRefresh = wx.getStorageSync('tabBarNeedsRefresh');
      // if (needsRefresh) {
        this.checkStatus();
      //   wx.removeStorageSync('tabBarNeedsRefresh'); // 清除刷新标志
      // }
      
      // this.setCurrentTab();
    }
  },
  onLoad: function() {
    this.setCurrentTab();
  },
  methods: {
    // 检查授权状态
    checkStatus() {
      // 获取本地存储的授权信息，或从全局状态获取
      const isIdentityVerified = wx.getStorageSync('isIdentityVerified');
      // 检查全局状态中的授权信息
      const app = getApp();
      const globalIsIdentityVerified = app && app.globalData ? !!app.globalData.isIdentityVerified : false;
      
      // 如果本地存储和全局状态不一致，以本地存储为准，并更新全局状态
      if (isIdentityVerified !== globalIsIdentityVerified && app && app.globalData) {
        app.globalData.isIdentityVerified = isIdentityVerified;
      }

      // 根据授权状态生成tab列表
      let filteredTabs;
      // let selectedTab = wx.getStorageSync('selectedTab') || 0;
      if (isIdentityVerified) {
        // 已认证，显示所有tab
        filteredTabs = this.data.allTabs;
        // selectedTab = wx.getStorageSync('selectedTab')==1?3:wx.getStorageSync('selectedTab')
      } else {
        // 未认证，只显示首页和我的
        filteredTabs = [
          this.data.allTabs[0], // 首页
          this.data.allTabs[3]  // 我的
        ];
      }
      // 更新组件数据
      this.setData({
        isIdentityVerified: isIdentityVerified,
        list: filteredTabs,
        selected: wx.getStorageSync('selectedTab') || 0
      }, () => {
        // 在数据更新完成后，强制一次UI刷新
        wx.nextTick(() => {
          // 强制重新渲染TabBar
          this.setData({
            forceUpdate: new Date().getTime()
          });
        });
      });
      
      
      return isIdentityVerified;
    },
    
    
    
    // 获取系统信息，判断设备类型和安全区域
    getSystemInfo() {
      try {
        const systemInfo = wx.getSystemInfoSync();
        // 判断是否为iOS设备
        const isIOS = systemInfo.system.toLowerCase().includes('ios');
        // 判断是否为全面屏手机
        const isFullScreen = !!(systemInfo.safeArea && systemInfo.safeArea.bottom > 0 && 
                        systemInfo.safeArea.bottom !== systemInfo.screenHeight);
        // 判断是否为刘海屏iPhone
        const isIphoneX = isIOS && /iPhone X|iPhone 11|iPhone 12|iPhone 13|iPhone 14|iPhone 15/.test(systemInfo.model);
        
        // 计算底部安全区高度
        const safeAreaBottom = isIphoneX ? 50 : 20;
        console.log('isphone', safeAreaBottom)
        this.setData({
          statusBarHeight: systemInfo.statusBarHeight || 0,
          isIOS: isIOS,
          isFullScreen: isFullScreen || isIphoneX,
          safeAreaBottom: safeAreaBottom
        });
        
      
      } catch (e) {
        console.error('获取系统信息失败', e);
      }
    },
    
    // 设置当前选中的tab
    setCurrentTab() {
      const selectedTab = wx.getStorageSync('selectedTab');
      let selected = selectedTab !== undefined && selectedTab !== null ? parseInt(selectedTab, 10) : 0;
      
      // 获取当前页面路径，用于更精确地确定选中项
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage ? currentPage.route : '';
      
      // 如果没有从存储中获取到选中状态，则根据当前页面路径判断
      let tabIndex = selected;
      // if (currentRoute && (!selectedTab && selectedTab !== 0)) {
      //   // 检查当前路径是否匹配某个tab的pagePath
      //   const matchingTab = this.data.list.findIndex(item => 
      //     currentRoute === item.pagePath.substring(1) || 
      //     currentRoute.startsWith(item.pagePath.substring(1))
      //   );
        
      //   if (matchingTab !== -1) {
      //     tabIndex = matchingTab;
      //     // 更新本地存储
      //     wx.setStorageSync('selectedTab', tabIndex);
      //   }
      // }
      console.log('tabIndex,setcurrent', tabIndex)
      // 确保选中的tab在实际可见的tab列表中
      if (tabIndex >= this.data.list.length) {
        tabIndex = 0; // 默认回到首页
        wx.setStorageSync('selectedTab', tabIndex);
      }
      // 更新选中状态
      this.setData({
        selected: tabIndex
      });
    },
    
    // 切换tabbar项
    switchTab(e) {
      const index = e.currentTarget.dataset.index;
      const path = e.currentTarget.dataset.path;
      if (this.data.selected === index) {
        return; // 当前已经是选中状态，无需处理
      }
      this.checkStatus()
      // 立即更新本地存储和选中状态，提高响应速度
      wx.setStorageSync('selectedTab', index);
      this.setData({
        selected: index
      });
      
      
      // 使用微信的switchTab API进行页面切换
      wx.switchTab({
        url: path,
        success: () => {
          // 切换成功后滚动到顶部
          wx.pageScrollTo({
            scrollTop: 0,
            duration: 300
          });
        },
        fail: (error) => {
          console.error('Tab切换失败:', error, '尝试使用备选方案');
          // 尝试使用redirectTo作为备选方案
          wx.redirectTo({
            url: path,
            fail: (err) => {
              console.error('备选重定向也失败:', err);
              // 恢复原来的选中状态
              const oldSelected = wx.getStorageSync('selectedTab') || 0;
              this.setData({
                selected: parseInt(oldSelected, 10)
              });
            }
          });
        }
      });
    }
  }
}) 