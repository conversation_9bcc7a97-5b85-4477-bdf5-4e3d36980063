import { authorize, decodePhone } from '../../api/user';
import { saveAuthInfo, uploadFileWithAuth } from '../../utils/auth';
const authBehavior = require('../../behaviors/auth-behavior')

import request from '../../utils/request';

Component({
  behaviors: [authBehavior],
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    // 下一个页面路径
    nextPath: {
      type: String,
      value: ''
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible': function(visible) {
      // 当visible变为false时，重置表单
      if (visible === false) {
        this.resetForm();
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    nickname: '',
    phone: '',
    avatar: '',
    loading: false,
    errorMessage: '',
    showError: false,
    formErrors: {
      nickname: '',
      phone: '',
      avatar: ''
    },
    avatarUrl:''
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 重置表单数据和验证
    resetForm() {
      this.setData({ 
        nickname: '',
        phone: '',
        avatar: '',
        avatarUrl: '',
        loading: false,
        errorMessage: '',
        showError: false,
        formErrors: {
          nickname: '',
          phone: '',
          avatar: ''
        }
      });
    },
    
    // 选择头像回调
    onChooseAvatar(e) {
      const { avatarUrl } = e.detail;
      // 从request.js中获取baseURL
      const baseURL = request.config.baseURL;
      const imgURL = request.config.imgURL;
      
      // 上传头像，使用完整的API路径
      const uploadTask = uploadFileWithAuth(avatarUrl, `${baseURL}/wechat/user/upload`);
      uploadTask.then(res => {
        this.setData({
          avatar:res.data.fileUrl,
          avatarUrl:imgURL+res.data.fileUrl
        })
        console.log('上传头像成功', res);
      }).catch(err => {
        console.error('上传头像失败', err);
      });
      
      this.setData({
        avatar: avatarUrl,
        'formErrors.avatar': '',
        showError: false
      });
    },

    // 输入昵称
    onInputNickname(e) {
      console.log('onInputNickname', e)
      this.setData({ 
        nickname: e.detail.value,
        'formErrors.nickname': '',
        showError: false
      });
    },

    // 获取手机号
    getPhoneNumber(e) {
      console.log('getPhoneNumber', e)
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        this.setData({
          'formErrors.phone': '获取手机号失败，请重试',
          showError: true
        });
        return;
      }

      // 获取code，服务端解密获取手机号
      const { code } = e.detail;
      
      // 设置loading状态
      this.setData({ loading: true });
      
      // 调用API接口解析手机号
      decodePhone(code).then(res => {
        // 根据utils/request.js的处理，这里直接获取到了data部分
        if (res && res.phoneNumber) {
          this.setData({
            phone: res.phoneNumber,
            'formErrors.phone': '',
            showError: false
          });
        } else {
          this.setData({
            'formErrors.phone': '获取手机号失败，请重试',
            showError: true
          });
        }
      }).catch(err => {
        console.error('解析手机号失败:', err);
        this.setData({
          'formErrors.phone': '网络错误，请重试',
          showError: true
        });
      }).finally(() => {
        this.setData({ loading: false });
      });
    },

    // 验证昵称格式
    validateNickname() {
      if (!this.data.nickname) {
        this.setData({ 'formErrors.nickname': '请输入微信昵称' });
        return false;
      }
      return true;
    },

    // 验证头像是否已选择
    validateAvatar() {
      if (!this.data.avatar) {
        this.setData({ 'formErrors.avatar': '请选择头像' });
        return false;
      }
      return true;
    },

    // 验证表单
    validateForm() {
      const nicknameValid = this.validateNickname();
      // const avatarValid = this.validateAvatar();
      // TODO:手机号开关
      // if (!this.data.phone) {
      //   this.setData({ 
      //     'formErrors.phone': '请获取手机号',
      //     showError: true
      //   });
      //   return false;
      // }
      
      
      return nicknameValid ;
    },

    // 提交授权
    submitAuth() {
      if (!this.validateForm()) {
        return;
      }
      
      this.setData({ loading: true });
      
      const authData = {
        nickname: this.data.nickname,
        phone: this.data.phone,
        avatar: this.data.avatar,
        openId: wx.getStorageSync('openId')
      };
      
      authorize(authData).then(res => {
        if (res && res.success) {
          wx.showToast({
            title: '授权成功',
            icon: 'success'
          });
          // 保存token和授权信息
          saveAuthInfo(res);
          
          // 更新tabBar状态，使其显示全部tab
          // this.updateTabBarAfterAuth();
          
          // 关闭弹窗
          this.hidePopup();
          this.setData({
            isAuthorized: true,
          })
          
          // 通知外部组件授权成功
          this.triggerEvent('authsuccess', {...res, nextPath: this.data.nextPath});
        } else {
          this.setData({ 
            errorMessage: '授权失败，请重试',
            showError: true,
            loading: false
          });
        }
      }).catch(err => {
        console.error('授权失败:', err);
        this.setData({ 
          errorMessage: err.message || '授权失败，请重试',
          showError: true,
          loading: false
        });
      });
    },

    // 关闭弹窗
    hidePopup() {
      // this.setData({ visible: false });
      this.triggerEvent('close',{type:'showAuthPopup'});
    },

    // 点击协议链接
    openAgreement() {
      wx.navigateTo({
        url: '/pages/agreement/agreement'
      });
    },

    // 点击隐私政策链接
    openPrivacyPolicy() {
      wx.navigateTo({
        url: '/pages/privacy/privacy'
      });
    },

    // 阻止弹窗内点击事件冒泡
    preventBubble() {
      return;
    }
  }
}); 