/**
 * 授权状态管理工具
 */

/**
 * 检查用户是否已授权
 * @returns {Boolean} 授权状态
 */
export const isAuthorized = () => {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    return !!(token && userInfo);
};

/**
 * 保存授权信息
 * @param {Object} authData - 授权数据
 * @param {String} authData.token - 授权令牌
 * @param {Object} authData.userInfo - 用户信息
 */
export const saveAuthInfo = (authData) => {
  // if (!authData || !authData.token || !authData.userInfo) {
  //   console.error('授权数据格式错误');
  //   return false;
  // }
  
  // try {
    wx.setStorageSync('token', authData.token);
    wx.setStorageSync('isAuthorized', true);
    
    // 更新app全局状态
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.isIdentityVerified = true;  // 确保全局状态更新
    }
    
    
    return true;
  // } catch (error) {
  //   console.error('保存授权信息失败', error);
  //   return false;
  // }
};


/**
 * 获取授权用户信息
 * @returns {Object|null} 用户信息
 */
export const getAuthUserInfo = () => {
  try {
    return wx.getStorageSync('userInfo');
  } catch (error) {
    console.error('获取用户信息失败', error);
    return null;
  }
};

/**
 * 获取授权令牌
 * @returns {String|null} 授权令牌
 */
export const getToken = () => {
  try {
    return wx.getStorageSync('token');
  } catch (error) {
    console.error('获取授权令牌失败', error);
    return null;
  }
};

/**
 * 检查页面是否需要授权
 * @param {String} url - 页面路径
 * @returns {Boolean} 是否需要授权
 */
export const pageRequiresAuth = (url) => {
  // 不需要授权的页面列表 - 首页和登录页等
  const publicPages = [
    'pages/index/index',
    'pages/about/about'
  ];

  // 移除路径中的参数部分
  let cleanUrl = url;
  if (url.includes('?')) {
    cleanUrl = url.split('?')[0];
  }
  
  // 检查是否在公开页面列表中
  for (const page of publicPages) {
    if (cleanUrl.includes(page)) {
      return false;
    }
  }
  
  // 默认其他页面都需要授权
  return true;
};

/**
 * 带授权的文件上传
 * @param {String} filePath - 要上传的文件路径
 * @param {String} url - 上传的服务器地址
 * @param {String} name - 服务器接收文件的表单名称
 * @param {Object} formData - 附加的表单数据
 * @returns {Object} 包含上传任务对象和Promise的结果
 */
export const uploadFileWithAuth = (filePath, url, name = 'file', formData = {}) => {
  // 检查用户是否已授权
  // if (!isAuthorized()) {
  //   return Promise.reject(new Error('用户未授权，无法上传文件'));
  // }

  // // 获取授权令牌
  const token = getToken();
  // if (!token) {
  //   return Promise.reject(new Error('获取授权令牌失败'));
  // }

  // 执行上传
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url,
      filePath,
      name:'file',
      // formData:{
      //   'fileName':filePath
      // },
      header: {
        'Authorization':`Bearer ${token}`,
      'token': `${token}`
    },

    success(res) {
      // 微信小程序返回的data是字符串形式，需要转换为对象
      let data;
      try {
        data = JSON.parse(res.data);
      } catch (e) {
        data = res.data;
      }

      if (res.statusCode !== 200) {
        // uploadTask._reject && uploadTask._reject(new Error(data.message || '上传失败'));
        reject(new Error(data.message || '上传失败'));
      } else {
        // uploadTask._resolve && uploadTask._resolve(data);
        resolve(data);
      }
    },
    fail(err) {
      console.error('文件上传失败', err);
      // uploadTask._reject && uploadTask._reject(err);
      reject(err);
      }
    });
  });
};



