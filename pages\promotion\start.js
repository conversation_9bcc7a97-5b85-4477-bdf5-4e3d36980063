// pages/promotion/start.js
import { getExamDetail, startExam } from '../../api/promotion'
const authBehavior = require('../../behaviors/auth-behavior')

Page({
  data: {
    knowledgeBaseId:'',
    examDetail: null,
    isLoading: false,
    isStarting: false,
    examInfo:null,
  },
  behaviors: [authBehavior],


  onLoad: function(options) {
    if (options.knowledgeBaseId) {
      this.setData({
        knowledgeBaseId:options.knowledgeBaseId,
        examInfo:wx.getStorageSync('examInfo'),
        title:options.title,
      });
      // TODO: 获取详情
      // this.loadExamDetail();
    }
  },

  async loadExamDetail() {
    this.setData({
      isLoading: true
    });

    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      const result = await getExamDetail(this.data.examId);
      this.setData({
        examDetail: result,
        isLoading: false
      });
    } catch (error) {
      console.error('获取考试详情失败：', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });

      this.setData({
        isLoading: false
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 开始考试
  async startExam() {
    if (this.data.isStarting) return;

    this.setData({
      isStarting: true
    });

      // 使用授权导航
      const result = await startExam({
        positionName: this.data.examInfo.positionName,
        positionLevel: this.data.examInfo.positionLevel,
        knowledgeBaseId:this.data.knowledgeBaseId
      });
      console.log(result.examId,this.data.knowledgeBaseId)
      if (result) {
      // this.navigateToAuthRequiredPage(`/pages/promotion/exam?examId=${result.examId}&knowledgeBaseId=${this.data.knowledgeBaseId}`);
      wx.redirectTo({
        url: `/pages/promotion/exam?examId=${result.examId}&knowledgeBaseId=${this.data.knowledgeBaseId}`
      });

      }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
})
