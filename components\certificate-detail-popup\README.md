# 证书详情弹框组件 (certificate-detail-popup)

一个可复用的证书详情弹框组件，支持图片预览、长按保存、分享等功能。

## 功能特性

- ✅ 证书图片展示
- ✅ 图片预览功能
- ✅ 长按保存到相册
- ✅ 分享功能
- ✅ 加载状态显示
- ✅ 错误状态处理
- ✅ 权限管理
- ✅ 响应式设计
- ✅ 动画效果

## 使用方法

### 1. 在页面配置中引入组件

```json
{
  "usingComponents": {
    "certificate-detail-popup": "/components/certificate-detail-popup/certificate-detail-popup"
  }
}
```

### 2. 在WXML中使用组件

```xml
<certificate-detail-popup
  visible="{{showCertPopup}}"
  certificateUrl="{{certificateUrl}}"
  title="证书详情"
  showActions="{{true}}"
  showShareButton="{{true}}"
  showSaveButton="{{true}}"
  maskClosable="{{true}}"
  bind:close="onCloseCertPopup"
  bind:share="onShareCert"
  bind:savesuccess="onSaveSuccess"
  bind:savefail="onSaveFail"
  bind:longpress="onLongPress"
  bind:imagetap="onImageTap"
/>
```

### 3. 在JavaScript中处理事件

```javascript
Page({
  data: {
    showCertPopup: false,
    certificateUrl: ''
  },

  // 显示证书详情
  showCertificateDetail(certificateUrl) {
    this.setData({
      certificateUrl: certificateUrl,
      showCertPopup: true
    });
  },

  // 关闭弹框
  onCloseCertPopup() {
    this.setData({
      showCertPopup: false
    });
  },

  // 分享证书
  onShareCert(e) {
    console.log('分享证书:', e.detail);
    // 处理分享逻辑
  },

  // 保存成功
  onSaveSuccess(e) {
    console.log('保存成功:', e.detail);
    // 处理保存成功逻辑
  },

  // 保存失败
  onSaveFail(e) {
    console.log('保存失败:', e.detail);
    // 处理保存失败逻辑
  },

  // 长按事件
  onLongPress(e) {
    console.log('长按事件:', e.detail);
    // 处理长按逻辑
  },

  // 图片点击
  onImageTap(e) {
    console.log('图片点击:', e.detail);
    // 处理图片点击逻辑
  }
})
```

## 属性说明

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| visible | Boolean | false | 是 | 是否显示弹框 |
| certificateUrl | String | '' | 是 | 证书图片URL |
| title | String | '证书详情' | 否 | 弹框标题 |
| imageMode | String | 'widthFix' | 否 | 图片显示模式 |
| showActions | Boolean | false | 否 | 是否显示操作按钮 |
| showShareButton | Boolean | false | 否 | 是否显示分享按钮 |
| showSaveButton | Boolean | true | 否 | 是否显示保存按钮 |
| maskClosable | Boolean | true | 否 | 是否允许点击遮罩关闭 |
| loading | Boolean | false | 否 | 加载状态 |
| error | String | '' | 否 | 错误信息 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭弹框 | - |
| share | 分享按钮点击 | {certificateUrl} |
| savesuccess | 保存成功 | {certificateUrl} |
| savefail | 保存失败 | {error, certificateUrl} |
| downloadfail | 下载失败 | {error, certificateUrl} |
| longpress | 图片长按 | {certificateUrl} |
| imagetap | 图片点击 | {certificateUrl} |
| imageload | 图片加载完成 | - |
| imageerror | 图片加载失败 | - |
| retry | 重试按钮点击 | - |

## 样式定制

组件提供了完整的样式，支持响应式设计。如需定制样式，可以通过以下CSS变量进行调整：

```css
/* 在页面的wxss文件中覆盖样式 */
.certificate-detail-popup .cert-container {
  /* 自定义容器样式 */
}

.certificate-detail-popup .certificate-image {
  /* 自定义图片样式 */
}
```

## 注意事项

1. **权限处理**：组件会自动处理相册权限，首次保存时会引导用户开启权限
2. **图片格式**：支持常见的图片格式（jpg、png、gif等）
3. **网络图片**：确保图片URL可访问，组件会处理加载失败的情况
4. **性能优化**：大图片会自动进行优化显示
5. **兼容性**：支持微信小程序基础库2.0+

## 更新日志

### v1.0.0
- 初始版本
- 支持基础的图片展示和保存功能
- 支持长按操作菜单
- 支持权限管理
- 支持响应式设计
