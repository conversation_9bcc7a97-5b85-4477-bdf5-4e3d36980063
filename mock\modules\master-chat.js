/**
 * AI大师助手相关API的模拟数据
 */

// 聊天会话列表
const chatSessions = [
  {
    id: 'session_001',
    title: '烹饪技巧咨询',
    lastMessageTime: '2024-03-14 15:30:25',
    lastMessage: '谢谢你的解答，我明白了如何控制火候。',
    unreadCount: 0
  },
  {
    id: 'session_002',
    title: '菜单设计建议',
    lastMessageTime: '2024-03-13 10:45:18',
    lastMessage: '下面我为你推荐几种搭配方案...',
    unreadCount: 1
  },
  {
    id: 'session_003',
    title: '食材采购问题',
    lastMessageTime: '2024-03-10 16:20:45',
    lastMessage: '新鲜的海鲜应该具备以下特征...',
    unreadCount: 0
  }
];

// 助手角色列表
const assistantRoles = [
  {
    id: 'role_001',
    name: '烹饪大师',
    avatar: '/static/images/master-chat/cooking-master.png',
    description: '精通中西餐烹饪技巧，可以解答各类烹饪问题，提供菜谱和烹饪建议',
    expertise: ['中餐技法', '西餐技法', '食材搭配', '烹饪技巧'],
    isDefault: true
  },
  {
    id: 'role_002',
    name: '餐厅管理顾问',
    avatar: '/static/images/master-chat/management-consultant.png',
    description: '拥有丰富的餐厅运营和管理经验，可以提供管理建议和问题解决方案',
    expertise: ['人员管理', '成本控制', '服务优化', '运营策略']
  },
  {
    id: 'role_003',
    name: '营养师',
    avatar: '/static/images/master-chat/nutritionist.png',
    description: '专业的营养学知识，可以提供健康饮食建议和营养搭配方案',
    expertise: ['营养分析', '健康饮食', '特殊人群膳食', '食材营养价值']
  },
  {
    id: 'role_004',
    name: '食品安全专家',
    avatar: '/static/images/master-chat/food-safety-expert.png',
    description: '专注于食品安全领域，提供安全操作和卫生管理指导',
    expertise: ['安全规范', '卫生标准', '风险防控', '法规遵从']
  }
];

// 常见问题类别
const faqCategories = [
  {
    id: 'cat_001',
    name: '烹饪技巧',
    icon: '/static/icons/faq-cooking.png'
  },
  {
    id: 'cat_002',
    name: '食材选购',
    icon: '/static/icons/faq-ingredients.png'
  },
  {
    id: 'cat_003',
    name: '餐厅管理',
    icon: '/static/icons/faq-management.png'
  },
  {
    id: 'cat_004',
    name: '设备使用',
    icon: '/static/icons/faq-equipment.png'
  },
  {
    id: 'cat_005',
    name: '菜单设计',
    icon: '/static/icons/faq-menu.png'
  }
];

// 常见问题列表
const faqs = [
  {
    id: 'faq_001',
    categoryId: 'cat_001',
    question: '如何掌握炒菜的火候？',
    popularity: 985
  },
  {
    id: 'faq_002',
    categoryId: 'cat_001',
    question: '煎牛排的最佳温度和时间是多少？',
    popularity: 876
  },
  {
    id: 'faq_003',
    categoryId: 'cat_002',
    question: '如何挑选新鲜的海鲜？',
    popularity: 910
  },
  {
    id: 'faq_004',
    categoryId: 'cat_002',
    question: '不同种类蔬菜的保鲜方法有哪些？',
    popularity: 854
  },
  {
    id: 'faq_005',
    categoryId: 'cat_003',
    question: '如何有效控制餐厅的食材成本？',
    popularity: 930
  },
  {
    id: 'faq_006',
    categoryId: 'cat_003',
    question: '餐厅服务员培训的关键点有哪些？',
    popularity: 875
  },
  {
    id: 'faq_007',
    categoryId: 'cat_004',
    question: '商用烤箱的正确使用和维护方法？',
    popularity: 832
  },
  {
    id: 'faq_008',
    categoryId: 'cat_004',
    question: '厨房刀具的保养技巧有哪些？',
    popularity: 865
  },
  {
    id: 'faq_009',
    categoryId: 'cat_005',
    question: '如何设计一份平衡营养又有特色的菜单？',
    popularity: 920
  },
  {
    id: 'faq_010',
    categoryId: 'cat_005',
    question: '季节性菜单应该如何规划？',
    popularity: 880
  }
];

// 聊天消息样本
const chatMessageSamples = {
  'session_001': [
    {
      id: 'msg_001_001',
      sessionId: 'session_001',
      sender: 'user',
      content: '我想知道炒菜时如何控制火候？',
      timestamp: '2024-03-14 15:23:45',
      status: 'sent'
    },
    {
      id: 'msg_001_002',
      sessionId: 'session_001',
      sender: 'assistant',
      roleId: 'role_001',
      content: '控制火候是烹饪中的关键技巧，以下是一些建议：\n\n1. 辨别火力大小：小火温度约120-130℃，中火约150-160℃，大火约200℃以上。\n\n2. 食材与火候匹配：\n- 肉类煎炒通常先用大火封住表面，再转中小火熟透\n- 蔬菜快炒多用大火，保持脆嫩\n- 炖煮类菜品先大火煮开，再转小火慢炖\n\n3. 观察锅中变化：\n- 油温适中时，放入葱蒜会立即发出"滋滋"声\n- 炒菜时食材颜色变化均匀，没有焦糊\n\n4. 实用技巧：\n- 不同食材放锅的顺序很重要，先硬后软\n- 用锅铲翻炒的频率也能控制受热程度\n- 适时加盖或加水调节温度\n\n掌握火候需要不断实践，建议从简单的菜品开始尝试，逐渐积累经验。',
      timestamp: '2024-03-14 15:25:10',
      status: 'sent'
    },
    {
      id: 'msg_001_003',
      sessionId: 'session_001',
      sender: 'user',
      content: '谢谢你的解答，我明白了如何控制火候。',
      timestamp: '2024-03-14 15:30:25',
      status: 'sent'
    }
  ],
  'session_002': [
    {
      id: 'msg_002_001',
      sessionId: 'session_002',
      sender: 'user',
      content: '我需要为一个商务晚宴设计菜单，大约20人，有什么推荐？',
      timestamp: '2024-03-13 10:35:20',
      status: 'sent'
    },
    {
      id: 'msg_002_002',
      sessionId: 'session_002',
      sender: 'assistant',
      roleId: 'role_001',
      content: '为20人的商务晚宴设计菜单需要考虑正式感、多样性和平衡性。以下是我的建议：\n\n**前菜/开胃菜(3-4种)**\n- 时令沙拉拼盘\n- 熏三文鱼配芥末酱\n- 凉拌海鲜塔\n- 精致小点心拼盘\n\n**主菜(4-5种)**\n- 肉类：牛排卷配红酒汁、香煎羊排\n- 家禽：香草烤鸡、橙汁烤鸭\n- 海鲜：清蒸鲈鱼、黑椒虾仁\n\n**配菜(3-4种)**\n- 时蔬：芦笋、西兰花、蘑菇\n- 淀粉：松露土豆泥、意大利烩饭\n\n**甜点(2-3种)**\n- 水果拼盘\n- 提拉米苏\n- 巧克力熔岩蛋糕\n\n**饮品**\n- 红酒、白葡萄酒\n- 果汁、矿泉水\n- 咖啡、茶\n\n您有任何特殊要求或偏好吗？比如预算限制、特殊饮食需求、主题等，我可以进一步调整建议。',
      timestamp: '2024-03-13 10:40:15',
      status: 'sent'
    },
    {
      id: 'msg_002_003',
      sessionId: 'session_002',
      sender: 'user',
      content: '这个建议很好，但有几位客人是素食者，能否提供一些素食选项？',
      timestamp: '2024-03-13 10:42:30',
      status: 'sent'
    },
    {
      id: 'msg_002_004',
      sessionId: 'session_002',
      sender: 'assistant',
      roleId: 'role_001',
      content: '下面我为你推荐几种适合商务晚宴的高品质素食选项，可以与之前建议的菜单一起提供：\n\n**素食前菜**\n- 藜麦沙拉配石榴和杏仁\n- 烤蔬菜塔配巴萨米克酱\n- 松露香菇酱配全麦脆饼\n\n**素食主菜**\n- 菌菇risotto（意大利烩饭）\n- 烤茄子卷配松子和香草\n- 印度香料烤花菜配藏红花米饭\n- 素食千层面配自制番茄酱\n\n**素食甜点**\n- 椰奶芒果布丁\n- 水果挞配薄荷\n\n这些素食选项营养均衡且精致美观，与荤食搭配提供，可以确保所有宾客都能享用到美味佳肴。建议在菜单上标注素食选项，方便客人选择。',
      timestamp: '2024-03-13 10:45:18',
      status: 'sent',
      relatedLinks: [
        {
          title: '高端素食菜单设计',
          url: '/pages/courses/detail?id=12005'
        },
        {
          title: '商务宴会布置指南',
          url: '/pages/articles/detail?id=35089'
        }
      ]
    }
  ]
};

// 生成完整聊天记录
const generateChatMessages = (sessionId, count = 10) => {
  // 如果有样本对话，则直接返回
  if (chatMessageSamples[sessionId]) {
    return chatMessageSamples[sessionId];
  }
  
  // 否则生成随机对话
  const messages = [];
  const session = chatSessions.find(s => s.id === sessionId);
  const roleId = 'role_001'; // 默认使用烹饪大师角色
  
  const userQuestions = [
    '如何做出完美的蛋炒饭？',
    '西餐摆盘有哪些基本原则？',
    '厨房刀具应该如何挑选？',
    '食材保鲜的最佳方法是什么？',
    '如何提高菜品的色香味？'
  ];
  
  const assistantAnswers = [
    '做完美蛋炒饭的关键是：\n1. 使用隔夜或冷却的米饭\n2. 先单独炒蛋，保持半熟状态\n3. 大火快炒，保持锅热\n4. 米粒要充分分离\n5. 调味适中，不要过湿\n\n最重要的是控制火候和翻炒技巧，每粒米饭都均匀裹上蛋液才算成功。',
    '西餐摆盘基本原则包括：\n1. 遵循"三点法则"，形成视觉三角形\n2. 主菜、配菜、酱汁位置协调\n3. 注重色彩搭配和对比\n4. 高度和层次感的营造\n5. 盘子留白，不要过于拥挤\n6. 装饰点缀要适度\n\n记住，简约往往更显高级。',
    '厨房刀具选购建议：\n1. 材质：高碳不锈钢较为理想\n2. 重量：平衡舒适，握感顺手\n3. 刀刃：锋利度是首要考虑因素\n4. 功能：厨师刀、切片刀、削皮刀等基础刀具优先\n5. 品牌：知名品牌通常有质量保证\n\n建议亲自握持后再购买，找到最适合自己手型的刀具。',
    '不同食材保鲜方法：\n1. 绿叶蔬菜：包裹湿纸巾，放入保鲜袋中冷藏\n2. 根茎类：避光阴凉处保存，某些不适合冷藏\n3. 肉类：分小份冷冻，使用前缓慢解冻\n4. 海鲜：最好当天食用，必要时放冰上冷藏\n5. 水果：大部分常温存放，熟透的放冰箱延长保质期\n\n正确保鲜可以最大限度保留食材营养和风味。',
    '提升菜品色香味的技巧：\n1. 色泽：注重烹饪时机掌握，适当使用天然色素\n2. 香气：调料与配料搭配，香料提前爆香\n3. 口味：五味平衡，层次丰富\n4. 质地：火候控制得当，保持食材最佳状态\n5. 摆盘：注重色彩和构图\n\n烹饪是综合艺术，需要不断实践和创新。'
  ];
  
  // 生成对话
  for (let i = 0; i < count; i++) {
    const isUser = i % 2 === 0;
    const timestamp = new Date(new Date().getTime() - (count - i) * 300000).toISOString().replace('T', ' ').substring(0, 19);
    
    if (isUser) {
      messages.push({
        id: `${sessionId}_msg_${i+1}`,
        sessionId,
        sender: 'user',
        content: userQuestions[i/2 % userQuestions.length],
        timestamp,
        status: 'sent'
      });
    } else {
      messages.push({
        id: `${sessionId}_msg_${i+1}`,
        sessionId,
        sender: 'assistant',
        roleId,
        content: assistantAnswers[(i-1)/2 % assistantAnswers.length],
        timestamp,
        status: 'sent'
      });
    }
  }
  
  return messages;
};

// 推荐内容
const recommendations = [
  {
    id: 'rec_001',
    type: 'course',
    title: '中式烹饪基础入门',
    imageUrl: '/static/images/courses/chinese-cooking-basics.jpg',
    link: '/pages/courses/detail?id=10001',
    reason: '根据您的烹饪技巧咨询，推荐该基础练习'
  },
  {
    id: 'rec_002',
    type: 'article',
    title: '火候控制的艺术：从学徒到大师的烹饪秘诀',
    imageUrl: '/static/images/articles/cooking-heat-control.jpg',
    link: '/pages/articles/detail?id=2053',
    reason: '与您咨询的火候控制问题高度相关'
  },
  {
    id: 'rec_003',
    type: 'video',
    title: '大师示范：完美煎牛排的全过程',
    imageUrl: '/static/images/videos/steak-cooking.jpg',
    link: '/pages/videos/detail?id=3078',
    reason: '热门烹饪技巧视频，获得超高评价'
  },
  {
    id: 'rec_004',
    type: 'tool',
    title: '菜单营养计算器',
    imageUrl: '/static/images/tools/menu-calculator.jpg',
    link: '/pages/tools/menu-calculator',
    reason: '根据您的菜单设计需求推荐的实用工具'
  }
];

// 用户反馈选项
const feedbackOptions = [
  {
    id: 'fb_001',
    content: '回答非常有帮助',
    type: 'positive'
  },
  {
    id: 'fb_002',
    content: '专业知识丰富',
    type: 'positive'
  },
  {
    id: 'fb_003',
    content: '解答清晰易懂',
    type: 'positive'
  },
  {
    id: 'fb_004',
    content: '回答不够全面',
    type: 'negative'
  },
  {
    id: 'fb_005',
    content: '信息不够准确',
    type: 'negative'
  },
  {
    id: 'fb_006',
    content: '没有解决我的问题',
    type: 'negative'
  }
];

// API调用处理函数
const masterChatMock = {
  // 获取聊天会话列表
  'GET /api/master-chat/sessions': (data) => {
    return {
      code: 0,
      message: 'success',
      data: chatSessions
    };
  },
  
  // 获取单个会话详情
  'GET /api/master-chat/session/detail': (data) => {
    const { sessionId } = data;
    const session = chatSessions.find(s => s.id === sessionId);
    
    if (session) {
      return {
        code: 0,
        message: 'success',
        data: session
      };
    } else {
      return {
        code: 404,
        message: '会话不存在',
        data: null
      };
    }
  },
  
  // 创建新会话
  'POST /api/master-chat/session/create': (data) => {
    const { title, roleId } = data;
    
    const newSession = {
      id: `session_${Date.now()}`,
      title: title || '新的对话',
      lastMessageTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      lastMessage: '会话已创建，请开始您的提问',
      unreadCount: 0
    };
    
    // 模拟添加到列表
    chatSessions.unshift(newSession);
    
    return {
      code: 0,
      message: 'success',
      data: newSession
    };
  },
  
  // 获取会话消息列表
  'GET /api/master-chat/messages': (data) => {
    const { sessionId, pageSize = 20, pageNum = 1 } = data;
    
    const messages = generateChatMessages(sessionId);
    
    // 简单分页
    const start = (pageNum - 1) * pageSize;
    const end = pageNum * pageSize;
    const pagedMessages = messages.slice(start, end);
    
    return {
      code: 0,
      message: 'success',
      data: {
        list: pagedMessages,
        total: messages.length,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(messages.length / pageSize)
      }
    };
  },
  
  // 发送消息
  'POST /api/master-chat/message/send': (data) => {
    const { sessionId, content, roleId } = data;
    
    // 验证会话存在
    const session = chatSessions.find(s => s.id === sessionId);
    if (!session) {
      return {
        code: 404,
        message: '会话不存在',
        data: null
      };
    }
    
    // 创建新消息
    const newMessage = {
      id: `msg_${Date.now()}`,
      sessionId,
      sender: 'user',
      content,
      timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
      status: 'sent'
    };
    
    // 更新会话最后消息
    session.lastMessage = content;
    session.lastMessageTime = newMessage.timestamp;
    
    return {
      code: 0,
      message: 'success',
      data: newMessage
    };
  },
  
  // 获取AI助手回复
  'POST /api/master-chat/message/reply': (data) => {
    const { sessionId, messageId, roleId = 'role_001' } = data;
    
    // 验证会话存在
    const session = chatSessions.find(s => s.id === sessionId);
    if (!session) {
      return {
        code: 404,
        message: '会话不存在',
        data: null
      };
    }
    
    // 获取角色信息
    const role = assistantRoles.find(r => r.id === roleId) || assistantRoles[0];
    
    // 模拟AI思考时间
    setTimeout(() => {}, 1000);
    
    // 从样本回复中随机选择一个
    const sampleReplies = [
      '根据您的问题，我建议首先考虑食材的新鲜度和质量，这是烹饪成功的基础。接下来是技法的掌握，不同烹饪方法适合不同食材，搭配恰当才能发挥食材最佳风味。温度和时间控制也至关重要，它们直接影响菜品的口感和成熟度。最后，调味要适中，好的调味能提升菜品风味但不应掩盖食材本身的味道。',
      '在餐饮管理中，成本控制是核心要素之一。有效的成本控制包括：仔细规划采购，避免过量库存；标准化菜品制作流程，减少浪费；定期进行库存盘点；培训员工正确处理和储存食材；优化菜单设计，关注高毛利产品；建立供应商评估体系。这些措施综合实施，可以显著提升餐厅的盈利能力。',
      '食品安全是餐饮业的基础，建议从以下几个方面着手：建立完整的HACCP体系；严格执行进货查验制度；设置合理的食材存储区域和温度；员工卫生培训常态化；定期消毒和清洁设备；建立食品安全应急预案。只有确保食品安全，才能赢得顾客的信任和忠诚。',
      '菜单设计需要平衡多方面因素：考虑目标客群的喜好和消费能力；季节性和食材可获得性；厨房设备和团队能力；成本和定价策略；视觉设计和描述语言；竞争对手分析。一份优秀的菜单应当能突显餐厅特色，平衡利润和价值，同时为顾客提供清晰易懂的选择。'
    ];
    
    const replyContent = sampleReplies[Math.floor(Math.random() * sampleReplies.length)];
    
    // 创建回复消息
    const replyMessage = {
      id: `msg_reply_${Date.now()}`,
      sessionId,
      sender: 'assistant',
      roleId,
      content: replyContent,
      timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
      status: 'sent',
      relatedLinks: recommendations.slice(0, 2).map(item => ({
        title: item.title,
        url: item.link
      }))
    };
    
    // 更新会话最后消息
    session.lastMessage = replyContent;
    session.lastMessageTime = replyMessage.timestamp;
    
    return {
      code: 0,
      message: 'success',
      data: replyMessage
    };
  },
  
  // 获取助手角色列表
  'GET /api/master-chat/roles': (data) => {
    return {
      code: 0,
      message: 'success',
      data: assistantRoles
    };
  },
  
  // 获取常见问题类别
  'GET /api/master-chat/faq/categories': (data) => {
    return {
      code: 0,
      message: 'success',
      data: faqCategories
    };
  },
  
  // 获取常见问题列表
  'GET /api/master-chat/faq/list': (data) => {
    let result = [...faqs];
    
    // 按类别筛选
    if (data.categoryId) {
      result = result.filter(item => item.categoryId === data.categoryId);
    }
    
    // 按关键词搜索
    if (data.keyword) {
      const keyword = data.keyword.toLowerCase();
      result = result.filter(item => item.question.toLowerCase().includes(keyword));
    }
    
    // 按热度排序
    result.sort((a, b) => b.popularity - a.popularity);
    
    return {
      code: 0,
      message: 'success',
      data: result
    };
  },
  
  // 获取推荐内容
  'GET /api/master-chat/recommendations': (data) => {
    return {
      code: 0,
      message: 'success',
      data: recommendations
    };
  },
  
  // 获取反馈选项
  'GET /api/master-chat/feedback/options': (data) => {
    return {
      code: 0,
      message: 'success',
      data: feedbackOptions
    };
  },
  
  // 提交反馈
  'POST /api/master-chat/feedback/submit': (data) => {
    const { messageId, options, content } = data;
    
    return {
      code: 0,
      message: 'success',
      data: {
        messageId,
        submittedAt: new Date().toISOString().replace('T', ' ').substring(0, 19),
        status: 'received'
      }
    };
  }
};

export default masterChatMock; 