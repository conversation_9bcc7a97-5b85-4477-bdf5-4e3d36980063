.container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
 
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
  background-color: #fff;
  text-align: center;
}

.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100vw;
  box-sizing: border-box;
}
.main-content {
  width: 100vw;
  max-width: 340px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.visual {
  margin-top: 12px;
  margin-bottom: 10px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, var(--primary-color) 0%, #fbeaff 80%, transparent 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  filter: blur(2px);
}
.icon-bg {
  position: relative;
  background: linear-gradient(135deg, var(--primary-color) 0%, #fbeaff 100%);
  border-radius: 50%;
  width: 96px;
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px 0 rgba(200, 160, 255, 0.15);
  z-index: 1;
}
.icon-bg svg {
  width: 56px;
  height: 56px;
  display: block;
}
.icon-bg .iconfont{
  font-size: 50px;
  color: #fff;
}
.main-title {
  font-size: 22px;
  font-weight: 700;
  margin-top: 6px;
  margin-bottom: 2px;
  color: #5f3dc4;
  letter-spacing: 1px;
  text-align: center;
}
.sub-title {
  font-size: 15px;
  color: var(--primary-color);
  margin-bottom: 10px;
  text-align: center;
}
.exam-info-vertical {
  width: 100%;
  max-width: 340px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 8px;
  margin-top: 50rpx;
}
.exam-capsule {
  /* background: linear-gradient(90deg, #e0cfff 0%, #fbeaff 100%); */
  background: #f8f0fc;
  border-radius: 18px;
  line-height: 70rpx;
  color: var(--primary-color);
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(200, 160, 255, 0.08);
  text-align: center;
  margin-bottom:30rpx;
}
.exam-capsule::-webkit-scrollbar {
  display: none;
}
.exam-info-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: #f8f0fc;
  border-radius: 12px;
  line-height: 70rpx;
  padding:0px 20rpx;
  color: var(--primary-color);
  box-shadow: 0 1px 4px 0 rgba(200, 160, 255, 0.04);
}
.exam-info-label {
  color: var(--primary-color);
  font-size: 13px;
  margin-right: 2px;
}
.exam-info-value {
  color: #5f3dc4;
  font-size: 15px;
  font-weight: bold;
  margin-left:20rpx;
}
.btn-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 12px;
  margin: 40px 0;
}
.btn-main, .btn-secondary {
  width: 48%;
  max-width: 160px;
  min-width: 80px;
  padding: 10px 0;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 8px 0 rgba(200, 160, 255, 0.10);
  border: none;
  outline: none;
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.1s;
}
.btn-main {
  /* background: linear-gradient(90deg, var(--primary-color) 0%, #e599f7 100%); */
  background:var(--primary-gradient);
  color: #fff;
}
.btn-main:active {
  transform: scale(0.97);
  box-shadow: 0 1px 4px 0 rgba(200, 160, 255, 0.08);
}
.btn-secondary {
  background: #fff;
  color: var(--primary-color);
  border: 1.5px solid var(--primary-color);
}
.btn-secondary:active {
  background: #f8f0fc;
}
.encourage {
  color: var(--primary-color);
  font-size: 15px;
  margin-bottom: 16px;
  letter-spacing: 1px;
  text-align: center;
  width: 100%;
  max-width: 340px;
}