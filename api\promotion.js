// api/promotion.js
import { get, post } from '../utils/request';


/**
 * 获取考试列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} 考试列表
 */
export const getExamList = (params) => {
  return get('/promotion/exams', params);
};

/**
 * 获取考试等级列表
 * @returns {Promise<Array>} 考试等级列表
 */
export const getExamLevels = () => {
  return get('/promotion/levels');
};

/**
 * 获取考试类型列表
 * @returns {Promise<Array>} 考试类型列表
 */
export const getExamTypes = () => {
  return get('/promotion/types');
};

/**
 * 获取考试详情
 * @param {String} examId - 考试ID
 * @returns {Promise<Object>} 考试详情
 */
export const getExamDetail = (examId) => {
  return get('/promotion/exam/detail', { id: examId });
};

/**
 * 获取考试结果
 * @param {String} examId - 考试ID
 * @returns {Promise<Object>} 考试结果
 */
export const getExamResult = (examId) => {
  return get('/promotion/exam/result', { id: examId });
};

/**
 * 预约考试
 * @param {String} examId - 考试ID
 * @returns {Promise<Object>} 预约结果
 */
export const registerExam = (examId) => {
  return post('/promotion/exam/register', { examId });
};

/**
 * 提交考试答案
 * @param {String} examId - 考试ID
 * @param {Object} answers - 答案数据
 * @param {Number} timeUsed - 使用时间（分钟）
 * @returns {Promise<Object>} 提交结果
 */
export const submitExam = (examId, answers, timeUsed) => {
  return post('/promotion/exam/submit', {
    examId,
    answers,
    timeUsed
  });
};

/**
 * 获取考试模块列表
 * @returns {Promise<Array>} 考试模块列表
 */
export const getExamModules = () => {
  return get('/promotion/modules',{},{
    enableMock:true
  });
};

/**
 * 申请考试
 * @param {String} examSubject - 考试科目ID
 * @returns {Promise<Object>} 申请结果
 */
export const applyExam = (examSubject) => {
  const openId = wx.getStorageSync('openId') || '12138'; // 获取用户openId
  return post('/wechat/exam/apply', {
    examSubject,
    openId
  });
};

/**
 * 开始考试
 * @param {String} moduleId - 模块ID
 * @returns {Promise<Object>} 开始考试结果
 */
export const startExam = (body) => {
  return post('/wechat/exam/create-exam', body);
};
/**
 * 考试出题
 * @param {String} body
 * @returns {Promise<Object>}
 */
export const getPromotionQuestion = (body) => {
  return post('/wechat/exam/questions/random', body);
};

/**
 * 用户回答后调用
 * @param {String} body
 * @returns {Promise<Object>}
 */
export const answerPromotion = (body) => {
  return post('/wechat/exam/questions/answer', body);
};



/**
 * 获取考试报告
 * @param {String} reportId - 报告ID
 * @returns {Promise<Object>} 考试报告
 */
export const getExamReport = (params) => {
  return post(`/wechat/exam/report`,params);
};

/**
 * 获取证书
 * @param {String} moduleId - 模块ID
 * @returns {Promise<Object>} 证书信息
 */
export const getCertificate = (moduleId) => {
  return get(`/promotion/certificate/${moduleId}`);
};

/**
 * 获取考试记录列表
 * @returns {Promise<Array>} 考试记录列表
 */
export const getExamRecordList = (params) => {
  return get('/wechat/exam/user-exams',params);
};
/*
 * 获取岗位认证信息
 * @returns {Promise<Object>} 岗位认证信息
 */
export const getPositionCertification = (params) => {
  return get('/wechat/exam/position-certification',params);
};

/**
 * 申请考试审核
 * @param {String} knowledgeBaseId - 知识库ID（即examSubject）
 * @param positionName
 * @param positionLevel
 * @returns {Promise<Object>} 申请结果
 */
export const applyExamReview = (knowledgeBaseId,positionName,positionLevel) => {
  return post('/exam/review/miniapp/application', {
    "knowledgeBaseId":knowledgeBaseId,
    "positionName":positionName,
    "positionLevel":positionLevel
  });
};


/**
 * 继续考试
 * @returns {Promise<Object>} 考试报告
 */


export const getContinueExam = (params) => {
  return get(`/wechat/exam/continue-exam`,params);
};

