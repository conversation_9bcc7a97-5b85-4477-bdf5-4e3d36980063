# Loading 状态管理优化

## 🎯 优化目标
确保 `isLoading` 状态的正确管理：
- 用户发送消息时设置 `isLoading: true`
- 流式回答完全结束后设置 `isLoading: false`

## 🔧 修改内容

### 1. 发送消息时设置 Loading
**位置**：`sendMessage` 方法（第959行）

```javascript
this.setData({
  messages: newMessages,
  isLoading: true,        // ✅ 发送消息时设置为 true
  loadingText: "思考中"
});
```

### 2. WebSocket 流式处理中保持 Loading
**位置**：`sendWebSocketData` 方法

**之前**：在发送数据时就设置 `isLoading: false` ❌
```javascript
this.setData({
  messages: newMessages,
  isStreaming: true,
  streamingMessageIndex: messageIndex,
  isLoading: false,  // ❌ 过早设置为 false
  completedQuestions: this.data.completedQuestions + 1
});
```

**现在**：保持 `isLoading: true` 直到流式回答结束 ✅
```javascript
this.setData({
  messages: newMessages,
  isStreaming: true,
  streamingMessageIndex: messageIndex,
  // 保持 isLoading: true，直到流式回答结束 ✅
  completedQuestions: this.data.completedQuestions + 1
});
```

### 3. 流式回答结束时设置 Loading 为 false
**位置**：打字机效果完成回调

```javascript
onComplete: (text) => {
  that.setData({
    messages: newMessages,
    isStreaming: false,
    isLoading: false // ✅ 流式回答结束后设置为 false
  }, () => {
    console.log('🎯 文本输出完毕，语音功能已准备就绪');
  });
}
```

### 4. HTTP 回退模式的 Loading 管理
**位置**：`displayResponseWithStreaming` 方法

**打字机效果完成时：**
```javascript
this.data.typewriterEffect.onComplete = (text) => {
  this.setData({
    isStreaming: false,
    isLoading: false, // ✅ HTTP 回退模式下也要设置为 false
    [`messages[${this.data.streamingMessageIndex}].isStreaming`]: false,
    [`messages[${this.data.streamingMessageIndex}].showNextQuestion`]: true
  });
};
```

**直接显示时：**
```javascript
this.setData({
  [`messages[${this.data.streamingMessageIndex}].content`]: analysis,
  [`messages[${this.data.streamingMessageIndex}].isStreaming`]: false,
  [`messages[${this.data.streamingMessageIndex}].showNextQuestion`]: true,
  isStreaming: false,
  isLoading: false // ✅ 直接显示时也要设置为 false
});
```

### 5. 错误处理中的 Loading 管理
**位置**：WebSocket 错误处理

```javascript
streamingSocket.on('error', (error) => {
  that.setData({
    isStreaming: false,
    isLoading: false,    // ✅ 错误时也要重置 loading
    isSendDisabled: false
  });
});
```

## 📊 完整的状态流程

### WebSocket 流式处理模式
```
用户发送消息
    ↓
isLoading: true (发送时)
    ↓
创建空 AI 消息
    ↓
发送 WebSocket 数据 (保持 isLoading: true)
    ↓
接收流式数据
    ↓
打字机效果显示
    ↓
流式回答完成
    ↓
isLoading: false (完成时) ✅
```

### HTTP 回退模式
```
用户发送消息
    ↓
isLoading: true (发送时)
    ↓
WebSocket 连接失败
    ↓
HTTP 请求 (保持 isLoading: true)
    ↓
收到 HTTP 响应
    ↓
打字机效果显示
    ↓
显示完成
    ↓
isLoading: false (完成时) ✅
```

## 🎯 用户体验

### Loading 状态显示
- **发送消息后**：立即显示 loading 状态
- **流式输出中**：继续显示 loading 状态
- **回答完成后**：隐藏 loading 状态

### 视觉效果
```
发送消息 → [Loading...] → 流式显示文字 → [Loading 消失]
```

用户可以清楚地知道：
1. **消息已发送**：看到 loading 状态
2. **正在处理**：loading 状态持续
3. **处理完成**：loading 消失，可以进行下一步操作

## ✅ 验证方法

### 测试步骤
1. **发送消息**：观察 `isLoading` 是否变为 `true`
2. **流式输出过程**：确认 `isLoading` 保持为 `true`
3. **回答完成**：确认 `isLoading` 变为 `false`

### 成功标准
- ✅ 发送消息时立即显示 loading
- ✅ 流式输出过程中持续显示 loading
- ✅ 回答完全结束后 loading 消失
- ✅ 错误情况下 loading 也能正确重置

## 🚀 优化效果

### 1. 状态一致性
- Loading 状态与实际处理状态完全同步
- 用户不会看到状态不一致的情况

### 2. 用户体验
- 清晰的反馈：用户知道系统正在处理
- 合理的时机：在真正完成时才隐藏 loading

### 3. 功能完整性
- 支持 WebSocket 和 HTTP 两种模式
- 完善的错误处理机制

现在 loading 状态管理已经完全优化，用户体验更加流畅和一致！
