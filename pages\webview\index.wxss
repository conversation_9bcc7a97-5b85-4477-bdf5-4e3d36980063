/* pages/webview/index.wxss */
.container {
  width: 100%;
  height: 100vh;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 30rpx;
  background-color: #f8f8f8;
}

.error-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background-color: #ff6b6b;
  color: #fff;
  font-size: 60rpx;
  font-weight: bold;
  border-radius: 50%;
  margin-bottom: 40rpx;
}

.error-message {
  font-size: 32rpx;
  color: #666;
  text-align: center;
  margin-bottom: 50rpx;
}

.error-button {
  padding: 20rpx 60rpx;
  background-color: #f2f2f2;
  color: #666;
  font-size: 30rpx;
  border-radius: 40rpx;
} 