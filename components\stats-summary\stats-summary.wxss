.summary-box {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border-radius: 32rpx;
  padding: 40rpx 20rpx;
  margin: 32rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
}

.summary-box::before,
.summary-box::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.summary-box::before {
  top: -40rpx;
  right: -40rpx;
  width: 240rpx;
  height: 240rpx;
}

.summary-box::after {
  bottom: -60rpx;
  left: -60rpx;
  width: 300rpx;
  height: 300rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  display:flex;
  justify-content: space-between;
  align-items: center;
}

.stat-grid {
  display: grid;
  gap: 30rpx;
}

.stat-grid.columns-2 {
  grid-template-columns: repeat(2, 1fr);
}

.stat-grid.columns-3 {
  grid-template-columns: repeat(3, 1fr);
}

.stat-item {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 24rpx;
  text-align: center;
}

.stat-item .stat-value {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.stat-item .stat-label {
  font-size: 24rpx;
  opacity: 0.9;
} 