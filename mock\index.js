/**
 * Mock数据系统入口
 * 为小程序提供模拟数据服务
 */

import certificateMock from './modules/certificate';
import badgeMock from './modules/badge';
import { practiceMock } from './modules/practice';
import { promotionMock } from './modules/promotion';
import homeMock from './modules/home';
import masterChatMock from './modules/master-chat';
import examMock from './modules/exam';
import { userMock } from './modules/user';

// 所有API模拟数据
const mockApis = {
  ...certificateMock,
  ...badgeMock,
  ...practiceMock,
  ...promotionMock,
  ...homeMock,
  ...masterChatMock,
  ...examMock,
  ...userMock
  // 可以继续添加其他模块
};

/**
 * 模拟请求处理函数
 * @param {string} url - 请求URL
 * @param {Object} params - 请求参数
 * @param {string} method - 请求方法 (GET, POST等)
 * @returns {Promise} Promise对象
 */
export const mockRequest = (url, params = {}, method = 'GET') => {
  return new Promise((resolve, reject) => {
    // 构建API键名
    const apiKey = `${method} ${url}`;
    
    // 检查是否存在对应的模拟处理函数
    if (mockApis[apiKey]) {
      // 添加随机延迟，模拟网络请求
      setTimeout(() => {
        try {
          // 调用对应的模拟处理函数
          const response = mockApis[apiKey](params);
          resolve(response.data);
        } catch (error) {
          reject({
            code: 500,
            message: '模拟数据处理错误',
            error: error.message
          });
        }
      }, Math.random() * 300 + 100); // 100-400ms随机延迟
    } else {
      // 未找到对应的模拟处理函数
      reject({
        code: 404,
        message: `未找到模拟API: ${apiKey}`
      });
    }
  });
};

export default mockApis; 