/* components/cert-detail-popup/cert-detail-popup.wxss */
/* page{
  font-family: 'Noto Serif SC', serif;

} */
.cert-detail {
  width: 100%;
  /* height: calc(90% - 100rpx); */
  position: relative;
}

/* 长按保存提示 */
.save-tip {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 15px;
  z-index: 10;
  white-space: nowrap;
}

/* 保存中状态 */
.saving-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
}

.saving-content {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.saving-icon {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.saving-content text {
  color: white;
  font-size: 14px;
}

/* 提示弹窗 */
.tips-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 1000;
}

.tips-popup.show {
  opacity: 1;
  visibility: visible;
}

/* 用于生成图片的离屏Canvas */
.cert-canvas {
  position: fixed;
  left: -2000px;
  top: -2000px;
  width: 750px;
  height: 1200px;
  z-index: -1;
}

.theme-gold{
  width:100%;
  /* padding:50px 50rpx; */
  box-sizing: border-box;
  position: relative;
  overflow:hidden;
}
.theme-gold .bg{
  position:absolute;
  width:100%;
  height:100%;
  top:0px;
  left:0px;
}

.certificate-container {
  width: 100%;
  /* position: relative; */
  padding: 30rpx 20rpx;
  box-sizing: border-box;
  margin: 0 auto;
}

.border-frame {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  bottom: 12px;
  border: 2px solid #D4AF37;
  pointer-events: none;
  border-radius: 12px;
}

.border-frame::before {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 1px solid #C0A062;
  border-radius: 14px;
}

.corner-decoration {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 2px solid #D4AF37;
}

.top-left {
  top: 10px;
  left: 10px;
  border-right: none;
  border-bottom: none;
}

.top-right {
  top: 10px;
  right: 10px;
  border-left: none;
  border-bottom: none;
}

.bottom-left {
  bottom: 10px;
  left: 10px;
  border-right: none;
  border-top: none;
}

.bottom-right {
  bottom: 10px;
  right: 10px;
  border-left: none;
  border-top: none;
}

.header {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
  margin-top:350rpx;
  color:#595757;
}
.header .name{
  font-size:60rpx;
  font-weight:bold;
  margin-bottom:30rpx;
}



.title {
  font-size: 28px;
  color: #1B3F8F;
  font-weight:bold;
  margin-bottom: 15px;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 24rpx;
  color: #D4AF37;
  margin-bottom: 8px;
  letter-spacing: 2px;
}

.level {
  font-size: 24rpx;
  color: #fff;
  /* margin: 5px 0; */
  font-weight: 500;
  /* padding: 4px 15px; */
  border-radius: 20px;
  display: inline-block;
  line-height: 1.4;
  margin-top:100rpx;
  position:relative;
  width:80%;
  height:50rpx;
}
.level .text{
  position: absolute;
  left:0px;
  top:0px;
  width:100%;
  line-height: 50rpx;

}
.level_bg{
  height:100%;


}

.course-name {
  font-size: 22rpx;
  /* color: #1B3F8F; */
  margin-top: 15px;
  font-weight:bold;
  padding: 0 20px;
  line-height: 40rpx;
  width:80%;
  margin:0px auto;
}

.content {
  text-align: center;
  margin: 30px 0;
  line-height: 2;
  color:#1B3F8F;
}

.recipient-name {
  font-size: 20px;
  margin: 20px 0;
  font-weight: bold;
}

.certificate-text {
  font-size: 24rpx;
  line-height: 1.8;
  margin: 20px auto;
  padding: 0 10px;
  text-align: center;
}

.certificate-text text {
  text-align: center;
}

.footer {
  width:80%;
  display: flex;
  margin: 100rpx auto;
  position: relative;
  padding: 0 15px;
  justify-content: space-between;
  align-items: flex-end;
}
.certificate .item{
  margin-right:20rpx;
  width:100rpx;
  height:150rpx;
  position: relative;
  float:left;
}
.certificate_bg{
  width:100%;
  height:100%;
}
.certificate .text{
  position: absolute;
  width:50%;
  top:0px;
  font-size:20rpx;
  left:50%;
  transform:translateX(-25%);
  top:20rpx;
  line-height: 25rpx;
  font-weight:bold;
}
.certificate .logo{
  position:absolute;
    width: 50%;
    height:50rpx;
    left:50%;
    margin-left:-25%;
    top:20rpx;

}

.issuer {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 20px;
  width: 100%;
  position: relative;
  padding-bottom: 30px;
}

.issuer-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.issuer-name {
  font-size: 24rpx;
  font-weight:bold;
  padding-bottom: 8px;
  border-bottom: 1px solid #C0A062;
  margin-bottom: 5px;
}

.date-range {
  font-size: 14px;
  color: #9c8464;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align:right;
}

.date-item {
  display: flex;
  align-items: center;
  line-height:30rpx;
  font-size: 22rpx;
}

.date-label {
  color: #9c8464;
}


.certificate-number {
  /* position: absolute;
  bottom: 0;
  left: 0; */
  font-size: 22rpx;
  /* color: #D4AF37; */
  font-weight:bold;
  /* border-top: 1px dashed #C0A062; */
  width: 100%;
  margin:20rpx 0;
}

.watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 80px;
  color: rgba(156, 111, 228, 0.03);
  white-space: nowrap;
  pointer-events: none;
  z-index: 0;
}

.decorative-line {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #D4AF37, transparent);
  margin: 10px 0;
  opacity: 0.5;
}


/* .theme-gold .seal {
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="48" fill="none" stroke="%23D4AF37" stroke-width="2"/><circle cx="50" cy="50" r="44" fill="none" stroke="%23D4AF37" stroke-width="2"/><path d="M50 20l4 12h12l-10 8 4 12-10-8-10 8 4-12-10-8h12z" fill="%23D4AF37"/></svg>') no-repeat center;
} */

.theme-gold .watermark {
  color: rgba(27, 63, 143, 0.03);
}



.certificateUrl{
  width:100%;
  cursor: pointer;
  transition: transform 0.2s ease;
  position: relative;
}

/* 长按提示效果 */
.certificateUrl:active {
  transform: scale(0.98);
}
