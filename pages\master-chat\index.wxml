<view class="container">
  <!-- 自定义导航栏 -->
  <!-- <custom-nav-bar
    title="{{restaurantConfig.positionTypeName +'餐考师' || '前厅餐考师'}}"
    desc="{{restaurantConfig.name ? '@' + restaurantConfig.name : '@张磊'}}"
    show-back="{{false}}"
  >
    <view slot="rightBtn">
      <image class="master-avatar" src="{{restaurantConfig.avatar}}" mode="aspectFill"></image>
    </view>
  </custom-nav-bar> -->

  <view class="custom-nav">
  <!-- 导航内容区 -->
  <view class="nav-content" style="height: {{navBarHeight}}px;">
     <view class="nav-right">
      <image class="master-avatar" src="{{restaurantConfig.avatar}}" mode="aspectFill"></image>
    </view>
    <view class="nav-center">
      <view class="nav-title">{{restaurantConfig.positionTypeName +'餐考师' || '餐考师'}}</view>
      <view class="nav-subtitle">{{restaurantConfig.name ? '@' + restaurantConfig.name : ''}}</view>
    </view>

  </view>
</view>

  <!-- 聊天内容区域 -->
  <chat-container
    id="chatContainer"
    messages="{{messages}}"
    is-recording="{{isRecording}}"
    total-nav-height="{{navBarHeight}}"
    keyboard-height="{{keyboardHeight}}"
    has-more="{{hasMore}}"
    is-loading="{{isLoading}}"
    page-type="master-chat"
     is-practice-started="{{isPracticeStarted}}"
    is-next-btn-disabled="{{isNextBtnDisabled}}"
    bind:scrollToUpper="onScrollToUpper"
    bind:playMessageAudio="playMessageAudio"
  />

  <!-- 常见问题 -->
  <!--<view class="quick-questions">
    <view
      class="question-chip"
      wx:for="{{quickQuestions}}"
      wx:key="index"
      bindtap="askQuestion"
      data-question="{{item}}"
    >
      {{item}}
    </view>
  </view>-->

  <!-- 底部输入区域 -->
  <input-area
    input-content="{{inputMessage}}"
    is-voice-mode="{{isVoiceMode}}"
    is-recording="{{isRecording}}"
    keyboard-height="{{keyboardHeight}}"
    bind:sendMessage="sendMessage"
     bind:inputFocus="onInputFocus"
    bind:inputBlur="onInputBlur"
    bind:toggleInputType="toggleInputType"
    bind:startRecording="startRecording"
    bind:stopRecording="stopRecording"
    bind:touchMove="onTouchMove"
    bind:cancelRecording="cancelRecording"
    bind:inputChange="onInputChange"

  />

  <!-- 录音提示蒙层 -->
  <recording-mask
    is-recording="{{isRecording}}"
    is-cancelling="{{isCancelling}}"
    recording-time="{{recordingTime}}"
    show-countdown="{{showCountdown}}"
    countdown-number="{{countdownNumber}}"
    bind:cancelCountdown="onCancelCountdown"
    bind:slideChange="onSlideChange"
    bind:recordingEnd="onRecordingEnd"
  />
</view>
