/* components/message-item/message-item.wxss */
/* 消息项 */
.message-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}

/* AI消息 */
.ai-message {
  justify-content: flex-start;
}

/* 用户消息 */
.user-message {
  justify-content: flex-end;
}

.avatar-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  /* margin:0px 20rpx;/ */
  background-color: #a18cd1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-icon text {
  font-size: 45rpx;
  color: #ffffff;
}

.avatar-icon image {
  width: 100%;
  height: 100%;
}
.avatar-icon .aiIcon{
  font-size:28rpx;
  color:#fff;
}

/* 消息内容 */
.message-content {
  max-width: 70%;
  margin: 0 20rpx;
  display: flex;
  flex-direction: column;
  /* flex: 1; */
}

/* 消息气泡 */
.message-bubble {
  padding: 20rpx 24rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.5;
  position: relative;
  word-break: break-all;
  display: flex;
  flex-direction: column;
  text-align: justify;
  margin-bottom: 8rpx;
}

/* 带音频消息的气泡特殊处理 */
.message-bubble .audio-player + text {
  margin-top: 15rpx;
}

/* AI消息气泡 */
.ai-message .message-bubble {
  background-color: #ffffff;
  color: #333333;
  border-top-left-radius: 0;
  border-radius: 5px 15px 15px 15px;
}

/* 用户消息气泡 */
.user-message .message-bubble {
  background-color: #a18cd1;
  color: #ffffff;
  border-top-right-radius: 0;
  border-radius: 15px 5px 15px 15px;
}

/* 消息时间 */
.message-time {
  font-size: 22rpx;
  color: #999999;
  margin-top: 10rpx;
  margin-left: 16rpx;
}

/* 系统消息项 */
.system-message-item {
	text-align: center;
	margin: 20rpx auto;
	padding: 10rpx 30rpx;
	color: #999999;
	font-size: 24rpx;
	background-color: rgba(0, 0, 0, 0.05);
	border-radius: 8rpx;
  }

.question-container{
  width:100%;
  border-top: 1rpx solid #f0f0f0;
  padding-top:10rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.question-container-user{
  justify-content: flex-start;
}

.question-container-user .iconfont{
 color: #ffffff !important;
}

.question-container .iconfont{
  font-size: 26rpx;
  color: #a18cd1;
  margin-right: 10rpx;
}


.practice-btn{
	/* padding: 12rpx 30rpx; */
	color: #a18cd1;
	/* background-color: #a18cd1; */
	/* color: #ffffff; */
	font-size: 24rpx;
	/* border-radius: 30rpx; */
	/* align-self: center; */
	width:fit-content;
	float: right;
	text-align: center;
	/* box-shadow: 0 4rpx 12rpx rgba(161, 140, 209, 0.3); */
	transition: all 0.3s;
  }


.btn-disabled {
  background-color: #cccccc;
  color: #ffffff;
  opacity: 0.8;
}

/* 音频播放器 */
.audio-player {
  display: flex;
  align-items: center;
  width: 70%;
  height: 50rpx;
  position: relative;
  margin-top: 10rpx;
}
.play-text{
  font-size: 20rpx;
  color: #a18cd1;
}
/* 音频播放按钮 */
.audio-play-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15rpx;
  flex-shrink: 0;
  color: #a18cd1;
  /* width: 60rpx; */
  height: 60rpx;
  /* border-radius: 50%; */
}

.user-message .audio-play-btn {
  color: #ffffff;
}

.ai-message .audio-play-btn {
  /* background-color: #a18cd1; */
}

.audio-play-btn text {
  /* color: #ffffff; */
  font-size: 35rpx;
}

/* 音频波形 */
.audio-waveform {
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
  margin-right: 10rpx;
  flex: 0 1 auto; /* 修改为自动收缩 */
}

/* 根据文本长度调整波形样式 */
.audio-waveform.short-text {
  min-width: 80rpx;
  max-width: 120rpx;
}

.audio-waveform.medium-text {
  min-width: 120rpx;
  max-width: 180rpx;
}

.audio-waveform.long-text {
  min-width: 100rpx;
  max-width: 140rpx;
}

/* 根据屏幕自适应 */
@media (max-width: 375px) {
  .audio-waveform.short-text {
    max-width: 100rpx;
  }

  .audio-waveform.medium-text {
    max-width: 150rpx;
  }

  .audio-waveform.long-text {
    max-width: 200rpx;
  }
}

.wave-line {
  width: 4rpx;
  height: 16rpx;
  background-color: #a18cd1;
  border-radius: 2rpx;
  margin: 0 4rpx;
}

.ai-message .wave-line {
  background-color: #a18cd1;
}

.user-message .wave-line {
  background-color: #ffffff;
}

/* 随机高度波形 */
.wave-line:nth-child(1) { height: 10rpx; }
.wave-line:nth-child(2) { height: 18rpx; }
.wave-line:nth-child(3) { height: 28rpx; }
.wave-line:nth-child(4) { height: 15rpx; }
.wave-line:nth-child(5) { height: 20rpx; }

/* 音频时长 */
.audio-duration {
  font-size: 24rpx;
  color: #999999;
  margin-left: 5rpx;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 45rpx;
}

.user-message .audio-duration {
  color: #ffffff;
}

/* 音频播放动画 */
@keyframes waveform {
  0% { height: 10rpx; }
  50% { height: 30rpx; }
  100% { height: 10rpx; }
}

.audio-player.playing .wave-line {
  animation: waveform 1.5s ease-in-out infinite;
}

.audio-player.playing .wave-line:nth-child(1) { animation-delay: 0s; }
.audio-player.playing .wave-line:nth-child(2) { animation-delay: 0.2s; }
.audio-player.playing .wave-line:nth-child(3) { animation-delay: 0.4s; }
.audio-player.playing .wave-line:nth-child(4) { animation-delay: 0.6s; }
.audio-player.playing .wave-line:nth-child(5) { animation-delay: 0.8s; }

.play-btn{
  height:30rpx;
  line-height: 30rpx;
  padding: 10rpx 10rpx;
  background-color: #ccc;
  color: #666;
  font-size: 22rpx;
  border-radius: 20rpx;
}

/* 打字机光标样式 */
.typing-cursor {
  color: #a18cd1;
  font-weight: bold;
  animation: blink 1s infinite;
  margin-left: 2rpx;
}

/* 光标闪烁动画 */
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}