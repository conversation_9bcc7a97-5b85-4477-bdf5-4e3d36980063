// pages/promotion/report.js
import { getExamReport } from '../../api/promotion'
const authBehavior = require('../../behaviors/auth-behavior')

// 页面配置，设置自定义导航栏
const pageConfig = {
  navigationBarTitleText: '考试报告',
  navigationStyle: 'custom',
  // 启用自定义返回处理
  disableScroll: false,
  usingComponents: {
    'custom-nav-bar': '/components/custom-nav-bar/custom-nav-bar'
  }
}

Page({
  behaviors: [authBehavior],

  /**
   * 页面的初始数据
   */
  data: {
    statusConfirmed: false,
    examId: '',
    reportData: null,
    reportScore:null,
    isLoading: false,
    title:'考试报告',
    examInfo:null, // 考试信息
    totalNum:0,
    correctNum:0,
    errorNum:0,
    radarData: {
      categories: [
        '知识掌握度',
        '表达能力 ',
        '客户服务能力',
        '解决问题',
        '菜品知识',
      ],
      series: [{
        name: '当前水平',
        data: [0,0,0,0,0]
      }]
    },
    radarOpts: {
      padding: [15, 15, 0, 15],
      dataLabel: false,
      legend: false,
      extra: {
        radar: {
          gridType: 'radar',
          gridColor: '#CCCCCC',
          gridCount: 5,
          max: 100,
          labelColor: '#666666',
          radarStyle: 'radar'
        }
      }
    },
    isBackTriggered: false, // 标记是否已经触发了返回操作
    isEarlySubmission:false,//是否提前交卷
    from:'',
    passCore:0,
    accuracy:0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      // examInfo:wx.getStorageSync('examInfo'),
      examId: options.examId||this.data.examInfo.examId,
      isEarlySubmission:options.isEarlySubmission=='true'?true:false,
      title:'考试报告',
      from:options.from
    });
    this.loadReportData();

    console.log(this.data.from)
    // 启用页面返回提示，防止用户直接返回上一页
    if (this.data.from !== 'examRecords' && wx.enableAlertBeforeUnload) {
      wx.enableAlertBeforeUnload({
        message: '返回将离开考试报告页面',
        success: (res) => {
          console.log('启用返回提示成功', res);
        },
        fail: (err) => {
          console.error('启用返回提示失败', err);
        }
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 延迟一点时间绘制雷达图，确保DOM已渲染
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 监听窗口尺寸变化
    wx.onWindowResize(this.handleResize);
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 取消监听窗口尺寸变化
    wx.offWindowResize(this.handleResize);
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 如果不是通过我们的返回按钮触发的，则视为物理返回键触发
    if (!this.data.isBackTriggered) {
      console.log('检测到物理返回键退出，重定向到考试列表');
      // 标记为已触发返回
      this.setData({
        isBackTriggered: true
      });
      
      // 通过全局数据或存储，告诉下一个页面需要跳转
      wx.setStorageSync('needRedirectToPromotion', true);
      
      // 使用延迟，确保数据已保存
      if(this.data.from !== 'examRecords'){
        setTimeout(() => {
          // 通过reLaunch方式跳转到考试列表页
          wx.reLaunch({
            url: '/pages/promotion/list'
          });
        }, 50);
      }
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
// 绘制雷达图
drawRadarChart: function() {
  // 使用选择器获取容器实际尺寸
  const query = wx.createSelectorQuery();
  query.select('#radar-container').boundingClientRect((rect) => {
    if (!rect) {
      console.error('无法获取雷达图容器尺寸');
      return;
    }
    
    // 获取实际容器尺寸
    const containerWidth = rect.width || 300; // 默认宽度300
    const containerHeight = rect.height || 300; // 默认高度300
    
    // 计算雷达图中心点坐标
    const centerX = containerWidth / 2;
    const centerY = containerHeight / 2;
    
    console.log('雷达图容器尺寸:', containerWidth, containerHeight);
    console.log('雷达图中心坐标:', centerX, centerY);
    
    const ctx = wx.createCanvasContext('radarCanvas');
    const data = this.data.radarData;
    const categories = data.categories;
    const series = data.series;
    
    const radius = Math.min(containerWidth, containerHeight) * 0.35; // 自适应半径
    const angleStep = 2 * Math.PI / categories.length; // 每个维度之间的角度
    
    // 绘制雷达图网格
    ctx.setLineWidth(1);
    ctx.setStrokeStyle('#e4e4e4');
    
    // 绘制环形网格
    for (let r = 0.2; r <= 1; r += 0.2) {
      ctx.beginPath();
      for (let i = 0; i < categories.length; i++) {
        const angle = i * angleStep - Math.PI / 2; // 从上方开始
        const x = centerX + radius * r * Math.cos(angle);
        const y = centerY + radius * r * Math.sin(angle);
        
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      ctx.closePath();
      ctx.stroke();
    }
    
    // 绘制连接中心点的线
    for (let i = 0; i < categories.length; i++) {
      const angle = i * angleStep - Math.PI / 2; // 从上方开始
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(x, y);
      ctx.stroke();
      
      // 绘制维度标签
      const labelDistance = radius * 1.20; // 标签距离中心点稍远一些
      const labelX = centerX + labelDistance * Math.cos(angle);
      const labelY = centerY + labelDistance * Math.sin(angle);
      
      ctx.setFontSize(10);
      ctx.setFillStyle('#000000'); // 设置文字颜色为黑色
      ctx.setTextAlign('center');
      ctx.setTextBaseline('middle');
      ctx.fillText(categories[i], labelX, labelY);
    }
    
    // 绘制数据区域
    const colors = ['#a18cd1', '#8fc1ff']; // 当前水平和上次评估的颜色
    const colorsBg = ['rgba(161, 140, 209, 0.3)', 'rgba(56, 163, 209, 0.3)']; // 当前水平和上次评估的颜色
    
    // for (let s = 0; s < series.length; s++) {
      const seriesData = series[0].data;
      
      // 绘制数据连线
      ctx.beginPath();
      for (let i = 0; i < categories.length; i++) {
        const value = seriesData[i] / 100; // 归一化值
        const angle = i * angleStep - Math.PI / 2; // 从上方开始
        const x = centerX + radius * value * Math.cos(angle);
        const y = centerY + radius * value * Math.sin(angle);
        
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      ctx.closePath();
      
      // 填充区域
      ctx.setFillStyle(colorsBg[0]); // 添加透明度
      ctx.fill();
      
      // 描边
      ctx.setLineWidth(1);
      ctx.setStrokeStyle(colors[0]);
      ctx.stroke();
      
      // 绘制数据点
      for (let i = 0; i < categories.length; i++) {
        const value = seriesData[i] / 100; // 归一化值
        const angle = i * angleStep - Math.PI / 2; // 从上方开始
        const x = centerX + radius * value * Math.cos(angle);
        const y = centerY + radius * value * Math.sin(angle);
        
        ctx.beginPath();
        ctx.arc(x, y, 2, 0, 2 * Math.PI);
        // ctx.setFillStyle('#fe3');
        // ctx.fill();
        ctx.setLineWidth(1);
        ctx.setStrokeStyle(colors[0]);
        ctx.stroke();
      }
    // }
    
    ctx.draw();
  }).exec();
},
  async loadReportData() {
    this.setData({
      isLoading: true
    });
    
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });
      
      const result = await getExamReport({
        exam_id:this.data.examId,
        isEarlySubmission:this.data.isEarlySubmission
      });
      let correctNum = 0;
      let errorNum = 0;
      result.exam.forEach(item=>{
        if(item.result){
          correctNum++
        }else{
          errorNum++
        }
      })
      this.setData({
        reportData: {...result,score:parseFloat(result.score)},
        reportScore:result.reportData,
        isLoading: false,
        passScore:result.pass_score,
        correctNum,
        errorNum,
        totalNum:result.questionCount,
        accuracy:parseFloat((correctNum/result.questionCount*100).toFixed(0)) 
      });
      console.log(this.data.correctNum,this.data.errorNum)
      const radarData = this.data.radarData;
      radarData.series[0].data =[
        this.data.reportScore.knowledge_mastery,
        this.data.reportScore.expression_ability,
        this.data.reportScore.customer_service_ability,
        0,0
      ]
      this.setData({
        radarData: radarData
      });

    } catch (error) {
      console.error('获取考试报告失败：', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
      
      this.setData({
        isLoading: false
      });
    } finally {
      this.drawRadarChart();
      wx.hideLoading();
    }
  },

  createConfetti() {
    // 这里可以添加粒子动画效果
    // 由于小程序限制，可以使用CSS动画替代
  },

  /**
   * 自定义返回按钮处理
   */
  goBack() {
      // 标记为已触发返回
      this.setData({
        isBackTriggered: true
      });
      
      // 设置tab索引
      wx.setStorageSync('selectedTab', 2);
      
      if(this.data.from == 'examRecords'){
        setTimeout(() => {
          wx.navigateBack();
        }, 100);
      }else{
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/promotion/list'
          });
        }, 100);
      }
  },
  // 考试记录
  goToRecord(){
    wx.navigateTo({
      url: '/pages/promotion/paper/paper?examId='+this.data.examId,
    })
 
  },
  /**
   * 处理窗口尺寸变化
   */
  handleResize() {
    // 重新绘制雷达图
    this.drawRadarChart();
  },
})