Component({
  properties: {
    isRecording: {
      type: Boolean,
      value: false
    },
    isCancelling: {
      type: Boolean,
      value: false
    },
    recordingTime: {
      type: String,
      value: '00:00'
    },
    showCountdown: {
      type: Boolean,
      value: false
    },
    countdownNumber: {
      type: Number,
      value: 3
    }
  },
  data: {
    countdownTimer: null,
    startY: 0, // 记录开始触摸的Y坐标
    currentY: 0, // 记录当前触摸的Y坐标
    isMovingUp: false // 是否正在上滑
  },
  options: {
    styleIsolation: 'apply-shared'
  },
  methods: {
    // 统一的触摸开始事件
    onTouchStart(e) {
      console.log('遮罩触摸开始', e);
      const touch = e.touches[0];
      this.setData({
        startY: touch.clientY,
        currentY: touch.clientY,
        isMovingUp: false
      });
    },

    // 统一的触摸移动事件
    onTouchMove(e) {
      console.log('遮罩触摸移动', e);

      // 如果是倒计时期间，只阻止滚动，不处理上滑逻辑
      if (this.properties.showCountdown) {
        console.log('倒计时期间触摸移动');
        return false;
      }

      // 录音期间处理上滑逻辑
      const touch = e.touches[0];
      const currentY = touch.clientY;
      const startY = this.data.startY;

      // 计算移动距离，向上为负值
      const deltaY = currentY - startY;

      // 判断是否上滑超过阈值（比如50px）
      const isMovingUp = deltaY < -50;

      console.log('触摸移动数据:', {
        startY: startY,
        currentY: currentY,
        deltaY: deltaY,
        isMovingUp: isMovingUp,
        showCountdown: this.properties.showCountdown
      });

      // 更新当前位置和上滑状态
      this.setData({
        currentY: currentY,
        isMovingUp: isMovingUp
      });

      // 通知父页面上滑状态变化
      this.triggerEvent('slideChange', {
        isMovingUp: isMovingUp,
        deltaY: deltaY
      });

      return false;
    },

    // 统一的触摸结束事件
    onTouchEnd(e) {
      console.log('遮罩触摸结束', e);
      console.log('当前状态:', {
        showCountdown: this.properties.showCountdown,
        isRecording: this.properties.isRecording,
        isMovingUp: this.data.isMovingUp
      });

      // 如果是倒计时期间，触发取消倒计时
      if (this.properties.showCountdown) {
        console.log('倒计时期间松开手指，取消倒计时');
        this.triggerEvent('cancelCountdown', {
          source: 'recording-mask',
          action: 'touchend'
        });
      } else {
        // 录音期间，根据上滑状态决定操作
        console.log('录音期间松开手指');
        const isMovingUp = this.data.isMovingUp;

        this.triggerEvent('recordingEnd', {
          isCancel: isMovingUp,
          source: 'recording-mask'
        });
      }

      // 重置状态
      this.setData({
        startY: 0,
        currentY: 0,
        isMovingUp: false
      });

      return false;
    }
  }
})