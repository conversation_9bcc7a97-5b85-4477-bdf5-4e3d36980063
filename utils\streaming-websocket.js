// utils/streaming-websocket.js
// WebSocket 流式数据处理工具类

class StreamingWebSocket {
  constructor(options = {}) {
    this.socket = null;
    this.isConnected = false;
    this.messageQueue = [];
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 3;
    this.reconnectDelay = options.reconnectDelay || 1000;
    this.heartbeatTimer = null;
    this.heartbeatInterval = options.heartbeatInterval || 30000; // 30秒心跳
    this.connectTimeout = options.connectTimeout || 10000; // 连接超时
    this.isManualClose = false; // 标记是否为主动关闭
  }

  /**
   * 连接 WebSocket
   * @param {string} url - WebSocket 服务器地址
   * @param {Object} options - 连接选项
   */
  connect(url, options = {}) {
    return new Promise((resolve, reject) => {
      try {
        // 创建 WebSocket 连接
        this.socket = wx.connectSocket({
          url: url,
          protocols: options.protocols || [],
          header: options.header || {}
        });

        // 连接打开事件
        this.socket.onOpen(() => {
          console.log('WebSocket 连接已建立');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.startHeartbeat();

          // 发送队列中的消息
          this.flushMessageQueue();

          resolve();
        });

        // 接收消息事件
        this.socket.onMessage((event) => {
          // console.log('WebSocket 原始消息事件:', event);
          this.handleMessage(event.data);
        });

        // 连接关闭事件
        this.socket.onClose((event) => {
          console.log('WebSocket 连接已关闭', event);

          this.isConnected = false;
          this.stopHeartbeat();

          // 如果不是主动关闭且未达到最大重试次数，尝试重连
          if (!this.isManualClose && this.reconnectAttempts < this.maxReconnectAttempts) {
            console.log('🔄 检测到非主动关闭，准备重连');
            this.reconnect(url, options);
          } else if (this.isManualClose) {
            console.log('✅ 主动关闭连接，不进行重连');
          } else {
            console.log('❌ 已达到最大重试次数，停止重连');
          }
        });

        // 连接错误事件
        this.socket.onError((error) => {
          console.error('WebSocket 连接错误:', error);
          this.isConnected = false;

          // 触发错误事件，让页面处理用户提示
          if (this.onError) {
            this.onError(error);
          }

          reject(error);
        });

      } catch (error) {
        console.error('创建 WebSocket 连接失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 重连 WebSocket
   */
  reconnect(url, options) {
    this.reconnectAttempts++;
    console.log(`尝试重连 WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      this.connect(url, options).catch(() => {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          console.error('WebSocket 重连失败，已达到最大重试次数');
        }
      });
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  /**
   * 发送消息
   * @param {Object} data - 要发送的数据
   */
  send(data) {
    const message = JSON.stringify(data);

    if (this.isConnected && this.socket) {
      this.socket.send({
        data: message
      });
    } else {
      // 连接未建立时，将消息加入队列
      this.messageQueue.push(message);
    }
  }

  /**
   * 发送队列中的消息
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const message = this.messageQueue.shift();
      this.socket.send({
        data: message
      });
    }
  }

  /**
   * 处理接收到的消息
   * @param {string} data - 接收到的数据
   */
  handleMessage(data) {
    // console.log('🔄 WebSocket 收到原始消息:', data);

    try {
      const message = JSON.parse(data);
      // console.log('📨 WebSocket 解析后的消息:', message,message.event);

      // 根据消息类型处理
      switch (message.event) {
        case 'stream_start':
          // console.log('🚀 触发 streamStart 事件');
          this.onStreamStart && this.onStreamStart(message);
          break;
        case 'stream_content':
          // console.log('🚀 触发 stream_content 事件');
          this.onStreamStart && this.onStreamStart(message);
          break;
        case 'stream_chunk':
          // console.log('📦 触发 streamChunk 事件，内容:', message.content);
          this.onStreamChunk && this.onStreamChunk(message);
          break;
        case 'analysis_complete':
          // 输出结束
          this.onStreamEnd && this.onStreamEnd(message);
        case 'stream_end':
          console.log('🏁 触发 streamEnd 事件');
          // this.onStreamEnd && this.onStreamEnd(message);
          break;
        case 'error':
          // console.log('❌ 触发 error 事件');
          // this.onError && this.onError(message);
          this.onError && this.onError(message);
          break;
        case 'pong':
          console.log('💓 收到心跳响应');
          // 心跳响应，不需要处理
          break;
        default:
          console.log('📝 触发 message 事件，类型:', message.type);
          // this.onMessage && this.onMessage(message);
      }
    } catch (error) {
      console.error('❌ 解析 WebSocket 消息失败:', error, '原始数据:', data);
    }
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' });
      }
    }, this.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 关闭连接
   * @param {boolean} isManualClose - 是否为主动关闭
   */
  close(isManualClose = true) {
    this.isConnected = false;
    this.isManualClose = isManualClose; // 标记是否为主动关闭
    this.stopHeartbeat();

    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  /**
   * 设置事件监听器
   */
  on(event, callback) {
    switch (event) {
      case 'streamStart':
        this.onStreamStart = callback;
        break;
      case 'streamChunk':
        this.onStreamChunk = callback;
        break;
      case 'streamEnd':
        this.onStreamEnd = callback;
        break;
      case 'errorEnd':
          this.onErrorEnd = callback;
          break;
      case 'error':
        this.onError = callback;
        break;
      case 'message':
        this.onMessage = callback;
        break;
    }
  }
}

/**
 * 流式打字机效果处理器
 */
class TypewriterEffect {
  constructor(options = {}) {
    this.speed = options.speed || 50; // 打字速度（毫秒）
    this.onUpdate = options.onUpdate || (() => {});
    this.onComplete = options.onComplete || (() => {});
    this.onError= options.onError || (() => {});
    this.isRunning = false;
    this.currentText = '';
    this.targetText = '';
    this.timer = null;
  }

  /**
   * 重置打字机效果
   */
  reset() {
    this.stop();
    this.currentText = '';
    this.targetText = '';
    console.log('🔄 打字机效果已重置');
  }

  /**
   * 开始打字机效果
   * @param {string} text - 要显示的文本
   */
  start(text = '') {
    this.reset(); // 开始前先重置
    this.targetText = text;
    this.currentText = '';
    this.isRunning = true;
    this.typeNextChar();
  }

  /**
   * 追加文本
   * @param {string} text - 要追加的文本
   */
  append(text) {
    // console.log('✍️ TypewriterEffect append:', text);
    // console.log('📝 当前目标文本长度:', this.targetText.length, '是否运行中:', this.isRunning);

    this.targetText += text;
    // console.log('📝 追加后目标文本长度:', this.targetText.length);

    // 如果当前没有在运行，开始打字
    if (!this.isRunning) {
      // console.log('🎬 开始打字机效果');
      this.isRunning = true;
      this.typeNextChar();
    }
  }

  /**
   * 打字下一个字符
   */
  typeNextChar() {
    if (!this.isRunning) return;

    if (this.currentText.length < this.targetText.length) {
      this.currentText = this.targetText.substring(0, this.currentText.length + 1);
      this.onUpdate(this.currentText);

      this.timer = setTimeout(() => {
        this.typeNextChar();
      }, this.speed);
    } else {
      this.isRunning = false;
      this.onComplete(this.currentText);
    }
  }

  /**
   * 停止打字机效果
   */
  stop() {
    this.isRunning = false;
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  /**
   * 立即完成打字
   */
  complete() {
    this.stop();
    this.currentText = this.targetText;
    this.onUpdate(this.currentText);
    this.onComplete(this.currentText);
    this.onError(this.currentText);
  }
}

module.exports = { StreamingWebSocket, TypewriterEffect };
