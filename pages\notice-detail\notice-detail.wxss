/* pages/notice-detail/notice-detail.wxss */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #fff;
  position: relative;
}


/* 公告详情容器 */
.notice-detail-container {
}

.notice-detail-content {
  margin: 20rpx 32rpx;
  overflow: hidden;
}

/* 公告头部 */
.notice-header {
  border-bottom: 1rpx solid #f0f0f0;
}

.notice-title-wrapper {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.notice-badge {
  display: inline-block;
  background-color: #ff6b6b;
  color: white;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.notice-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

.notice-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notice-time {
  font-size: 26rpx;
  color: #999;
}

/* 公告内容 */
.notice-content {
  font-size: 28rpx;
  line-height: 50rpx;
  color: #333;
  /* white-space: pre-wrap;
  word-break: break-all; */
  margin:20rpx 0;
}


/* 公告底部 */
.notice-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 附件样式 */
.attachments {
  margin-bottom: 30rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-item .iconfont {
  font-size: 32rpx;
  color: #a18cd1;
  margin-right: 16rpx;
}

.attachment-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 链接样式 */
.links {
  margin-bottom: 0;
}

.link-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.link-item:last-child {
  border-bottom: none;
}

.link-item .iconfont {
  font-size: 32rpx;
  color: #a18cd1;
  margin-right: 16rpx;
}

.link-name {
  font-size: 28rpx;
  color: #a18cd1;
  flex: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .notice-title {
    font-size: 32rpx;
  }

  .notice-text {
    font-size: 28rpx;
  }

  .notice-detail-content {
    margin: 16rpx;
  }
}