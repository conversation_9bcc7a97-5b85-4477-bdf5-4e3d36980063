/* 空数据提示组件样式 */
.empty-tip-container {
  /* width: 100%; */
  height: 100%;
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.empty-tip {
  /* display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; */
  width: 80%;
  margin:0px auto;
  text-align:center;
}
.empty-image{
  display: block;
  width: inherit;
  margin:0px auto;
}
.empty-tip .iconfont {
  font-size: 260rpx;
  color: #c0c0c0;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
} 