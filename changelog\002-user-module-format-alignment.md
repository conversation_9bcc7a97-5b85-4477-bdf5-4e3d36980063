# 用户模块格式对齐

## 变更摘要
- 修改了用户模块(user.js)的API接口定义格式，使其与其他模块保持一致
- 将API路径格式从`'METHOD:PATH'`改为`'METHOD PATH'`（由冒号分隔改为空格分隔）
- 封装所有API处理函数到userMock对象中
- 修改导出格式为CommonJS格式，与其他模块保持一致
- 统一了返回数据的格式，确保所有API都返回`{ code, message, data }`结构

## 用户模块API对齐
原始API路径 | 新API路径
--- | ---
`GET:/api/user/info` | `GET /api/user/info`
`GET:/api/user/certificates` | `GET /api/user/certificates`
`GET:/api/user/badges` | `GET /api/user/badges`
`GET:/api/user/skill-radar` | `GET /api/user/skill-radar`
`GET:/api/user/badge-stats` | `GET /api/user/badge-stats`
`GET:/api/user/badge/detail` | `GET /api/user/badge/detail`
`POST:/api/user/update` | `POST /api/user/update`
`POST:/api/user/authorize` | `POST /api/user/authorize`

## 导出格式变更
- 将直接导出改为包装在userMock对象中并使用CommonJS格式导出：
  ```javascript
  // 旧格式
  export default {
    // API处理函数...
  };
  
  // 新格式
  const userMock = {
    // API处理函数...
  };
  
  module.exports = {
    userMock
  };
  ```

## 返回格式统一
- 确保所有API处理函数都返回统一的数据结构：
  ```javascript
  {
    code: 0, // 状态码，0表示成功
    message: 'success', // 状态消息
    data: { ... } // 实际数据
  }
  ```

## 后续工作
- 确保mock/index.js中正确导入userMock
- 验证所有用户相关API能正常工作 