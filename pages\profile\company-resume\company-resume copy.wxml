<!--pages/profile/company-resume.wxml-->
<view class="container">
  
  <!-- 主要内容区域 -->
  <view class="main-content" >
    <!-- 头部卡片 -->
    <view class="resume-header">
      <image class="company-logo" src="{{companyLogo || '/static/images/default-company.png'}}"></image>
      <view class="header-info">
        <view class="company-name">{{companyName || '未设置企业名称'}}</view>
        <view class="join-date">入职时间：{{joinDate || '未设置'}}</view>
      </view>
    </view>
    
    <!-- 个人信息区域 -->
    <view class="info-section">
      <view class="section-title">
        <text class="title-icon iconfont icon-user"></text>
        <text>个人信息</text>
      </view>
      
      <view class="info-list">
        <view class="info-item">
          <text class="item-label">姓名</text>
          <text class="item-value">{{userInfo.realName || '未设置'}}</text>
        </view>
        
        <view class="info-item">
          <text class="item-label">手机号</text>
          <text class="item-value">{{userInfo.phone || '未设置'}}</text>
        </view>
        
        <view class="info-item">
          <text class="item-label">身份证号</text>
          <text class="item-value">{{userInfo.idNumber ? (userInfo.idNumber.substring(0, 6) + '********' + userInfo.idNumber.substring(14)) : '未设置'}}</text>
        </view>
        
        <view class="info-item">
          <text class="item-label">岗位</text>
          <text class="item-value">{{position || '未设置'}}</text>
        </view>
      </view>
    </view>
    
    <!-- 工作经历区域 -->
    <view class="info-section">
      <view class="section-title">
        <text class="title-icon iconfont icon-work"></text>
        <text>工作经历</text>
      </view>
      
      <view class="timeline">
        <block wx:if="{{workExperience && workExperience.length > 0}}">
          <view class="timeline-item" wx:for="{{workExperience}}" wx:key="index">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <view class="timeline-header">
                <text class="timeline-title">{{item.title}}</text>
                <text class="timeline-date">{{item.date}}</text>
              </view>
              <view class="timeline-desc">{{item.description}}</view>
            </view>
          </view>
        </block>
        <view class="empty-timeline" wx:else>
          <text>暂无工作经历记录</text>
        </view>
      </view>
    </view>
    
    <!-- 电子简历区域 -->
    <view class="info-section">
      <view class="section-title">
        <text class="title-icon iconfont icon-file"></text>
        <text>电子简历</text>
      </view>
      
      <view class="resume-file" bindtap="viewResume" wx:if="{{resumeUrl}}">
        <view class="file-icon">
          <text class="iconfont icon-pdf"></text>
        </view>
        <view class="file-info">
          <view class="file-name">我的简历.pdf</view>
          <view class="file-size">{{resumeSize || '未知大小'}}</view>
        </view>
        <view class="file-action">
          <text class="iconfont icon-preview"></text>
        </view>
      </view>
      
    </view>
  </view>
</view> 