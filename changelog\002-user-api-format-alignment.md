# 用户模块API格式对齐

## 变更摘要
- 修改了用户模块(user.js)的API接口格式，使其与其他模块保持一致
- 将模块导出从默认导出改为命名导出
- 修改了mock/index.js中对用户模块的导入方式

## 具体变更
1. 修改API路径格式：
   - 从 `'GET:/api/user/info'` 改为 `'GET /api/user/info'`（将冒号改为空格）
   - 从 `'POST:/api/user/update'` 改为 `'POST /api/user/update'`

2. 修改返回值格式：
   - 从直接返回数据 `return userInfo` 改为返回标准响应对象 `return { code: 0, message: 'success', data: userInfo }`

3. 修改导出方式：
   - 从 `export default {...}` 改为 `const userMock = {...}; module.exports = { userMock }`

4. 修改导入方式：
   - 从 `import userMock from './modules/user'` 改为 `import { userMock } from './modules/user'`

## 变更原因
确保所有模块的API格式一致，避免由于格式不一致导致的调用错误。这些变更使用户模块与之前修改的练习模块和晋升考试模块保持一致的格式。

## 后续工作
1. 确保所有其他模块也采用相同的API格式和导出方式
2. 检查前端组件中使用这些API的地方，确保调用方式正确 