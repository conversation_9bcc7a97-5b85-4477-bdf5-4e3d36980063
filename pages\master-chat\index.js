// pages/master-chat/index.js
// 从uniapp的pages/master-chat/index.vue转换而来

// 导入API
const { getInitialMessages, getQuickQuestions, getRestaurantConfig } = require('../../api/master-chat');
const { getFormattedTime } = require('../../utils/dateFormat');
import { sendQuestionToCankaoshi } from '../../api/home';

// 导入流式输出相关工具
const { StreamingWebSocket, TypewriterEffect } = require('../../utils/streaming-websocket');
const { getPracticeStreamUrl, getConnectionConfig } = require('../../config/websocket');

// 引入微信同声传译插件
const plugin = requirePlugin("WechatSI");
// 获取全局唯一的语音合成管理器
const manager = plugin.getRecordRecognitionManager();
const authBehavior = require('../../behaviors/auth-behavior');
import request from '../../utils/request';
Page({
  behaviors: [authBehavior],

  // 页面的初始数据
  data: {
    messages: [],
    inputMessage: '',
    isRecording: false,
    isCancelling: false,
    wasCancelled: false,
    recordingTime: '00:00',
    recordingSeconds: 0,
    scrollTop: 0,
    quickQuestions: [],
    recordingTimer: null,
    recordTimer: null,
    isLoading: false,
    keyboardHeight: 0,
    startY: 0,
    hasRecordAuth: false,
    innerAudioContext: null,
    isPlaying: false,
    timeUpdateChecked: false,
    isRecovering: false,
    timeUpdateCount: 0,
    isVoiceMode: false,
    windowWidth: 0,
    restaurantConfig: null, // 添加餐厅配置数据
    recordId: null, // 添加recordId字段，默认为null
    isSendDisabled:false, // 是否禁用发送按钮
    showCountdown: false, // 是否显示倒计时
    countdownNumber: 3, // 倒计时数字
    recordingCountdownTimer: null, // 录音倒计时定时器

    // WebSocket 流式处理相关
    streamingSocket: null, // WebSocket 连接实例
    typewriterEffect: null, // 打字机效果实例
    isStreaming: false, // 是否正在流式输出
    streamingMessageIndex: -1 // 当前流式输出的消息索引

  },

  // 生命周期函数--监听页面加载
  onLoad: function() {
    this.loadInitialData();

    // 检查并设置音频播放权限
    this.checkAudioPermission();

    // 初始化录音管理器
    this.initRecorderManager();

    // 初始化音频播放器
    this.initAudioContext();

    // 页面加载时就请求录音权限
    this.requestRecordPermission();


     // 添加欢迎消息
    //  const userMessage = {
    //   type: 'user',
    //   content: '你好餐考师，请问',
    //   time: getFormattedTime(),
    //   isPlaying: false,
    //   audioDuration: 0,
    //   audioSrc: '',
    //   isAudioOnly: false,
    //   showStartButton: true // 显示开始按钮
    // };
    // this.setData({
    //   messages: [userMessage]
    // });


    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      windowWidth: systemInfo.windowWidth
    });

    // 初始化 WebSocket 连接和打字机效果
    this.initStreamingConnection();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('📱 页面显示，检查 WebSocket 连接状态');

    // 只在没有 WebSocket 实例时才初始化，避免频繁重连
    if (!this.data.streamingSocket) {
      console.log('🔄 初始化 WebSocket 连接');
      this.initStreamingConnection();
    } else if (!this.data.streamingSocket.isConnected) {
      console.log('📡 WebSocket 实例存在但未连接，等待使用时再连接');
    } else {
      console.log('✅ WebSocket 连接正常');
    }
  },

  /**
   * 页面隐藏时的处理
   */
  onHide() {
    console.log('📱 页面隐藏，保持 WebSocket 连接');
    // 注意：这里不清理连接，保持连接状态
  },

  // 页面卸载时清理资源
  onUnload: function() {
    // 清除计时器
    if (this.data.recordingTimer) {
      clearInterval(this.data.recordingTimer);
    }
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    // 清除录音倒计时定时器
    this.clearRecordingCountdownTimer();

    // 释放音频资源
    if (this.innerAudioContext) {
      try {
        this.innerAudioContext.stop();
        // 移除所有事件监听
        this.innerAudioContext.offEnded();
        this.innerAudioContext.offError();
        this.innerAudioContext.offTimeUpdate();
        this.innerAudioContext.offCanplay();
        this.innerAudioContext.offPlay();
        this.innerAudioContext.destroy();
      } catch (error) {
        console.error('销毁音频上下文失败:', error);
      }
      this.innerAudioContext = null;
    }

    // 清理流式连接
    this.cleanupStreamingConnection();
  },

  // 加载初始数据
  loadInitialData: function() {
    this.setData({
      isLoading: true
    });

    // 并行请求数据
    Promise.all([
      this.loadRestaurantConfig(),
      // this.loadInitialMessages(),
      // this.loadQuickQuestions()
    ])
    .catch(error => {
      console.error('加载数据失败：', error);
    })
    .finally(() => {
      this.setData({
        isLoading: false
      });
    });
  },

  // 加载餐厅配置信息
  loadRestaurantConfig: function() {
    getRestaurantConfig()
      .then(result => {
        if (result && result.config) {
          console.log('获取餐厅配置成功:', result.config);
          this.setData({
            restaurantConfig: result.config
          })
          //头像
          if(result.config.avatar){
            this.setData({
              ["restaurantConfig.avatar"]: request.config.imgURL + result.config.avatar
            })
          }
          //初始化打招呼信息
          this.loadInitialMessage(result)
        }
      })
      .catch(error => {
        console.error('获取餐厅配置失败：', error);
      });
  },
   //初始化打招呼信息
  loadInitialMessage(result){
    const aiMessage = {
      type: 'ai',
      content: result.config.initMessage || '你好',
      time: getFormattedTime(),
      isPlaying: false,
      audioDuration: 0,
      audioSrc: '',
      avatar:this.data.restaurantConfig.avatar,
      isAudioOnly: false,
      showStartButton: true // 显示开始按钮
    };
    this.setData({
      messages: [aiMessage]
    });
    this.scrollToBottom();
  },


  // 加载快捷提问
  loadQuickQuestions: function() {
    return getQuickQuestions()
      .then(result => {
        if (result && Array.isArray(result)) {
          this.setData({
            quickQuestions: result
          });
        }
      })
      .catch(error => {
        console.error('获取快捷提问失败：', error);
      });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 格式化消息文本（添加样式）
  formatMessage: function(text) {
    return text.replace(/@张磊/g, '<span class="master-name">@张磊</span>');
  },

  // 添加用户消息
  addUserMessage: function(text) {
    const now = new Date();
    const time = getFormattedTime();

    const messages = this.data.messages.concat([{
      type: 'user',
      content: text,
      time: time,
      isAudio: false,
      audioSrc: ''
    }]);

    this.setData({
      messages: messages
    });

    this.scrollToBottom();
  },

  // 滚动到底部
  scrollToBottom: function() {
    setTimeout(() => {
      const chatContainer = this.selectComponent('#chatContainer');
      if (chatContainer && typeof chatContainer.scrollToBottom === 'function') {
        chatContainer.scrollToBottom();
      }
    }, 100);
  },

  // 发送消息
  sendMessage: function(e) {
    const content = e.detail.content || this.data.inputMessage
    if (!this.data.inputMessage.trim()) {
      wx.showToast({
        title: '请输入回答内容',
        icon: 'none'
      });
      return
    }
    if(this.data.isSendDisabled){
      wx.showToast({
        title: '思考中请稍后...',
        icon: 'none'
      });
      return
    }

    // 停止当前语音播放
    this.stopSpeech();

    // 添加用户消息
    this.addUserMessage(content);

    // 清空输入框
    setTimeout(() => {
      this.setData({
        inputMessage: ''
      });
    }, 50);

    this.setData({
      isSendDisabled:true,
    })

    // 优先尝试使用流式输出
    if (this.tryStreamingOutput(content)) {
      console.log('流式输出已启动');
      return;
    }

    // 回退到传统HTTP请求
    // this.sendTraditionalMessage(content);
  },

  // 使用快捷问题提问
  askQuestion: function(e) {
    const question = e.currentTarget.dataset.question;
    this.addUserMessage(question);

    // 发送快捷问题并获取回复
    sendQuestionToCankaoshi(question)
      .then(response => {
        if (response) {
          // 添加餐考师回复
          const messages = this.data.messages.concat([response]);
          this.setData({
            messages: messages
          });
          this.scrollToBottom();
        }
      })
      .catch(error => {
        console.error('发送快捷问题失败：', error);
      });
  },

  // 初始化录音管理器
  initRecorderManager: function() {
    const that = this;

    manager.onError((res) => {
      console.log('识别错误：', res);
    });

    manager.onStart = function(res) {
      console.log('识别开始：', res);
    };

    manager.onStop = function(res) {
      console.log('识别结束onstop：', res);

      // 检查是否是被取消的录音
      if (that.data.wasCancelled) {
        console.log('录音已被用户取消，不处理录音结果');
        // 重置取消标志
        that.setData({
          wasCancelled: false,
          isLoading: false,
        });
        return;
      }

      // 语音识别结束，获取文本内容和临时录音文件
      const text = res.result || '';
      const tempFilePath = res.tempFilePath || '';

      // 防止二次判断，此处保留原有逻辑以兼容之前的实现
      if (that.data.isCancelling) {
        // 如果是取消录音，重置状态
        that.setData({
          isRecording: false,
          isCancelling: false,
          isLoading: false,
        });
        return; // 如果是取消录音，不进行处理
      }

      if (tempFilePath) {
        if (!text) {
          wx.showToast({
            title: '语音录入未识别，请重新回答问题',
            icon: 'none'
          });
          return;
        }

        // 构造用户消息
        const userMessage = {
          type: 'user',
          content: text, // 显示识别的文本内容
          time: getFormattedTime(),
          isAudio: true, // 标记为音频消息
          audioSrc: tempFilePath, // 保存录音文件路径
          audioDuration: that.data.recordingTime || '00:00' // 保存录音时长
        };

        // 添加消息到列表并更新输入框
        const newMessages = [...that.data.messages, userMessage];
        that.setData({
          messages: newMessages,
          // inputMessage: text,
          isLoading: true
        });

        that.scrollToBottom();
        that.setData({
          isSendDisabled:true,
        })

        // 优先尝试使用流式输出
        if (that.tryStreamingOutput(text)) {
          console.log('录音流式输出已启动');
          return;
        }

        // 回退到传统HTTP请求
        that.sendTraditionalMessage(text);
          // .catch(error => {
          //   console.error('发送消息失败：', error);
          //   wx.showToast({
          //     title: '发送失败，请重试?',
          //     icon: 'none'
          //   });
          //   that.setData({ isLoading: false });
          // });
      } else {
        // 如果没有录音文件但有识别文本，设置为输入框内容
        if (text && text.trim()) {
          that.setData({
            // inputMessage: text
          });
        }
      }

      that.setData({
        isRecording: false,
        isCancelling: false
      });
    };
  },

  // 开始录音
  startRecording: function(e) {
    if(this.data.isSendDisabled){
      wx.showToast({
        title: '思考中请稍后...',
        icon: 'none'
      });
      return
    }
    // 获取触摸的起始Y坐标
    const startY = e.detail.touches[0].clientY;
    this.setData({
      startY: startY,
      isCancelling: false,
      wasCancelled: false // 重置取消标志
    });

    // 检查是否已有录音权限
    if (this.data.hasRecordAuth) {
      // 已有权限，开始倒计时
      this.startRecordingCountdown();
    } else {
      // 没有权限，提示用户并重置录音状态
      this.setData({
        isRecording: false
      });

      wx.showModal({
        title: '提示',
        content: '需要您授权录音权限才能使用语音功能',
        confirmText: '去授权',
        success: (res) => {
          if (res.confirm) {
            wx.openSetting({
              success: (settingRes) => {
                if (settingRes.authSetting['scope.record']) {
                  this.setData({
                    hasRecordAuth: true
                  });
                }
              }
            });
          }
        }
      });
    }
  },

  // 开始录音倒计时
  startRecordingCountdown: function() {
    console.log('开始录音倒计时');

    this.setData({
      showCountdown: true,
      countdownNumber: 3,
      isRecording: true,
      inputMessage:''
    });

    // 清除可能存在的倒计时定时器
    this.clearRecordingCountdownTimer();

    // 开始倒计时
    let count = 3;
    const recordingCountdownTimer = setInterval(() => {
      count--;

      if (count > 0) {
        this.setData({
          countdownNumber: count
        });
      } else {
        // 倒计时结束，开始录音
        this.setData({
          showCountdown: false,
          countdownNumber: 3
        });

        // 清除倒计时定时器
        this.clearRecordingCountdownTimer();

        // 开始实际录音
        this.startActualRecording();
      }
    }, 300);

    this.setData({
      recordingCountdownTimer: recordingCountdownTimer
    });
  },

  // 开始实际录音
  startActualRecording: function() {
    console.log('倒计时结束，开始实际录音');

    // 开始录音
    console.log('开始录音', manager);
    manager.start({
      lang: 'zh_CN',
    });

    // 开始录音计时
    this.startRecordTimer();
  },

  // 清除录音倒计时定时器
  clearRecordingCountdownTimer: function() {
    if (this.data.recordingCountdownTimer) {
      clearInterval(this.data.recordingCountdownTimer);
      this.setData({
        recordingCountdownTimer: null
      });
    }
  },

  // 开始录音计时
  startRecordTimer: function() {
    // 清除可能存在的计时器
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    const startTime = Date.now();
    const recordTimer = setInterval(() => {
      const diff = Math.floor((Date.now() - startTime) / 1000);
      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');
      const seconds = (diff % 60).toString().padStart(2, '0');
      const recordingTime = `${minutes}:${seconds}`;

      this.setData({
        recordingTime: recordingTime
      });

      // 如果录音时长超过60秒，自动停止
      if (diff >= 60) {
        this.stopRecording();
      }
    }, 1000);

    this.setData({
      recordTimer: recordTimer
    });
  },

  // 停止录音计时
  stopRecordTimer: function() {
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
      this.setData({
        recordTimer: null,
        recordingTime: '00:00'
      });
    }
  },

  // 手指触摸移动事件
  onTouchMove: function(e) {
    if (!this.data.isRecording) return;

    try {
      // 阻止页面滚动
      e.preventDefault?.(); // 尝试阻止默认事件（可能不支持）

      // 获取当前触摸点的Y坐标
      const currentY = e.detail.touches[0].clientY;
      // 计算移动距离
      const moveDistance = this.data.startY - currentY;

      // 如果向上移动超过50像素，标记为取消状态
      if (moveDistance > 50) {
        if (!this.data.isCancelling) {
          this.setData({
            isCancelling: true
          });
        }
      } else {
        if (this.data.isCancelling) {
          this.setData({
            isCancelling: false
          });
        }
      }
    } catch (error) {
      console.error('处理触摸移动事件失败:', error);
      // 出错时重置录音取消状态，防止页面卡住
      this.setData({
        isCancelling: false
      });
    }

    // 返回false阻止事件冒泡和默认行为
    return false;
  },

  // 停止录音
  stopRecording: function() {
    console.log('stopRecording触发，当前状态:', {
      isRecording: this.data.isRecording,
      showCountdown: this.data.showCountdown,
      isCancelling: this.data.isCancelling
    });

    // 如果正在倒计时，取消倒计时
    if (this.data.showCountdown) {
      console.log('倒计时期间松开，取消倒计时');
      this.cancelRecordingCountdown();
      return;
    }

    if (!this.data.isRecording) return;

    // 如果正在取消录音，则调用取消录音方法
    if (this.data.isCancelling) {
      this.cancelRecording();
      return;
    }

    // 停止录音计时
    this.stopRecordTimer();

    // 停止录音
    manager.stop();

    this.setData({
      isRecording: false,
    });
  },

  // 取消录音
  cancelRecording: function() {
    console.log('cancelRecording', this.data);

    // 如果正在倒计时，取消倒计时
    if (this.data.showCountdown) {
      this.cancelRecordingCountdown();
      return;
    }

    this.setData({
      wasCancelled: true // 设置已取消标志
    });

    // 停止录音计时
    this.stopRecordTimer();

    // 重置录音状态，并设置取消标志
    this.setData({
      isRecording: false,
      isCancelling: false,
    });

    // 停止录音
    manager.stop();
  },

  // 取消录音倒计时
  cancelRecordingCountdown: function() {
    console.log('取消录音倒计时');

    // 清除倒计时定时器
    this.clearRecordingCountdownTimer();

    // 隐藏倒计时遮罩
    this.setData({
      showCountdown: false,
      countdownNumber: 3,
      isRecording: false
    });
  },

  // 处理组件触发的取消倒计时事件
  onCancelCountdown: function() {
    console.log('收到组件取消倒计时事件');
    this.cancelRecordingCountdown();
  },

  // 处理上滑状态变化
  onSlideChange: function(e) {
    console.log('收到上滑状态变化事件', e);
    const { isMovingUp } = e.detail;

    // 更新取消状态
    this.setData({
      isCancelling: isMovingUp
    });
  },

  // 处理录音结束事件
  onRecordingEnd: function(e) {
    console.log('收到录音结束事件', e);
    const { isCancel } = e.detail;

    if (isCancel) {
      // 取消录音
      this.cancelRecording();
    } else {
      // 正常结束录音
      this.stopRecording();
    }
  },

  // 切换输入模式（文本/语音）
  toggleInputType: function() {
    this.setData({
      isVoiceMode: !this.data.isVoiceMode,
      // inputMessage: ''
    });

    if (this.data.isVoiceMode) {
      wx.hideKeyboard();
    }
  },

  // 处理输入框内容变化
  inputChange: function(e) {
    this.setData({
      inputMessage: e.detail.value
    });
  },

  // 播放消息音频
  playMessageAudio: function(e) {
    // 注意：e 可能是页面原生事件，也可能是组件自定义事件
    let index, type, isAudio;

    // 如果是组件事件，获取detail中的数据
    if (e.detail && (e.detail.index !== undefined || e.detail.type)) {
      index = e.detail.index;
      type = e.detail.type;
      isAudio = e.detail.isAudio;
    } else {
      // 如果是原生事件，从dataset获取数据
      index = e.currentTarget.dataset.index;
      type = e.currentTarget.dataset.type;
      isAudio = e.currentTarget.dataset.isAudio;
    }

    const messages = this.data.messages;
    const message = messages[index];

    if (!message || !message.content) return;

    // 如果当前消息正在播放，则停止播放
    if (message.isPlaying) {
      this.stopSpeech();
      this.updateMessagePlayingState(index, false);
      return;
    }

    // 停止所有正在播放的音频
    this.stopAllAudio();

    // 根据消息类型和内容类型执行不同的播放逻辑
    if (type === 'user' && isAudio && message.audioSrc) {
      // 用户消息是录音，直接播放原始录音
      console.log('播放用户录音音频:', message.audioSrc);
      this.playAudioFile(message.audioSrc, index, true);
    } else {
      // AI消息或用户文本消息，使用TTS转换为语音
      console.log('播放文本消息的TTS:', message);
      this.playTextToSpeech(message.content, index, true);
    }
  },

  // 更新消息的播放状态
  updateMessagePlayingState: function(index, isPlaying) {
    if (index < 0 || index >= this.data.messages.length) return;

    this.setData({
      [`messages[${index}].isPlaying`]: isPlaying
    });
  },

  // 停止所有正在播放的音频
  stopAllAudio: function() {
    const messages = this.data.messages;
    if (messages && messages.length > 0) {
      const updatedMessages = messages.map(msg => {
        if (msg.isPlaying) {
          msg.isPlaying = false;
        }
        return msg;
      });

      this.setData({
        messages: updatedMessages,
        isPlaying: false
      });
    }

    // 停止当前播放
    this.stopSpeech();
  },

  /**
   * 初始化音频播放器
   */
  initAudioContext: function() {
    // 确保先销毁之前的实例
    this.releaseAudioContext();

    // 创建内部音频上下文
    const innerAudioContext = wx.createInnerAudioContext({useWebAudioImplement: false});

    // 设置音量为最大值
    innerAudioContext.volume = 1.0;
    innerAudioContext.useWebAudioImplement = true;

    // 音频播放开始事件
    innerAudioContext.onPlay(() => {
      console.log('音频播放开始');
      this.setData({
        isPlaying: true
      });
    });

    // 音频播放结束事件
    innerAudioContext.onEnded(() => {
      this.setData({
        isPlaying: false
      });
    });

    // 音频播放错误事件
    innerAudioContext.onError((res) => {
      console.error('音频播放错误:', res);
      this.setData({
        isPlaying: false
      });
    });

    // 监听音频可以播放事件
    innerAudioContext.onCanplay(() => {
      console.log('音频可以播放');
    });

    this.innerAudioContext = innerAudioContext;
  },

  /**
   * 释放音频上下文资源
   */
  releaseAudioContext: function() {
    if (this.innerAudioContext) {
      try {
        this.innerAudioContext.stop();
        // 移除所有事件监听
        this.innerAudioContext.offEnded();
        this.innerAudioContext.offError();
        this.innerAudioContext.offTimeUpdate();
        this.innerAudioContext.offCanplay();
        this.innerAudioContext.offPlay();
        this.innerAudioContext.destroy();
      } catch (error) {
        console.error('销毁音频上下文失败:', error);
      }
      this.innerAudioContext = null;
    }
  },

  /**
   * 统一的文本转语音播放方法
   */
  playTextToSpeech: function(text, messageIndex = -1, autoPlay = false) {
    if (!text) return;

    // 停止当前正在播放的音频
    this.stopSpeech();

    // 重置timeUpdate检查状态
    this.timeUpdateChecked = false;
    // 重置timeUpdate计数器
    this.timeUpdateCount = 0;

    // 如果指定了消息索引，则更新其播放状态
    if (messageIndex >= 0) {
      // 停止其他正在播放的音频的状态
      this.stopAllAudio();

      // 设置当前消息为播放状态
      if(autoPlay){
        this.updateMessagePlayingState(messageIndex, true);
      }

      // 如果消息已经有语音文件，直接播放
      const message = this.data.messages[messageIndex];
      if (message && message.audioSrc && !message.isAudio) {
        console.log('使用已缓存的TTS语音文件播放:', message.audioSrc);
        this.playAudioFile(message.audioSrc, messageIndex, autoPlay);
        return;
      }
    } else {
      // 通用播放状态
      this.setData({
        isPlaying: true
      });
    }

    // 确保先销毁之前的实例
    this.releaseAudioContext();

    // 创建新的音频实例
    this.innerAudioContext = wx.createInnerAudioContext({useWebAudioImplement: false});

    // 设置音频事件处理
    this.setupAudioEvents(messageIndex);

    // 使用插件进行语音合成
    const plugin = requirePlugin("WechatSI");
    plugin.textToSpeech({
      lang: 'zh_CN',
      tts: true,
      content: text,
      success: (res) => {
        // 获取当前时间
        const currentTime = getFormattedTime();
        console.log('语音合成成功:', currentTime, res);

        if (!res.filename) {
          this.updateMessagePlayingState(messageIndex, false);
          return;
        }

        // 保存语音文件URL到消息对象
        if (messageIndex >= 0) {
          this.setData({
            [`messages[${messageIndex}].audioSrc`]: res.filename
          });
        }

        // 播放合成的音频
        this.playAudioSource(res.filename, autoPlay);
      },
      fail: (res) => {
        console.error('语音合成失败:', res);
        this.updateMessagePlayingState(messageIndex, false);

        // wx.showToast({
        //   title: '语音合成失败',
        //   icon: 'none'
        // });
      }
    });
  },

  /**
   * 设置音频事件处理
   */
  setupAudioEvents: function(messageIndex = -1) {
    if (!this.innerAudioContext) return;

    // 设置音频结束事件
    this.innerAudioContext.onEnded(() => {
      this.updateMessagePlayingState(messageIndex, false);
    });

    // 设置音频错误事件
    this.innerAudioContext.onError((res) => {
      console.error('音频播放错误', res);
      this.updateMessagePlayingState(messageIndex, false);
    });

    // 设置音频加载完成事件
    this.innerAudioContext.onCanplay(() => {
      console.log('音频加载完成:', this.innerAudioContext);
    });

    // 当播放开始时
    this.innerAudioContext.onPlay(() => {
      console.log('音频开始播放');
    });
  },

  /**
   * 播放音频源
   */
  playAudioSource: function(src, autoPlay = false) {
    if (!src || !this.innerAudioContext) return;

    try {
      // 设置音频源
      this.innerAudioContext.src = src;

      // 添加延迟后自动播放，确保src设置完成
      setTimeout(() => {
        try {
          // 先判断是否已被销毁
          if (this.innerAudioContext && autoPlay) {
            this.innerAudioContext.play();
          }
        } catch (error) {
          console.error('播放音频失败:', error);
          this.setData({
            isPlaying: false
          });
        }
      }, 200);
    } catch (error) {
      console.error('设置音频源失败:', error);
      this.setData({
        isPlaying: false
      });
    }
  },

  /**
   * 直接播放语音文件
   */
  playAudioFile: function(audioSrc, messageIndex, autoPlay = false) {
    if (!audioSrc) return;

    // 重置timeUpdate检查状态
    this.timeUpdateChecked = false;
    this.timeUpdateCount = 0;

    // 确保先销毁之前的实例
    this.releaseAudioContext();

    // 创建新的音频实例
    this.innerAudioContext = wx.createInnerAudioContext({useWebAudioImplement: false});

    // 设置音频事件处理
    this.setupAudioEvents(messageIndex);

    // 更新消息状态为播放中
    this.updateMessagePlayingState(messageIndex, true);

    // 播放音频
    this.playAudioSource(audioSrc, autoPlay);
  },

  /**
   * 停止语音播放
   */
  stopSpeech: function() {
    if (this.innerAudioContext) {
      try {
        // 先暂停再停止
        try {
          this.innerAudioContext.pause();
        } catch (e) {
          console.error('暂停音频失败:', e);
        }

        // 停止音频播放
        this.innerAudioContext.stop();
      } catch (error) {
        console.error('停止音频失败:', error);
      }

      // 重置状态
      this.timeUpdateChecked = false;
    }
  },

  /**
   * 检查并设置音频播放权限
   */
  checkAudioPermission: function() {
    // 音频播放不需要显式授权，但可以检查设备静音状态
    wx.getSystemInfo({
      success: (res) => {
        console.log('系统信息:', res);
        // 提示用户确保设备未静音
        if (!res.microphoneAuthorized) {
          wx.showToast({
            title: '请确保已授予麦克风权限',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });

    // 尝试播放一个空音频以初始化音频引擎
    const testAudio = wx.createInnerAudioContext();
    testAudio.autoplay = true;
    testAudio.volume = 0;
    testAudio.playbackRate = 5;
    testAudio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA';
    testAudio.useWebAudioImplement = true;

    // 短暂播放后销毁
    setTimeout(() => {
      testAudio.destroy();
    }, 500);
  },

  /**
   * 请求录音权限
   */
  requestRecordPermission: function() {
    wx.authorize({
      scope: 'scope.record',
      success: () => {
        this.setData({
          hasRecordAuth: true
        });
      },
      fail: () => {
        console.log('未获取录音权限');
        this.setData({
          hasRecordAuth: false
        });

        // 显示获取权限对话框
        wx.showModal({
          title: '提示',
          content: '需要您授权录音权限才能使用语音功能',
          confirmText: '去授权',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.record']) {
                    this.setData({
                      hasRecordAuth: true
                    });
                  }
                }
              });
            }
          }
        });
      }
    });
  },

  // 处理输入框聚焦事件
  onInputFocus: function(e) {
    this.setData({
      keyboardHeight: e.detail.height || 0
    });

    // 键盘弹出后，滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 300);
  },

  // 处理输入框失焦事件
  onInputBlur: function(e) {
    this.setData({
      keyboardHeight: 0,
      inputMessage: e.detail.value
    });

    // 键盘收起后，重新滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 300);
  },

  onInputChange(e) {
    this.setData({
      inputMessage: e.detail.value
    });
  },

  /**
   * 尝试使用流式输出
   */
  tryStreamingOutput(content) {
    try {
      // 检查 WebSocket 实例是否存在
      if (!this.data.streamingSocket) {
        console.log('WebSocket 实例不存在，使用HTTP回退');
        return false;
      }

      // 检查 WebSocket 连接状态
      if (!this.data.streamingSocket.isConnected) {
        console.log('WebSocket 未连接，尝试连接');
        this.connectWebSocketForStreaming(content);
        return true; // 返回 true 表示已启动连接过程
      }

      // 直接发送数据
      this.sendWebSocketData(content);
      return true;

    } catch (error) {
      console.error('WebSocket 流式处理失败:', error);
      return false;
    }
  },

  /**
   * 为流式处理建立 WebSocket 连接
   * @param {string} content - 用户输入内容
   */
  connectWebSocketForStreaming(content) {
    const wsUrl = getPracticeStreamUrl(); // 使用练习模块的WebSocket地址
    const connectionConfig = getConnectionConfig();

    // 重新设置事件监听器（确保在连接前设置）
    this.setupWebSocketEventListeners(this.data.streamingSocket, this.data.typewriterEffect);

    // 异步连接，不阻塞主流程
    this.data.streamingSocket.connect(wsUrl, {
      header: {
        'openid': wx.getStorageSync('openId') || 'test_user',
        'Content-Type': 'application/json'
      },
      timeout: connectionConfig.connectTimeout
    }).then(() => {
      console.log('WebSocket 连接成功');
      // 连接成功后发送数据
      this.sendWebSocketData(content);
    }).catch((error) => {
      console.error('WebSocket 连接失败:', error);
      this.setData({
        isLoading: false,
        isSendDisabled: false
      });
      // 回退到HTTP处理
      // this.simulateStreamingResponse(content);
    });
  },

  /**
   * 发送数据到 WebSocket 服务器
   * @param {string} content - 用户输入内容
   */
  sendWebSocketData(content) {
    const params = {
      type: 'cankaoshi', // 聊天消息类型
      payload: {
        question: content,
        id: this.data.recordId,
        openId:wx.getStorageSync('openId')
      }
    };

    console.log('准备发送 WebSocket 数据:', params);
    if (this.data.streamingSocket && this.data.streamingSocket.isConnected) {
      // 重置打字机效果，防止内容累加
      if (this.data.typewriterEffect) {
        this.data.typewriterEffect.reset();
        console.log('🔄 已重置打字机效果');
      }

      // 在发送数据前创建 AI 消息用于流式输出
      const aiMessage = {
        type: 'ai',
        content: '',
        time: getFormattedTime(),
        isPlaying: false,
        audioDuration: 0,
        audioSrc: '',
        isAudioOnly: false,
        isStreaming: true // 标记为流式输出消息
      };

      const newMessages = [...this.data.messages, aiMessage];
      const messageIndex = newMessages.length - 1;

      this.setData({
        messages: newMessages,
        isStreaming: true,
        streamingMessageIndex: messageIndex,
        isLoading: false
      }, () => {
        // setData 完成后的回调

        // 重置打字机效果，确保新的流式输出不受之前状态影响
        if (this.data.typewriterEffect) {
          console.log('🔄 重置打字机效果状态');
          this.data.typewriterEffect.stop();
          this.data.typewriterEffect.currentText = '';
          this.data.typewriterEffect.targetText = '';
        }

        // 滚动到底部
        this.scrollToBottom();

        // 发送数据
        this.data.streamingSocket.send(params);
        console.log('WebSocket 数据已发送成功');
      });

      // 设置超时检测，如果10秒内没有收到响应，则回退到HTTP
      setTimeout(() => {
        if (this.data.isStreaming && this.data.streamingMessageIndex >= 0) {
          const currentMessage = this.data.messages[this.data.streamingMessageIndex];

          if (currentMessage && currentMessage.isStreaming) {
            // 检查是否有内容
            if (!currentMessage.content || currentMessage.content.trim() === '') {
              console.warn('WebSocket 响应超时且无内容，回退到 HTTP 处理');
              // 只有在没有内容时才移除消息
              const newMessages = this.data.messages.slice(0, -1);
              this.setData({
                messages: newMessages,
                isStreaming: false,
                streamingMessageIndex: -1
              });
              // 执行 HTTP 回退
              this.simulateStreamingResponse(content);
            } else {
              console.warn('WebSocket 响应超时但已有内容，保留现有内容');
              // 有内容时，只停止流式状态，保留内容
              const newMessages = [...this.data.messages];
              newMessages[this.data.streamingMessageIndex].isStreaming = false;
              this.setData({
                messages: newMessages,
                isStreaming: false,
                streamingMessageIndex: -1,
                isSendDisabled: false
              });

              // 停止打字机效果
              if (this.data.typewriterEffect) {
                this.data.typewriterEffect.stop();
              }
            }
          }
        }
      }, 10000);
    } else {
      console.error('WebSocket 未连接，无法发送数据');
      throw new Error('WebSocket 未连接');
    }
  },

  /**
   * 模拟流式响应（实际项目中替换为真实的API调用）
   */
  simulateStreamingResponse(content) {
    // 发送消息到餐考师接口
    sendQuestionToCankaoshi(content, this.data.recordId)
      .then(response => {
        if (response) {
          // 保存返回的recordId
          if (response.id) {
            this.setData({
              recordId: response.id
            });
          }

          // 获取响应文本
          const responseText = response.data.outputs.text || '抱歉，我暂时无法回答这个问题';

          // 使用打字机效果显示响应
          this.displayResponseWithStreaming(responseText);
        }
      })
      .catch(error => {
        console.error('发送消息失败：', error);
        // 如果API调用失败，回退到传统方式
        this.handleStreamingError();
      });
  },

  /**
   * 使用流式效果显示响应
   */
  displayResponseWithStreaming(responseText) {
    if (this.data.typewriterEffect && this.data.streamingMessageIndex >= 0) {
      // 重置打字机效果
      this.data.typewriterEffect.reset();

      // 设置完成回调
      this.data.typewriterEffect.onComplete = (text) => {
        // 打字机效果完成
        this.setData({
          isStreaming: false,
          [`messages[${this.data.streamingMessageIndex}].isStreaming`]: false,
          isSendDisabled: false
        });

        // 滚动到底部
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      };

      // 开始打字机效果
      this.data.typewriterEffect.start(responseText);
    } else {
      // 如果没有打字机效果实例，直接显示内容
      this.setData({
        [`messages[${this.data.streamingMessageIndex}].content`]: responseText,
        [`messages[${this.data.streamingMessageIndex}].isStreaming`]: false,
        isStreaming: false,
        isSendDisabled: false
      });

      // 滚动到底部
      setTimeout(() => {
        this.scrollToBottom();
      }, 100);
    }
  },

  /**
   * 处理流式输出错误
   */
  handleStreamingError() {
    if (this.data.streamingMessageIndex >= 0) {
      const currentMessage = this.data.messages[this.data.streamingMessageIndex];

      // 如果当前消息已经有内容，保留它并标记为完成
      if (currentMessage && currentMessage.content && currentMessage.content.trim()) {
        console.log('💾 保留已输出的内容:', currentMessage.content);
        // 保留消息内容，但标记为非流式状态
        const newMessages = [...this.data.messages];
        newMessages[this.data.streamingMessageIndex].isStreaming = false;

        this.setData({
          messages: newMessages,
          isStreaming: false,
          streamingMessageIndex: -1,
          isSendDisabled: false
        });

        // 停止打字机效果但不清除内容
        if (this.data.typewriterEffect) {
          this.data.typewriterEffect.stop();
        }

      } else {
        // 如果消息没有内容，则移除它
        const newMessages = this.data.messages.slice(0, -1);
        this.setData({
          messages: newMessages,
          isStreaming: false,
          streamingMessageIndex: -1,
          isSendDisabled: false
        });
      }
    } else {
      // 没有流式消息索引，只重置状态
      this.setData({
        isStreaming: false,
        isSendDisabled: false
      });
    }
  },

  /**
   * 传统HTTP消息发送（作为回退方案）
   */
  sendTraditionalMessage(content) {
    // 发送消息到餐考师接口
    sendQuestionToCankaoshi(content, this.data.recordId)
      .then(response => {
        if (response) {
          // 保存返回的recordId
          if (response.id) {
            this.setData({
              recordId: response.id
            });
          }

          // 添加餐考师回复
          const aiMessage = {
            type: 'ai',
            content: response.data.outputs.text || '抱歉，我暂时无法回答这个问题',
            time: getFormattedTime(),
            isPlaying: false,
            audioDuration: 0,
            audioSrc: '',
            isAudioOnly: false
          };

          // 添加消息到列表
          const newMessages = [...this.data.messages, aiMessage];

          this.setData({
            messages: newMessages,
            isSendDisabled: false,
          });
          this.scrollToBottom();
        }
      })
      .catch(error => {
        console.error('发送消息失败：', error);
        this.setData({
          isSendDisabled: false
        });
      });
  },

  /**
   * 初始化流式连接
   */
  initStreamingConnection() {
    try {
      // 获取连接配置
      const connectionConfig = getConnectionConfig();

      // 创建 WebSocket 实例
      const streamingSocket = new StreamingWebSocket({
        maxReconnectAttempts: connectionConfig.reconnect.maxAttempts,
        reconnectDelay: connectionConfig.reconnect.delay,
        heartbeatInterval: connectionConfig.heartbeat.interval,
        connectTimeout: connectionConfig.connectTimeout
      });

      // 保存页面实例引用
      const that = this;

      // 创建打字机效果实例
      const typewriterEffect = new TypewriterEffect({
        speed: 30, // 30ms 一个字符
        onUpdate: (text) => {
          // 更新当前流式输出的消息内容
          if (that.data.streamingMessageIndex >= 0) {
            const newMessages = [...that.data.messages];
            if (newMessages[that.data.streamingMessageIndex]) {
              newMessages[that.data.streamingMessageIndex].content = text;
              that.setData({
                messages: newMessages
              });
            }
          }
        },
        onComplete: (text) => {
          // 打字机效果完成
          if (that.data.streamingMessageIndex >= 0) {
            const newMessages = [...that.data.messages];
            if (newMessages[that.data.streamingMessageIndex]) {
              newMessages[that.data.streamingMessageIndex].content = text;
              newMessages[that.data.streamingMessageIndex].isStreaming = false;

              that.setData({
                messages: newMessages,
                isStreaming: false,
                isSendDisabled: false
              }, () => {
                that.scrollToBottom();
              });
            }
          } else {
            that.setData({
              isStreaming: false,
              isSendDisabled: false
            });
          }
        },
        onError:(error)=>{
        },
      });

      // 设置 WebSocket 事件监听
      this.setupWebSocketEventListeners(streamingSocket, typewriterEffect);

      // 保存实例到页面数据
      this.setData({
        streamingSocket: streamingSocket,
        typewriterEffect: typewriterEffect
      });

    } catch (error) {
      console.error('初始化流式连接失败:', error);
      // 即使初始化失败，也要确保页面能正常工作
      this.setData({
        streamingSocket: null,
        typewriterEffect: null
      });
    }
  },

  /**
   * 设置 WebSocket 事件监听器
   * @param {StreamingWebSocket} streamingSocket - WebSocket 实例
   * @param {TypewriterEffect} typewriterEffect - 打字机效果实例
   */
  setupWebSocketEventListeners(streamingSocket, typewriterEffect) {
    console.log('设置 WebSocket 事件监听器');

    // 保存页面实例的引用
    const that = this;

    // 流式输出开始
    streamingSocket.on('streamStart', (message) => {
      console.log('🚀 流式输出开始:', message);

      // 验证消息索引是否有效
      if (that.data.streamingMessageIndex >= 0 && that.data.messages[that.data.streamingMessageIndex]) {
        // 将数据块追加到打字机效果中
        if (message.data && typewriterEffect) {
          typewriterEffect.append(message.data);
        }
      }
      that.scrollToBottom();
    });

    // 流式输出结束
    streamingSocket.on('streamEnd', (message) => {
      console.log('🏁 流式输出结束:', message);

      // 更新消息的评估结果
      if (that.data.streamingMessageIndex >= 0) {
        const newMessages = [...that.data.messages];
        if (newMessages[that.data.streamingMessageIndex]) {
          newMessages[that.data.streamingMessageIndex].result = message.result || '';
          that.setData({
            messages: newMessages
          });
        }
      }
      that.scrollToBottom();
    });

    // 错误处理
    streamingSocket.on('errorEnd', (message) => {
      console.log('🏁 流式输出error:', message);
      that.setData({
        isLoading: false,
        isSendDisabled: false
      });
      // 对于errorEnd，保留已输出的内容
      that.handleStreamingError();
    });

    streamingSocket.on('error', (error) => {
      console.error('❌ WebSocket 错误:', error);

      // 检查错误类型，区分连接断开和其他错误
      const isConnectionError = error && (
        error.code === 1006 || // 连接异常关闭
        error.code === 1000 || // 正常关闭
        error.message && error.message.includes('连接') ||
        error.message && error.message.includes('disconnect')
      );

      if (isConnectionError) {
        console.log('🔗 WebSocket 连接断开，保留已输出内容');

        // 连接断开时，保留已输出的内容，但停止流式输出
        if (that.data.streamingMessageIndex >= 0) {
          const currentMessage = that.data.messages[that.data.streamingMessageIndex];
          if (currentMessage && currentMessage.content && currentMessage.content.trim()) {
            // 有内容，保留并标记为完成
            const newMessages = [...that.data.messages];
            newMessages[that.data.streamingMessageIndex].isStreaming = false;

            that.setData({
              messages: newMessages,
              isStreaming: false,
              streamingMessageIndex: -1,
              isLoading: false,
              isSendDisabled: false
            });

            // 停止打字机效果
            if (that.data.typewriterEffect) {
              that.data.typewriterEffect.stop();
            }
            return;
          }
        }
      }

      // 其他类型的错误，重置状态
      that.setData({
        isStreaming: false,
        isLoading: false,
        isSendDisabled: false
      });

      // 只有在非连接错误时才调用handleStreamingError
      if (!isConnectionError) {
        that.handleStreamingError();
      }
    });

    // 连接关闭事件
    streamingSocket.on('close', (event) => {
      console.log('🔌 WebSocket 连接关闭:', event);

      // 连接关闭时，如果正在流式输出，保留已输出的内容
      if (that.data.isStreaming && that.data.streamingMessageIndex >= 0) {
        const currentMessage = that.data.messages[that.data.streamingMessageIndex];
        if (currentMessage && currentMessage.content && currentMessage.content.trim()) {
          console.log('💾 连接关闭，保留已输出内容');

          const newMessages = [...that.data.messages];
          newMessages[that.data.streamingMessageIndex].isStreaming = false;

          that.setData({
            messages: newMessages,
            isStreaming: false,
            streamingMessageIndex: -1
          });

          // 停止打字机效果
          if (that.data.typewriterEffect) {
            that.data.typewriterEffect.stop();
          }
        }
      }
    });

  },

  /**
   * 清理流式连接
   */
  cleanupStreamingConnection() {
    console.log('🧹 清理流式连接');

    // 停止打字机效果
    if (this.data.typewriterEffect) {
      this.data.typewriterEffect.stop();
      console.log('⏹️ 打字机效果已停止');
    }

    // 关闭 WebSocket 连接
    if (this.data.streamingSocket) {
      this.data.streamingSocket.close(true); // 传递 true 表示主动关闭
      console.log('🔌 WebSocket 连接已关闭');
    }

    // 清理状态
    this.setData({
      streamingSocket: null,
      typewriterEffect: null,
      isStreaming: false,
      streamingMessageIndex: -1
    });

    console.log('✅ 流式连接清理完成');
  },
})
