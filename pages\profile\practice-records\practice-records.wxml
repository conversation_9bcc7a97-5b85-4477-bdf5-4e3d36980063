<!--pages/profile/practice-records/practice-records.wxml-->
<view class="pages-container">
  <!-- 统计概览 -->
   <view id="statsSummary">

  <stats-summary
    title="练习统计"
    stats="{{statsArray}}"
  />
  </view>

  <!-- 筛选区域 -->
   <view id="filterContainer">
  <view class="filter-container">
    <picker
      range="{{positions}}"
      bindchange="onPositionChange"
      class="filter-select"
      value="{{positionIndex || 0}}"
    >
      <text>{{selectedPosition}}</text>
      <text class="iconfont icon-down"></text>
    </picker>
    <picker
      range="{{levels}}"
      bindchange="onCategoryChange"
      class="filter-select"
      value="{{levelIndex || 0}}"
    >
      <text>{{selectLevel}}</text>
      <text class="iconfont icon-down"></text>
    </picker>
  </view>
  </view>

  <!-- 标签页 -->
   <view id="tabNavigator">
  <tab-navigator
    tabs="{{tabs}}"
    activeTab="{{currentTab}}"
    bind:change="switchTab"
  />
  </view>
  <!-- 页面加载中遮罩 -->
  <!-- <view class="page-loading-mask" wx:if="{{loading}}">
    <view class="loading-wrapper">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view> -->

  <!-- 记录列表 -->
  <view class="records-container {{loading ? 'hidden' : ''}}"
  style="height: {{scrollViewHeight}}px;"
  >
  <scroll-view
    scroll-y="{{true}}"
    bindscrolltolower="loadMore"
    bindscroll="onScroll"
    enable-back-to-top="{{true}}"
    scroll-with-animation="{{true}}"
    refresher-enabled="{{true}}"
    refresher-default-style="black"
    refresher-background="#f8f9fa"
    refresher-triggered="{{loading}}"
    bindrefresherrefresh="refreshRecords"
    style="height: 100%;"
    lower-threshold="50"
  >
    <view class="records-wrapper">
      <block wx:if="{{records && records.length > 0}}">
        <view
          wx:for="{{records}}"
          wx:key="id"
          class="record-card"
          animation="{{item.animation}}"
        >
          <view class="record-header">
            <view class="header">
              <view class="name">{{item.knowledgeName}}</view>
              <text class="record-tag">{{item.status}}</text>
            </view>
              <view class="record-summary">
                <!-- <view class="category-tag">{{item.typeName}}</view> -->
                <view class="position-badge">{{item.positionNameRel}}</view>
                <view class="level-badge">{{item.levelName}}</view>
              </view>
          </view>
          <view class="record-content">
            <view class="record-detail">
              <text class="record-label">练习时长：</text>
              <text>{{item.totalDuration}}</text>
            </view>
            <view class="record-detail">
              <text class="record-label">题目数量：</text>
              <text>{{item.questionNum}}题</text>
            </view>
            <view class="record-detail">
              <text class="record-label">练习时间：</text>
              <text>{{item.createTime}}</text>
            </view>
            <!-- <view class="record-detail">
              <text class="record-label">练习进度</text>
              <text class="accuracy {{item.accuracy >= 80 ? 'high' : ''}}">{{item.accuracy}}%
              </text>
            </view> -->
          </view>
          <view class="card-footer" data-record="{{item}}" bindtap="continuePractice">
            <view class="card-btn">
              查看详情  
              <!-- <text class="iconfont icon-right"></text> -->
              </view>
              </view>
        </view>
      </block>

      <!-- 加载更多状态指示器 -->
      <view class="loading-container" wx:if="{{loadingMore}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载更多...</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view class="no-more-data" wx:if="{{records.length > 0 && !hasMore && !loadingMore}}">
        <text>没有更多记录了</text>
      </view>

      <!-- 空状态 -->
      <empty-tip text="暂无练习记录" wx:if="{{!loading && (!records || records.length === 0)}}" />
    </view>
  </scroll-view>
  </view>
</view>
