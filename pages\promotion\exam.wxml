<!--pages/promotion/exam.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <!-- <custom-nav-bar
    title="{{title}}"
    status-bar-height="{{statusBarHeight}}"
    nav-bar-height="{{navBarHeight}}"
    showTime="{{false}}"
    showBack="{{false}}"
    bind:back="goBack"
    class="exam-nav"
  >
  </custom-nav-bar> -->

  <!-- components/custom-nav-bar/custom-nav-bar.wxml -->
<view class="custom-nav">
   <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  <!-- 导航内容区 -->
  <view class="nav-content" style="height: {{navBarHeight}}px; line-height:{{navBarHeight}}px;">
      {{title}}
  </view>
</view> 


  <!-- 考试进度 -->
  <view class="exam-countdown" style="margin-top:{{totalNavHeight}}px">
    <view class="exam-box">
      <view class="end-exam-btn" bindtap="onEndExam">提交试卷</view>
      <view class="countdown-box {{isCountdownWarning ? 'countdown-warning' : ''}}">
          <view class="iconfont icon-clock"></view>
          {{countdownTime}}
      </view>
      <view>
        {{questionIndex}}/{{examInfo.questionCount}}题
      </view>
    </view>

    <!-- <view class="progress-bar">
      <view class="progress-bar-fill" style="width: {{progressBarWidth}}%"></view>
    </view> -->

    <!-- 提交考试弹窗 -->
  <view class="popup-mask" wx:if="{{showSubmitPopup}}">
    <view class="popup-content">
      <view class="popup-title">提交试卷</view>
      <view class="popup-icon">
        <text class="iconfont icon-chat_conversation"></text>
      </view>
      <view class="popup-desc">
        {{submitPopupContent}}
      </view>
      <view class="popup-buttons">
        <button class="popup-btn confirm" bindtap="submitExam">确认</button>
        <button class="popup-btn cancel" wx:if="{{submitPopupShowCancel}}" bindtap="closePopup">取消</button>
      </view>
    </view>
  </view>

  </view>

  <!-- 聊天内容区域 -->
  <chat-container
    id="chatContainer"
    messages="{{messages}}"
    total-nav-height="{{totalNavHeight+50}}"
    is-recording="{{isRecording}}"
    scroll-top="{{scrollTop}}"
    keyboard-height="{{keyboardHeight}}"
    has-more="{{hasMore}}"
    is-loading="{{isLoading}}"
    page-type="exam"
    bind:scrollToUpper="onScrollToUpper"
    bind:playMessageAudio="playMessageAudio"
    loadingText="{{loadingText}}"
    examFinished="{{examFinished}}"
    showGetQuestionTip="{{showGetQuestionTip}}"
    bind:getQuestion="onGetQuestion"
  />


  <!-- 底部输入区域 -->
  <input-area
    input-content="{{inputContent}}"
    is-voice-mode="{{isVoiceMode}}"
    is-recording="{{isRecording}}"
    keyboard-height="{{keyboardHeight}}"
    bind:sendMessage="sendMessage"
    bind:inputFocus="onInputFocus"
    bind:inputBlur="onInputBlur"
    bind:toggleInputType="toggleInputType"
    bind:startRecording="startRecording"
    bind:stopRecording="stopRecording"
    bind:touchMove="onTouchMove"
    bind:cancelRecording="cancelRecording"
    bind:inputChange="onInputChange"
  />

  <!-- 录音提示蒙层 -->
  <recording-mask
    is-recording="{{isRecording}}"
    is-cancelling="{{isCancelling}}"
    recording-time="{{recordingTime}}"
    show-countdown="{{showCountdown}}"
    countdown-number="{{countdownNumber}}"
    bind:cancelCountdown="onCancelCountdown"
    bind:slideChange="onSlideChange"
    bind:recordingEnd="onRecordingEnd"
  />
</view>