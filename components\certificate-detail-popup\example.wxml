<!-- 使用示例页面 -->
<view class="example-page">
  <view class="example-header">
    <text class="example-title">证书详情弹框组件示例</text>
  </view>

  <view class="example-content">
    <!-- 示例按钮 -->
    <view class="example-section">
      <text class="section-title">基础用法</text>
      <button class="example-btn" bindtap="showBasicExample">显示基础弹框</button>
    </view>

    <view class="example-section">
      <text class="section-title">带操作按钮</text>
      <button class="example-btn" bindtap="showWithActions">显示带操作按钮的弹框</button>
    </view>

    <view class="example-section">
      <text class="section-title">自定义标题</text>
      <button class="example-btn" bindtap="showCustomTitle">显示自定义标题弹框</button>
    </view>

    <view class="example-section">
      <text class="section-title">加载状态</text>
      <button class="example-btn" bindtap="showLoadingExample">显示加载状态</button>
    </view>

    <view class="example-section">
      <text class="section-title">错误状态</text>
      <button class="example-btn" bindtap="showErrorExample">显示错误状态</button>
    </view>
  </view>

  <!-- 证书详情弹框组件 -->
  <certificate-detail-popup
    visible="{{showCertPopup}}"
    certificateUrl="{{certificateUrl}}"
    title="{{popupTitle}}"
    showActions="{{showActions}}"
    showShareButton="{{showShareButton}}"
    showSaveButton="{{showSaveButton}}"
    maskClosable="{{maskClosable}}"
    loading="{{loading}}"
    error="{{error}}"
    bind:close="onCloseCertPopup"
    bind:share="onShareCert"
    bind:savesuccess="onSaveSuccess"
    bind:savefail="onSaveFail"
    bind:downloadfail="onDownloadFail"
    bind:longpress="onLongPress"
    bind:imagetap="onImageTap"
    bind:imageload="onImageLoad"
    bind:imageerror="onImageError"
    bind:retry="onRetry"
  />
</view>
