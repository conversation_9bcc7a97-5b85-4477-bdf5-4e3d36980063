/* pages/master-chat/index.wxss */
.container {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
  height: 100vh;
  overflow: hidden;
  padding-bottom:0px !important;
}

.master-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  position: relative;
}


/* components/custom-nav-bar/custom-nav-bar.wxss */
/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  background-color: #f8f8f8;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  background-color: #f8f8f8;
}

/* 导航内容 */
.nav-content {
  display: flex;
  /* align-items: space-between; */
  padding: 0 20rpx;
  box-sizing: border-box;
  position: relative;
  /* justify-content: space-between; */
}

.nav-left {
  width: 60rpx;
  display: flex;
  /* align-items: center; */
  /* justify-content: flex-start; */
}

.nav-left .iconfont {
  font-size: 40rpx;
  color: #333;
}

.nav-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

.nav-title {
  font-size: 34rpx;
  color: #333;
  font-weight: bold;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-subtitle {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
  margin-top: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

