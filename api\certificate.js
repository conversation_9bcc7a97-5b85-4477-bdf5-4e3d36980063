// api/certificate.js
import { get, post } from '../utils/request';


/**
 * 获取证书详情
 * @param {String} id - 证书ID
 * @returns {Promise<Object>} 证书详情
 */
export const getCertificateDetail = (id) => {
  return get(`/certificate/${id}`);
};

/**
 * 通过微信API获取证书详情
 * @param {String} certId - 证书ID
 * @returns {Promise<Object>} 证书详情
 */
export const getCertificateDetailByWechat = (id) => {
  return get('/wechat/getCertificateDetail', { id });
};

/**
 * 分享证书
 * @param {String} id - 证书ID
 * @returns {Promise<Object>} 分享结果
 */
export const shareCertificate = (id) => {
  return post('/certificate/share', { id });
};

/**
 * 获取成长路径数据
 * @returns {Promise<Object>} 成长路径数据
 */
export const getGrowthPath = () => {
  return get('/user/growth-path');
};

/**
 * 获取证书等级数据
 * @returns {Promise<Array>} 证书等级列表
 */
export const getCertificateLevels = () => {
  return get('/certificate/levels');
};

/**
 * 获取证书列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} 证书列表
 */
export const getCertificateList = (params) => {
  return get('/certificate/list', params);
};

/**
 * 获取我的证书列表
 * @returns {Promise<Object>} 
 */
export const getMyCertificateList = (params) => {
  return get('/wechat/myinfo/certificates', params);
}; 