# 组件使用说明

本项目包含以下通用组件，可在考试和练习页面中复用：

## 1. 自定义导航栏 (custom-nav-bar)

导航栏组件，支持自定义标题、副标题和时间显示。

**主要属性：**
- `title`: 标题文本
- `desc`: 描述文本/副标题
- `statusBarHeight`: 状态栏高度
- `navBarHeight`: 导航栏高度
- `formatTime`: 右侧显示的时间文本

**事件：**
- `bind:back`: 返回按钮点击事件

## 2. 聊天容器 (chat-container)

聊天消息列表容器，包含加载状态和加载更多功能。

**主要属性：**
- `messages`: 消息数组
- `isRecording`: 是否正在录音
- `scrollTop`: 滚动位置
- `totalNavHeight`: 导航栏总高度(包含状态栏)
- `keyboardHeight`: 键盘高度
- `hasMore`: 是否有更多历史消息
- `isLoading`: 是否正在加载新消息
- `pageType`: 页面类型，'exam'(考试) 或 'practice'(练习)
- `isPracticeStarted`: 练习是否已开始
- `isNextBtnDisabled`: 下一题按钮是否禁用

**事件：**
- `bind:scrollToUpper`: 滚动到顶部事件
- `bind:playMessageAudio`: 播放消息音频事件
- `bind:startPractice`: 开始练习事件
- `bind:getNextQuestion`: 获取下一题事件

## 3. 消息项 (message-item)

单条消息项组件，支持AI消息、用户消息和系统消息三种类型。

**主要属性：**
- `message`: 消息对象
- `pageType`: 页面类型，'exam'(考试) 或 'practice'(练习)
- `isPracticeStarted`: 练习是否已开始
- `isNextBtnDisabled`: 下一题按钮是否禁用

**事件：**
- `bind:playAudio`: 播放音频事件
- `bind:startPractice`: 开始练习事件
- `bind:nextQuestion`: 下一题事件

## 4. 输入区域 (input-area)

底部输入区域组件，支持文本输入和语音输入。

**主要属性：**
- `inputContent`: 输入框文本内容
- `isVoiceMode`: 是否为语音模式
- `isRecording`: 是否正在录音
- `keyboardHeight`: 键盘高度

**事件：**
- `bind:sendMessage`: 发送消息事件
- `bind:inputFocus`: 输入框获取焦点事件
- `bind:inputBlur`: 输入框失去焦点事件
- `bind:toggleInputType`: 切换输入类型事件
- `bind:startRecording`: 开始录音事件
- `bind:stopRecording`: 结束录音事件
- `bind:touchMove`: 触摸移动事件
- `bind:cancelRecording`: 取消录音事件

## 5. 录音蒙层 (recording-mask)

录音状态显示蒙层组件。

**主要属性：**
- `isRecording`: 是否正在录音
- `isCancelling`: 是否正在取消录音
- `recordingTime`: 录音时间显示文本

## 使用示例

### 考试页面(exam)：

```html
<!-- 自定义导航栏 -->
<custom-nav-bar 
  title="{{title}}"
  desc="{{desc}}"
  status-bar-height="{{statusBarHeight}}"
  nav-bar-height="{{navBarHeight}}"
  format-time="{{formatTime}}"
  bind:back="goBack"
/>

<!-- 聊天内容区域 -->
<chat-container
  messages="{{messages}}"
  is-recording="{{isRecording}}"
  scroll-top="{{scrollTop}}"
  total-nav-height="{{totalNavHeight}}"
  keyboard-height="{{keyboardHeight}}"
  has-more="{{hasMore}}"
  is-loading="{{isLoading}}"
  page-type="exam"
  bind:scrollToUpper="onScrollToUpper"
  bind:playMessageAudio="playMessageAudio"
/>
```

### 练习页面(practice)：

```html
<!-- 聊天内容区域 -->
<chat-container
  messages="{{messages}}"
  is-recording="{{isRecording}}"
  scroll-top="{{scrollTop}}"
  total-nav-height="{{totalNavHeight}}"
  keyboard-height="{{keyboardHeight}}"
  has-more="{{hasMore}}"
  is-loading="{{isLoading}}"
  page-type="practice"
  is-practice-started="{{isPracticeStarted}}"
  is-next-btn-disabled="{{isNextBtnDisabled}}"
  bind:scrollToUpper="onScrollToUpper"
  bind:playMessageAudio="playMessageAudio"
  bind:startPractice="startPractice"
  bind:getNextQuestion="getNextQuestion"
/>
``` 