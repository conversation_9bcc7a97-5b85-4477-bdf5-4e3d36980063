/* pages/home/<USER>/

.container {
  padding: 32rpx;
  background-color: #f8f9fa;
}

/* Banner区域 */
.banner {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border-radius: 32rpx;
  padding: 50rpx 32rpx;
  margin-bottom: 32rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(161, 140, 209, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.banner-left{
  flex:1
}
.user-title{
  font-size: 24rpx;
  color: #fff;
  margin-bottom: 16rpx;
  display: block;
  position: relative;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
  display: block;
  position: relative;
  z-index: 2;
}

.banner-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  max-width: 80%;
  position: relative;
  z-index: 2;
}

.banner-pattern {
  /* position: absolute;
  right: 20rpx;
  bottom: 20rpx; */
  width: 200rpx;
  height: 200rpx;
  /* opacity: 0.3; */
  z-index: 1;
}

/* 上次练习记录卡片 */
.study-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}

.study-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.study-card-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.study-card-title .iconfont {
  color: #a18cd1;
  margin-right: 12rpx;
  font-size: 32rpx;
}

.study-card-time {
  font-size: 24rpx;
  color: #999;
  background: rgba(161, 140, 209, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
}

.study-card-content {
  padding: 24rpx 32rpx 32rpx;
}

.study-course-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.course-tag {
  font-size: 22rpx;
  color: #fff;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  font-weight: normal;
}

.study-course-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 24rpx;
  display: block;
}

.progress-container {
  margin-top: 16rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.progress-bar-wrapper {
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #a18cd1, #fbc2eb);
  border-radius: 6rpx;
}

.study-card-footer {
  padding: 24rpx 32rpx;
  border-top: 2rpx solid #f0f0f0;
}

.continue-btn {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #fff;
  border-radius: 16rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  height: 88rpx;
  box-shadow: 0 6rpx 20rpx rgba(161, 140, 209, 0.3);
}

.continue-btn .iconfont {
  margin-right: 12rpx;
}

.position-level{
  font-size: 24rpx;
  color: #666;
  margin-bottom: 24rpx;
  display: block;
  background: #fff;
  padding: 10rpx 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
}
/* 推荐练习内容卡片 */


.recommend-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.recommend-badge {
  font-size: 22rpx;
  color: #fff;
  background: linear-gradient(135deg, #a18cd1 0%, #a18cd1 100%);
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
}

.recommend-courses {
  padding: 16rpx 0;
}

.recommend-course {
  /* display: flex; */
  /* align-items: center; */
  padding: 24rpx 32rpx;
  position: relative;
}

.recommend-course:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 32rpx;
  right: 32rpx;
  bottom: 0;
  height: 2rpx;
  background: #f0f0f0;
}

.course-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.1) 0%, rgba(251, 194, 235, 0.1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.course-icon .iconfont {
  font-size: 40rpx;
  color: #a18cd1;
}

.course-info {
  flex: 1;
}

.course-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.course-desc {
  font-size: 24rpx;
  color: #999;
}

.course-action .iconfont {
  font-size: 32rpx;
  color: #ccc;
}

.stat-row {
  margin-bottom: 12rpx;
  font-size: 22rpx;
  color: #777;
  font-weight: 500;
  text-align: right;
}

.progress-bar {
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin: 12rpx 0;
  overflow: hidden;
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #a18cd1, #fbc2eb);
  border-radius: 8rpx;
  position: relative;
  transition: width 0.3s ease;
}


.all-courses-btn {
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #a18cd1;
  border-top: 2rpx solid #f0f0f0;
}

.all-courses-btn .iconfont {
  font-size: 24rpx;
}

/* 统计区域 */
.stats-container {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-header {
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.stats-tabs {
  display: flex;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  overflow: hidden;
}

.stats-tab {
  padding: 8rpx 24rpx;
  font-size: 24rpx;
  color: #666;
  position: relative;
}

.stats-tab.active {
  color: #fff;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}

.stats-content {
  padding: 40rpx 32rpx 32rpx;
  height:347rpx;
}

.position-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 55rpx 25rpx;
}
.position-item-content{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.position-stat-item .iconBG{
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  /* position: absolute;
  right: 10rpx;
  bottom: 10rpx; */
}
.position-stat-item .iconBG .iconfont{
  font-size: 80rpx;
  color: #a18cd1;
  opacity: 0.3;
}

.position-stat-item {
  background: rgba(161, 140, 209, 0.05);
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.position-name {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
  display: flex;
  align-items: center;
}

.position-name .iconfont {
  margin-right: 8rpx;
  color: #a18cd1;
}

.position-value {
  font-size: 42rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  -webkit-background-clip: text;
  color: transparent;
}
.position-value i{
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
}
.empty-container{
  height:100%;
}
.cert-stats {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.cert-stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(161, 140, 209, 0.05);
  border-radius: 16rpx;
  padding: 24rpx;
}

.cert-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(161, 140, 209, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.cert-icon .iconfont {
  font-size: 40rpx;
  color: #a18cd1;
}

.cert-value {
  font-size: 42rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 8rpx;
}
.cert-value i{
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
}

.cert-label {
  font-size: 24rpx;
  color: #666;
}

.view-cert-btn {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #fff;
  border-radius: 16rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  height: 88rpx;
  box-shadow: 0 6rpx 20rpx rgba(161, 140, 209, 0.3);
}

.view-cert-btn .iconfont {
  margin-right: 12rpx;
}

/* 问餐考师悬浮按钮 */
.master-btn-container {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 100;
}

.master-btn {
  pointer-events: auto;
  display: flex;
  align-items: center;
  background: #fff;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.1);
  border-radius: 48rpx;
  padding: 16rpx 24rpx;
  width: 200rpx;
  position: relative;
  height:fit-content;
}

.chef-hat-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.chef-hat-icon .iconfont {
  color: #fff;
  font-size: 32rpx;
}

.master-btn-text {
  display: flex;
  flex-direction: column;
}

.question {
  font-size: 20rpx;
  color: #999;
}

.ask-master {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}

/* 认证弹窗 */
.auth-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.auth-modal {
  width: 80%;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.1);
}

.auth-header {
  padding: 48rpx 0 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-bottom: 2rpx solid #f0f0f0;
}

.auth-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.auth-icon .iconfont {
  color: #fff;
  font-size: 48rpx;
}

.auth-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.auth-subtitle {
  font-size: 24rpx;
  color: #999;
}

.auth-form {
  padding: 32rpx;
}

.form-group {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input.error {
  border-color: #ff4d4f;
}

.form-error {
  font-size: 22rpx;
  color: #ff4d4f;
  margin-top: 8rpx;
  display: block;
}

.form-hint {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

/* 为初始化版块添加样式 */
.init-section {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  margin: 30rpx 0;
  padding: 30rpx;
  box-sizing: border-box;
}

.init-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.init-section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.init-section-title .iconfont {
  font-size: 36rpx;
  color: #a18cd1;
  margin-right: 10rpx;
}

.init-section-content {
  width: 100%;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9fb;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-item:active::after {
  opacity: 1;
}

.feature-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.feature-icon .iconfont {
  font-size: 48rpx;
  color: #fff;
}

.practice-icon {
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
}

.exam-icon {
  background: linear-gradient(135deg, #84fab0, #8fd3f4);
}

.analysis-icon {
  background: linear-gradient(135deg, #fad0c4, #ffd1ff);
}

.aimaster-icon {
  background: linear-gradient(135deg, #6a11cb, #2575fc);
}

.feature-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.init-guide {
  text-align: center;
  background: linear-gradient(to right, #f6f7fb, #f0f1f7);
  padding: 24rpx;
  border-radius: 12rpx;
  position: relative;
}

.init-guide::before {
  content: '';
  position: absolute;
  top: -16rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-bottom: 16rpx solid #f6f7fb;
}

.guide-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.guide-desc {
  font-size: 26rpx;
  color: #666;
}

/* 新用户认证弹窗的按钮样式 */
.auth-actions {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  margin-top: 30rpx;
}

.auth-cancel-btn, .auth-submit-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.auth-cancel-btn {
  background-color: #f5f7fa;
  color: #666;
  border: 1rpx solid #eee;
}

.auth-submit-btn {
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(161, 140, 209, 0.3);
}

/* 通知公告模块 */

.home-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}
.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.home-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.home-title .iconfont {
  color: #a18cd1;
  margin-right: 12rpx;
  font-size: 32rpx;
}

.notice-more {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.notice-more .iconfont {
  font-size: 24rpx;
  margin-left: 4rpx;
}

.notice-content {
  padding: 0px 32rpx;
}

.notice-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  line-height: 70rpx;
  height:70rpx;
  box-sizing: border-box;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-item-title {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}

.notice-badge {
  display: inline-block;
  background-color: #ff6b6b;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
}

.notice-item-meta {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.notice-time {
  font-size: 24rpx;
  color: #999;
  margin-right: 10rpx;
}

.notice-item-meta .iconfont {
  font-size: 24rpx;
  color: #999;
}

.empty-notice {
  padding: 40rpx 0;
}

/* 通知轮播样式 */
.notice-swiper-container {
  height: 140rpx;
  overflow: hidden;
}

.notice-swiper {
  height: 100%;
}

.notice-swiper-item {
  height: 70rpx;
  line-height: 70rpx;
  display: flex;
  align-items: center;
  border-bottom:1px solid #ececec
}

.notice-swiper-item .notice-item {
  width: 100%;
  height: 100%;
  border-bottom: none;
  margin: 0;
  padding: 0;
}