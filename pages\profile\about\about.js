// pages/profile/about.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 核心功能数据
    features: [
      { icon: 'icon-book', name: '智能练习', gradient: 'purple-gradient' },
      { icon: 'icon-trophy', name: '晋升考试', gradient: 'blue-gradient' },
      { icon: 'icon-icon-test', name: '能力分析', gradient: 'green-gradient' },
      { icon: 'icon-AIassistant', name: '智能助手', gradient: 'purple-gradient' },
      { icon: 'icon-System-Fill', name: '成就体系', gradient: 'blue-gradient' },
      { icon: 'icon-a-002', name: '技能提升', gradient: 'green-gradient' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 页面加载时的逻辑
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '餐烤餐考-专业的餐饮培训考核平台',
      path: '/pages/profile/about'
    }
  }
})