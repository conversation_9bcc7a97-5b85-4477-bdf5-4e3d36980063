// pages/profile/feedback.js
// 引入反馈相关的API
import { submitFeedback, getHistoryFeedback } from '../../../api/user'
import request from '../../../utils/request'
import {  getToken } from '../../../utils/auth'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    content: '',
    contactPerson: '',
    contactInfo: '',
    images: [],
    uploadedImages: [],
    isValid: false,
    historyList: [],
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 加载历史反馈数据
    this.loadHistoryFeedback();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新重新加载历史反馈
    this.loadHistoryFeedback().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 加载更多历史反馈
    const { currentPage, pageSize, total } = this.data.pagination;
    if (currentPage * pageSize < total) {
      this.loadMoreHistoryFeedback();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 预览历史反馈中的图片
   */
  previewHistoryImage(e) {
    const { current, urls } = e.currentTarget.dataset;
    if (!current || !urls || !urls.length) return;

    wx.previewImage({
      current: current, // 当前显示图片的http链接
      urls: urls // 需要预览的图片http链接列表
    });
  },

  /**
   * 输入反馈内容
   */
  inputContent(e) {
    this.setData({
      content: e.detail.value
    });
    this.checkValid();
  },

  /**
   * 输入联系人
   */
  inputContactPerson(e) {
    this.setData({
      contactPerson: e.detail.value
    });
    this.checkValid();
  },

  /**
   * 输入联系方式
   */
  inputContactInfo(e) {
    this.setData({
      contactInfo: e.detail.value
    });
    this.checkValid();
  },

  /**
   * 校验联系方式格式
   */
  validateContactInfo(contactInfo) {
    if (!contactInfo || !contactInfo.trim()) return false;
    return true;

    // // 手机号正则表达式（11位数字，1开头）
    // const phoneRegex = /^1[3-9]\d{9}$/;
    // // 邮箱正则表达式
    // const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    // return phoneRegex.test(contactInfo.trim()) || emailRegex.test(contactInfo.trim());
  },

  /**
   * 校验表单是否有效
   */
  checkValid() {
    const contentValid = this.data.content.trim().length > 0;
    const contactPersonValid = this.data.contactPerson.trim().length > 0;
    const contactInfoValid = this.data.contactInfo.trim().length > 0 &&
                            this.validateContactInfo(this.data.contactInfo);

    const isValid = contentValid && contactPersonValid && contactInfoValid;
    console.log('校验结果:', isValid,contentValid,contactPersonValid,contactInfoValid);
    this.setData({
      isValid
    });
  },

  /**
   * 选择图片
   */
  chooseImage() {
    // 计算还可以选择的图片数量
    const canSelectCount = 4 - this.data.images.length;
    if (canSelectCount <= 0) return;

    wx.chooseImage({
      count: canSelectCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 先保存临时路径到本地显示
        const tempFilePaths = res.tempFilePaths;
        // 将新的图片添加到列表中
        const newImages = [...this.data.images];

        // 上传每张新选择的图片
        tempFilePaths.forEach(filePath => {
          // 添加到本地显示
          newImages.push(filePath);
        });

        this.setData({
          images: newImages
        });

        // 上传新选择的所有图片
        this.uploadImages(tempFilePaths);
      }
    });
  },

  /**
   * 上传多张图片
   */
  uploadImages(tempFilePaths) {
    if (!tempFilePaths || tempFilePaths.length === 0) return;

    // 从request.js中获取baseURL
    const baseURL = request.config.baseURL;
    const imgURL = request.config.imgURL || '';

    // 上传API路径
    const uploadUrl = `${baseURL}/wechat/myinfo/upload-feedback-image`;

    console.log('开始上传图片，数量:', tempFilePaths.length);
    console.log('上传图片API地址:', uploadUrl);

    // 显示上传中提示
    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });

    // 获取授权令牌
    const token = getToken();

    // 处理每张图片的上传
    const uploadPromises = tempFilePaths.map(filePath => {
      return new Promise((resolve, reject) => {
        // 直接使用wx.uploadFile实现上传
        wx.uploadFile({
          url: uploadUrl,
          filePath,
          name: 'file',
          header: {
            'Authorization': `Bearer ${token}`,
            'token': `${token}`,
            'openId': `${token}`
          },
          success(res) {
            // 解析返回结果
            let data;
            try {
              data = JSON.parse(res.data);
            } catch (e) {
              data = res.data;
            }

            if (res.statusCode !== 200) {
              console.error('上传失败:', data.message || '上传失败');
              reject(new Error(data.message || '上传失败'));
            } else {
                console.log('图片上传成功:', data.data.fileUrl);
                resolve(data.data.fileUrl);
            }
          },
          fail(err) {
            console.error('文件上传失败', err);
            reject(err);
          }
        });
      });
    });

    // 等待所有图片上传完成
    Promise.all(uploadPromises.map(p => p.catch(e => null)))
      .then(results => {
        // 过滤掉上传失败的结果
        const validUrls = results.filter(url => url !== null);
        console.log('所有图片上传完成,有效URL数:', validUrls.length);

        // 获取原有的已上传图片URL
        const uploadedImages = [...(this.data.uploadedImages || []), ...validUrls];

        // 更新数据
        this.setData({
          uploadedImages
        });

        wx.hideLoading();

        // 如果有上传失败的图片，提示用户
        if (validUrls.length < tempFilePaths.length) {
          wx.showToast({
            title: '部分图片上传失败',
            icon: 'none'
          });
        }
      })
      .catch(error => {
        console.error('上传过程发生错误:', error);
        wx.hideLoading();
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
      });
  },

  /**
   * 删除图片
   */
  removeImage(e) {
    const index = e.currentTarget.dataset.index;

    // 移除本地图片路径
    const images = this.data.images;
    images.splice(index, 1);

    // 同时移除已上传的图片URL (如果有)
    const uploadedImages = this.data.uploadedImages || [];
    if (uploadedImages.length > index) {
      uploadedImages.splice(index, 1);
    }

    this.setData({
      images,
      uploadedImages
    });

    console.log('移除图片后剩余图片数:', images.length);
    console.log('移除图片后剩余已上传URLs:', uploadedImages);
  },

  /**
   * 提交反馈
   */
  async submitFeedback() {
    if (!this.data.isValid) return;

    // 再次验证必填字段
    if (!this.data.content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    if (!this.data.contactPerson.trim()) {
      wx.showToast({
        title: '请输入联系人',
        icon: 'none'
      });
      return;
    }

    if (!this.data.contactInfo.trim()) {
      wx.showToast({
        title: '请输入联系方式',
        icon: 'none'
      });
      return;
    }

    if (!this.validateContactInfo(this.data.contactInfo)) {
      wx.showToast({
        title: '请输入正确的手机号或邮箱',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '提交中...',
        mask: true
      });

      // 打印调试日志
      console.log('开始提交反馈');

      // 使用已上传的图片URL列表
      let feedbackImgs = this.data.uploadedImages || [];
      console.log('使用已上传的图片URLs:', feedbackImgs);

      // 确保所有图片都已上传
      if (this.data.images.length > feedbackImgs.length) {
        wx.showToast({
          title: '等待图片上传完成',
          icon: 'none'
        });
        wx.hideLoading();
        return;
      }

      // 确保所有URL都有效
      feedbackImgs = feedbackImgs.filter(url => url && typeof url === 'string');

      // 组装提交数据
      const submitData = {
        contactPerson: this.data.contactPerson,
        contactInfo: this.data.contactInfo,
        feedbackContent: this.data.content,
        feedbackImgs: feedbackImgs.length > 0 ? feedbackImgs.join(',') : ''
      };

      // 提交反馈数据
      const result = await submitFeedback(submitData);
      console.log('提交反馈成功:', result);

      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });

      // 重置表单
      this.setData({
        content: '',
        contactPerson: '',
        contactInfo: '',
        images: [],
        uploadedImages: [],
        isValid: false
      });

      // 刷新历史反馈
      this.loadHistoryFeedback();

    } catch (error) {
      console.error('提交失败:', error);
      if (error.message) {
        console.error('错误信息:', error.message);
      }

      wx.hideLoading();
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 格式化反馈时间
   */
  formatFeedbackTime(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) return timeStr;

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化反馈数据
   */
  formatFeedbackData(feedbackItem) {
    let images = feedbackItem.images&&feedbackItem.images.length>0?feedbackItem.images.split(','):
                  (feedbackItem.feedbackImgs ? feedbackItem.feedbackImgs.split(',') : []);
      let imgURL = request.config.imgURL;
      images=images.map(item=>imgURL+item)

      console.log('格式化反馈数据:',images)
    return {
      id: feedbackItem.id,
      content: feedbackItem.feedbackContent || '',
      date: this.formatFeedbackTime(feedbackItem.feedbackTime || feedbackItem.createTime),
      status: feedbackItem.hasReply ? 'resolved' : 'pending',
      statusText: feedbackItem.hasReply ? '已处理' : '处理中',
      reply: feedbackItem.replyContent || '',
      images: images,
      contactPerson: feedbackItem.contactPerson || '',
      contactInfo: feedbackItem.contactInfo || ''
    };
  },

  /**
   * 加载更多历史反馈
   */
  async loadMoreHistoryFeedback() {
    const { currentPage, pageSize, total } = this.data.pagination;
    if (currentPage * pageSize >= total) return;

    const nextPage = currentPage + 1;

    try {
      wx.showLoading({
        title: '加载更多...',
        mask: true
      });

      // 调用接口获取下一页数据
      const response = await getHistoryFeedback({
        page: nextPage,
        pageSize: pageSize
      });

      wx.hideLoading();

      // 解析响应数据
      if (response) {
        const list = response.list;
        const pagination = response.pagination;

        if (Array.isArray(list) && list.length > 0) {
          // 格式化新的反馈数据
          const formattedFeedbacks = list.map(item => this.formatFeedbackData(item));

          // 将新数据添加到当前列表
          this.setData({
            historyList: [...this.data.historyList, ...formattedFeedbacks],
            pagination: pagination || {
              currentPage: nextPage,
              pageSize: pageSize,
              total: this.data.pagination.total
            }
          });
        }
      }
    } catch (error) {
      wx.hideLoading();
      console.error('加载更多历史反馈失败：', error);
      wx.showToast({
        title: '加载更多失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载历史反馈
   */
  async loadHistoryFeedback() {
    try {
      console.log('开始获取历史反馈');

      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 直接调用接口获取数据
      const response = await getHistoryFeedback({
        page: 1,
        pageSize: 10
      });

      wx.hideLoading();

      console.log('获取历史反馈响应:', response);

      // 解析响应数据
      if (response ) {
        const list = response.list;
        const pagination = response.pagination;


        if (!Array.isArray(list) || list.length === 0) {
          this.setData({
            historyList: [],
            pagination: {
              currentPage: 1,
              pageSize: 10,
              total: 0
            }
          });
          return;
        }

        // 格式化反馈数据
        const formattedFeedbacks = list.map(item => this.formatFeedbackData(item));

        this.setData({
          historyList: formattedFeedbacks,
          pagination: pagination || {
            currentPage: 1,
            pageSize: 10,
            total: list.length
          }
        });

        console.log('格式化后的历史反馈:', formattedFeedbacks);
      } else {
        this.setData({
          historyList: [],
          pagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0
          }
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('获取历史反馈失败：', error);
      if (error.message) {
        console.error('错误信息:', error.message);
      }
      // 如果获取历史反馈失败，则设置为空数组，防止页面异常
      this.setData({
        historyList: [],
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        }
      });
    }
  }
})