<view class="tab-bar" id="tab-bar-{{forceUpdate}}">
  <view class="tab-bar-border"></view>
  <view class="tab-bar-content {{list.length === 2 ? 'two-tabs' : 'four-tabs'}}" style="margin-bottom:{{safeAreaBottom}}rpx">
    <view wx:for="{{list}}" wx:key="pagePath" class="tab-bar-item {{selected ===index ? 'active' : ''}}" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
      <view class="icon-wrapper">
        <!-- <image class="icon" src="{{selected ===index ? item.selectedIconPath : item.iconPath}}"></image> -->
        <text class="iconfont {{selected ===index ? 'selected':''}} {{item.icon}}" style="font-size:{{item.fontSize}}"></text>
      </view>
      <view class="text-wrapper">
        <text class="tab-bar-item-text">{{item.text}}</text>
      </view>
    </view>
  </view>
</view> 
