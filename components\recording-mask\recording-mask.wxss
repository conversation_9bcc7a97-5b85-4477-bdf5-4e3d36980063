/* components/recording-mask/recording-mask.wxss */
/* 统一的录音遮罩 */
.recording-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s;
}

/* 倒计时模式 */
.recording-mask.countdown-mode {
  background-color: rgba(0, 0, 0, 0.7);
}

/* 录音模式 */
.recording-mask:not(.countdown-mode) {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 取消状态 */
.recording-mask.cancelling {
  background-color: rgba(255, 71, 87, 0.8);
}

.countdown-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.countdown-circle {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  animation: countdown-pulse 1s ease-in-out infinite;
  box-shadow: 0 0 40rpx rgba(161, 140, 209, 0.6);
}

.countdown-number {
  font-size: 80rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.countdown-text {
  font-size: 32rpx;
  color: #ffffff;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.countdown-tip {
  font-size: 24rpx;
  color: #cccccc;
}

@keyframes countdown-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 40rpx rgba(161, 140, 209, 0.6);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 60rpx rgba(161, 140, 209, 0.8);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 40rpx rgba(161, 140, 209, 0.6);
  }
}



.recording-mask.cancelling {
  background-color: rgba(241, 67, 67, 0.5);
}

/* 录音指示器 */
.recording-indicator {
  width: 300rpx;
  height: 350rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 录音图标 */
.recording-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #a18cd1;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
}

.recording-icon.cancel-icon {
  background-color: #f14343;
}

/* 录音波纹 */
.recording-waves {
  position: absolute;
  width: 100%;
  height: 100%;
}

.wave {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4rpx solid #a18cd1;
  box-sizing: border-box;
  border-radius: 50%;
  animation: wave 1.5s infinite;
}

@keyframes wave {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

/* 取消图标 */
.cancel-icon-inner {
  font-size: 60rpx;
  color: #ffffff;
}

/* 录音文字 */
.recording-text {
  font-size: 28rpx;
  color: #ffffff;
  margin-bottom: 20rpx;
}

/* 取消提示 */
.cancel-tip {
  font-size: 24rpx;
  color: #cccccc;
}

/* 录音时间 */
.recording-time {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #ffffff;
}