/* pages/profile/about.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.about-container {
  padding: 32rpx;
}

.app-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0 60rpx 0;
}

.app-logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 40rpx;
  background: var( --primary-gradient);
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 50rpx rgba(161, 140, 209, 0.3);
}

.app-logo .iconfont {
  font-size: 80rpx;
  color: #fff;
}

.app-name {
  font-size: 44rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.app-version {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.app-slogan {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  max-width: 500rpx;
}

.section-card {
  background: #fff;
  border-radius: 32rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  font-weight: 600;
  display: flex;
  align-items: center;
}

.section-header .iconfont {
  color: #a18cd1;
  margin-right: 16rpx;
  font-size: 32rpx;
}

.section-header text {
  font-size: 32rpx;
}

.section-content {
  padding: 32rpx;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.feature-item {
  background-color: #f8f9fa;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.feature-icon .iconfont {
  font-size: 44rpx;
  color: #fff;
}

.feature-name {
  font-size: 24rpx;
  font-weight: 500;
}

.purple-gradient {
  background: var( --primary-gradient);
}

.blue-gradient {
  background: linear-gradient(135deg, #90dffe 0%, #38a3d1 100%);
}

.green-gradient {
  background: linear-gradient(135deg, #a8edea 0%, #59c173 100%);
}

.team-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: space-between;
}

.team-member {
  width: calc(50% - 10rpx);
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.member-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.member-info {
  flex-grow: 1;
}

.member-name {
  font-weight: 600;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  display: block;
}

.member-role {
  font-size: 24rpx;
  color: #999;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.2) 0%, rgba(251, 194, 235, 0.2) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a18cd1;
  margin-right: 32rpx;
  flex-shrink: 0;
}

.contact-icon .iconfont {
  font-size: 36rpx;
}

.contact-text {
  flex-grow: 1;
}

.contact-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 4rpx;
  display: block;
}

.contact-value {
  font-size: 28rpx;
  font-weight: 500;
}

.copyright {
  text-align: center;
  margin-top: 48rpx;
  margin-bottom: 32rpx;
}

.copyright text {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.copyright text:first-child {
  margin-bottom: 8rpx;
} 