<!--pages/profile/exam-records.wxml-->
<wxs module="helper">
// 获取考试状态样式类
function getStatusClass(status) {
  return status === '已通过' ? 'status-pass' : 'status-fail';
}

// 获取分数样式类
function getScoreClass(score, passScore) {
  if (score >= passScore) return 'score-high';
  return 'score-low';
}

module.exports = {
  getStatusClass: getStatusClass,
  getScoreClass: getScoreClass
};
</wxs>

<view class="pages-container">
  <!-- 统计概览 -->
   <view id="examStats">
  <stats-summary 
    title="考试统计" 
    stats="{{statsArray}}"
    >
    <view slot="rightBtn">
      <button class="filter-button" bindtap="showPositionDrawer">
        <text class="iconfont icon-user"></text>
        <text>岗位</text>
      </button>
    </view>
  </stats-summary>
  </view>
  <!-- 标签页 -->
  <tab-navigator
    id="tabContainer"
    tabs="{{tabs}}"
    isObjectTab="{{true}}"
    labelKey="name"
    activeTab="{{currentTab.value}}"
    bind:change="switchTab"
  />

 <view class="records-container"
  style="height: {{scrollViewHeight}}px;"
  >
  <!-- 考试记录列表 -->
  <scroll-view 
    scroll-y 
    bindscrolltolower="loadMore"
    refresher-enabled="{{true}}"
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onRefresh"
    style="height: 100%;"
    class="{{tabSwitching ? 'switching' : ''}}"
  >
    <block wx:if="{{examRecords.length > 0}}">
      <view 
        wx:for="{{examRecords}}" 
        wx:key="index"
        class="exam-card"
      >
        <text 
          class="exam-status {{helper.getStatusClass(item.confirmStatusText)}}"
        >{{item.confirmStatusText}}</text>
        
        <view class="exam-header">
          <text class="exam-title">{{item.examSubject}}</text>
          <text class="exam-date">{{item.examTime}}</text>
        </view>
        
        <view class="exam-content">
          <view class="score-section">
            <!-- {{helper.getScoreClass(item.score, item.passScore)}} -->
            <view 
              class="exam-score"
            >{{item.correctAnswerCount}}</view>
            <view class="exam-info" wx:if="{{item.confirmStatusText=='待确认'}}">
              <view class="info-label">考试成绩{{item.score}}分钟</view>
              <view class="info-value">考试合格分数{{ item.passScore }}分钟</view>
            </view>
             <view class="exam-info" wx:else>
              <view class="info-label">{{item.score >= item.passScore ? '已通过' : '未通过'}}</view>
              <view class="info-value">{{item.score >= item.passScore ? '已通过考试标准' : '未通过考试标准'}}</view>
            </view>
          </view>
          <view class="exam-info">
            <view class="detail-row">
              <text class="detail-label">考试时长</text>
              <text>{{item.usedDuration}}/{{item.examDuration}} 分钟</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">答题情况</text>
              <text>{{item.answerTotal}}/{{item.questionTotal}} 题</text>
            </view>
            <!-- <view class="detail-row">
              <text class="detail-label">练习进度</text>
              <text>{{item.accuracy}} %</text>
            </view> -->
            
          </view>
        </view>
        
        <view class="card-footer">
          <view  class="card-btn" data-record="{{item}}" bindtap="goExamRecord">查看试卷  
            <!-- <text class="iconfont icon-right"></text> -->
          </view>
           <view  class="card-btn" data-record="{{item}}" bindtap="goReportRecord">查看报告  
            <!-- <text class="iconfont icon-right"></text> -->
          </view>
        </view>
      </view>
    </block>
     <!-- 空状态 -->
     <empty-tip text="暂无考试记录" wx:else />
  
    <!-- 加载更多状态指示器 -->
    <view class="loading-container" wx:if="{{loadingMore}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载更多...</text>
    </view>

    <!-- 没有更多数据提示 -->
    <view class="no-more-data" wx:if="{{examRecords.length > 0 &&!loadingMore}}">
      <text>没有更多记录了</text>
    </view>
    
   </scroll-view>
  </view>

  <!-- 岗位选择抽屉 -->
  <view class="drawer-mask" wx:if="{{positionDrawerVisible}}" bindtap="closePositionDrawer" catchtouchmove="catchTouchMove"></view>
  <view class="drawer position-drawer {{positionDrawerVisible ? 'drawer-show' : ''}}" catchtouchmove="catchTouchMove">
    <view class="drawer-header">
      <text class="drawer-title">当前岗位：{{currentPosition}}</text>
      <text class="iconfont icon-close" bindtap="closePositionDrawer"></text>
    </view>
    <scroll-view class="drawer-body" scroll-y="{{true}}" wx:if="{{positions.length > 0}}">
      <view
        class="selector-item {{item.id === positionValue ? 'selector-item-active' : ''}}"
        wx:for="{{positions}}"
        wx:key="value"
        bindtap="selectPosition"
        data-position="{{item}}"
      >
        <view class="selector-icon">
          <text class="iconfont icon-user"></text>
        </view>
        <view class="selector-info">
          <text class="selector-title">{{item.name}}</text>
          <!-- <text class="selector-subtitle">{{item.desc}}</text> -->
        </view>
        <view class="selector-check" wx:if="{{item.id === positionValue}}">
          <text class="iconfont icon-check"></text>
        </view>
      </view>
    </scroll-view>
    <view class="drawer-body" wx:else>
      <empty-tip text="暂无岗位" />
    </view>
  </view>
</view>