<view class="certificate-card" bindtap="handleButtonClick">
   <!-- <view class="cert-status-badge status-{{module.status}}">
      <text class="iconfont {{module.buttonIcon}}"></text>
      <text>{{module.statusText}}</text>
    </view> -->
  <view class="certificate-icon">
    <!-- <text class="iconfont icon-book-open"></text> -->
     {{module.examConfig.positionLevel}}
  </view>
  <view class="certificate-content">
   <view class="content-title">
    <view class="certificate-title">{{module.title}}</view>
    <!-- <view class="certificate-subtitle">{{module.subtitle}}</view> -->
    </view>
    <view
      class="certificate-button {{module.buttonType}}-btn"
    >
      <text class="iconfont {{module.buttonIcon}}"></text>
      <text>{{module.buttonText}}</text>
    </view>
  </view>
</view>
