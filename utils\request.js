// utils/request.js
import { mockRequest } from '../mock/index';

// 开发环境配置
const config = {
  // API基础URL
  baseURL: 'https://cankao-admin.dev.lingmiaoai.com/api',
  // baseURL:'http://************:3000/api',
  // wsURL:'ws://************:3000/ws',
  // baseURL:'http://localhost:3000/api',
  wsURL:'wss://cankao-admin-api.dev.lingmiaoaii.com/ws',

  difyBaseURL: 'https://cankao-sa.dev.lingmiaoai.com/api',
  imgURL:'https://cankao-admin-api.dev.lingmiaoai.com',
  // imgURL:'http://localhost:3000',
  // 是否启用模拟数据
  enableMock: false,
  // 请求超时时间(ms)
  timeout: 10000
};

/**
 * 发起网络请求
 * @param {Object} options - 请求选项
 * @param {String} [options.customBaseURL] - 自定义基础URL
 * @returns {Promise} - 返回Promise对象
 */
const request = (options) => {
  const { url, method = 'GET', data = {}, header = {}, customBaseURL } = options;

  // 完整URL路径
  let baseURL = '';
  if(header.isDify){
    baseURL = config.difyBaseURL;
    delete header.isDify;
  }else{
    baseURL = customBaseURL || config.baseURL;
  }
  const fullUrl = url.startsWith('http') ? url : `${baseURL}${url}`;

  // 检查是否使用mock数据，如果有自定义baseURL则不使用mock
  if ((config.enableMock && !customBaseURL)||options.header.enableMock) {
    return mockRequest(`/api${url}`, data, method);
  }
  // 发起真实网络请求
  return new Promise((resolve, reject) => {
    wx.request({
      url: fullUrl,
      method,
      data,
      header: {
        // 'token': wx.getStorageSync('token'),
        'content-type': 'application/json',
        'openid':wx.getStorageSync('openId'),
        ...header
      },
      timeout: config.timeout,
      success: (res) => {
        // 请求成功，检查业务状态码
        if (res.statusCode == 200||res.statusCode==201) {
          // 业务层成功判断
          if(customBaseURL){
            resolve(res.data.data || res.data);
          }else{
            if (res.data.code === 0 || res.data.code === 200) {
              resolve(res.data.data || res.data);
            } else {
              // 业务层错误
              reject({
                code: res.data.code,
                message: res.data.message || '请求失败'
              });

              // 显示错误提示
              wx.showToast({
                title: res.data.message || '请求失败',
                icon: 'none',
                duration: 2000
              });
            }
          }
        } else {
          // TODO: 暂时屏蔽HTTP错误
          // reject({
          //   code: res.statusCode,
          //   message: res.data.message
          // });
          // 针对考试答题接口，返回数据格式不一样
          if(url=='/wechat/exam/questions/answer'){
            resolve(res.data.data);
          }else{
            // 显示错误提示
            wx.showToast({
              title: res.data.message,
              icon: 'none',
              duration: 2000
            });
          }
      }
      },
      fail: (err) => {
        // 网络请求失败
        reject({
          code: -1,
          message: err.errMsg || '网络请求失败'
        });

        // 显示错误提示
        wx.showToast({
          title: '网络请求失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  });
};

// 导出请求方法别名
export const get = (url, data = {}, header = {}, customBaseURL) => {
  return request({ url, method: 'GET', data, header, customBaseURL });
};

export const post = (url, data = {}, header = {}, customBaseURL) => {
  return request({ url, method: 'POST', data, header, customBaseURL });
};

export const put = (url, data = {}, header = {}, customBaseURL) => {
  return request({ url, method: 'PUT', data, header, customBaseURL });
};

export const del = (url, data = {}, header = {}, customBaseURL) => {
  return request({ url, method: 'DELETE', data, header, customBaseURL });
};

export default {
  request,
  get,
  post,
  put,
  delete: del,
  config
};
