// pages/practice/list.js
// 从uniapp的pages/practice/list.vue转换而来

// 导入API
const { getPracticeList ,postPosition,postLevel,getDictList} = require('../../api/practice')
const authBehavior = require('../../behaviors/auth-behavior')
const { getUserInfo,getPositionList ,getPositions,getLevels,getOtherPosition} = require('../../api/user')

Page({
  // 使用授权行为
  behaviors: [authBehavior],

  // 页面的初始数据
  data: {
    showGuide: true,
    currentPosition: '',
    currentLevel: '',
    currentLevelId: '',
    positions: [],
    otherPositions:[],
    promotionPositions: [],
    levels: [],
    allLevels: {}, // 缓存所有级别数据，包括常规级别和高级别
    practiceData: {},
    isLoading: false,
    positionValue: '',
    requiredCourses: [],
    practiceCourses: [],
    positionDrawerVisible: false,
    levelDrawerVisible: false,
    pageScroll: true, // 添加页面滚动控制状态
    userInfo: null, // 存储用户信息
    higherLevels: [], // 存储高级别列表
    tabItems: [
      { id: '1', name: '晋升岗位' },
      { id: '2', name: '其他岗位' }
    ],
    tabPositionId: wx.getStorageSync('tabPositionId')||'1'
  },


  // 生命周期函数--监听页面加载
  onLoad: function(options) {
    wx.setStorageSync('practicePositionId',null)
    wx.setStorageSync('practiceLevelId',null)
    wx.setStorageSync('tabPositionId','1')
  },

  // 页面显示时检查授权状态
  onShow: function() {
    this.init()

    // 更新TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: wx.getStorageSync('selectedTab') });
    }
  },
  init(){
    // 检查授权状态，并根据授权结果决定是否加载数据
    const authResult = this.checkAuthStatus();
    // 加载用户信息
    const userInfo = wx.getStorageSync('userInfo');
    // 只有在已授权的情况下才加载数据
    if (authResult&&wx.getStorageSync('isIdentityVerified')) {
      if (userInfo) {
        // 缓存中没有记录切换的岗位id 使用默认岗位
          this.setPosition(userInfo)
        this.initData();
      }else{
          getUserInfo( wx.getStorageSync('openId')).then(result => {
          if (result) {
            // 保存用户信息到本地
            wx.setStorageSync('userInfo', result.user);
            this.setPosition(result.user)
            this.initData();

          }
        })
      }
    }
  },
  selectTab(e){
    this.setData({
      tabPositionId: e.currentTarget.dataset.tab
    })
    // 更新岗位列表
    if(e.currentTarget.dataset.tab=='1'){
      this.fetchPositionsData()
    }else{
      this.getPromotionPositions()
    }
  },
  setPosition(info){
    if(!wx.getStorageSync('practicePositionId')){
      wx.setStorageSync('practicePositionId',info.positionId)
      wx.setStorageSync('practiceLevelId',info.currentLevel)
    }
    // 如果岗位id不一致 则更新岗位id
    // if(info&&info.positionId!=wx.getStorageSync('practicePositionId')){
    //   this.setData({
    //     currentPosition: info.positionName,
    //   });
    // }
    // // 如果级别id不一致 则更新级别id
    // if(info&&info.levelId!=wx.getStorageSync('practiceLevelId')){
    //   this.setData({
    //     currentLevel: info.currentLevelName,
    //   });
    // }
    this.setData({
      userInfo: info,
      // currentPosition: info.positionName,
      // currentLevel: info.currentLevelName
    });
  },
  // 初始化页面数据
  async initData() {
    this.setData({
      isLoading: true
    });

    let id=wx.getStorageSync('tabPositionId')||'1'
     id=='1'?await this.fetchPositionsData():await this.getPromotionPositions()
    // 同时获取岗位和级别数据
      // this.getPromotionPositions(),
      // this.fetchPositionsData(),
      await this.fetchAllLevelsData()
    // ])
    .then(() => {
      // 设置当前岗位和级别
      this.setupCurrentPositionAndLevel();
      // 获取课程列表
      return this.fetchPracticeList();
    })
    .catch(error => {
      console.error('初始化数据失败：', error);
      // wx.showToast({
      //   title: '加载数据失败，请重试',
      //   icon: 'none'
      // });
    })
    .finally(() => {
      this.setData({
        isLoading: false
      });
    });
  },

  // 获取其他岗位数据
  getPromotionPositions: function() {
    return new Promise((resolve, reject) => {
      // 其他岗位
      getOtherPosition()
        .then(result => {
          this.setData({
            positions: result.rows,
            otherPositions: result.rows
          });
          resolve(result);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  // 获取晋升岗位
  fetchPositionsData: function() {
    return new Promise((resolve, reject) => {
      getPositionList().then(result => {
          //设置高级别列表
          // this.setData({
          //   higherLevels: [...result.higherLevels].reverse()
          // });
          if (result && Array.isArray(result.rows)) {
            this.setData({
              positions: result.rows,
              // 晋升岗位列表
              promotionPositions: result.rows
            });
            resolve(result.rows);
          } else {
            reject(new Error('获取岗位列表数据格式错误'));
          }
        })
        .catch(error => {
          console.error('获取岗位列表失败：', error);
          reject(error);
        });
      });
  },

  // 获取所有级别数据
  fetchAllLevelsData: function() {
    return new Promise((resolve, reject) => {
      getLevels()
        .then(result => {
          if (result && Array.isArray(result) && result.length) {
            // 存储常规级别数据
            this.setData({
              allLevels: result
            });
            resolve(result);
          } else {
            reject(new Error('获取级别列表数据格式错误'));
          }
        })
        .catch(error => {
          console.error('获取级别列表失败：', error);
          reject(error);
        });
    });
  },

  // 设置当前岗位和级别
  setupCurrentPositionAndLevel: function() {
    // 优先从缓存获取练习页面选择的岗位和级别
    const practicePositionId = wx.getStorageSync('practicePositionId');
    const practiceLevelId = wx.getStorageSync('practiceLevelId');
    // 如果有缓存的练习页面岗位选择
    if (practicePositionId && this.data.positions.length > 0) {
      const currentPosition = this.data.positions.find(position => position.id === practicePositionId);
      const currentOtherPosition = this.data.otherPositions.find(position => position.id === practicePositionId);
      if (currentPosition||currentOtherPosition) {
        this.setData({
          currentPosition: currentPosition.name,
          positionValue: currentPosition.id
        });
      } else {
        // 如果找不到缓存的岗位，回退到用户默认岗位
        this.useDefaultPosition();
      }
    } else {
      
      // 没有缓存的练习页面岗位选择，使用默认岗位
      this.useDefaultPosition();
    }

    // 根据当前岗位和用户信息决定使用哪种级别列表
    this.updateLevelsList();
    // 如果有缓存的练习页面级别选择
    if (practiceLevelId && this.data.levels.length > 0) {
      const currentLevel = this.data.levels.find(level => level.id === practiceLevelId);
      if (currentLevel) {
        this.setData({
          currentLevel: currentLevel.name,
          currentLevelId: currentLevel.id
        });
      } else {
        // 如果找不到缓存的级别，使用默认级别
        this.useDefaultLevel();
      }
    } else {
      // 没有缓存的练习页面级别选择，使用默认级别
      this.useDefaultLevel();
    }
  },
  
  // 使用用户默认岗位
  useDefaultPosition: function() {
    // 从缓存获取当前选择的岗位
    // const storedPositionId = wx.getStorageSync('currentPosition');
    // if (storedPositionId && this.data.positions.length > 0) {
    //   const currentPosition = this.data.positions.find(position => position.id === storedPositionId);
    //   if (currentPosition) {
        this.setData({
          currentPosition: wx.getStorageSync('userInfo').positionName,
          positionValue: wx.getStorageSync('userInfo').positionId,
          currentLevel: wx.getStorageSync('userInfo').currentLevelName,
          currentLevelId: wx.getStorageSync('userInfo').currentLevel
        });
    //   }
    // }

  },
  
  // 使用用户默认级别
  useDefaultLevel: function() {
    // 从缓存获取当前选择的级别
    const storedLevelId = wx.getStorageSync('userInfo').currentLevel;
    if (storedLevelId && this.data.levels.length > 0) {
      const currentLevel = this.data.levels.find(level => level.id === storedLevelId);
      if (currentLevel) {
        this.setData({
          currentLevel: currentLevel.name,
          currentLevelId: currentLevel.id
        });
      }
    }
  },

  // 更新级别列表
  updateLevelsList: function() {
    const userInfo = this.data.userInfo;
    // const currentLevelId = wx.getStorageSync('currentLevel');
    const currentLevelId = wx.getStorageSync('practiceLevelId')||wx.getStorageSync('userInfo').currentLevel;
    // 判断用户是否选择了晋升岗位
    const promotionItem = this.data.promotionPositions.find(item=>item.id==this.data.positionValue)
    if ( promotionItem) {
      // if (userInfo && userInfo.currentPosition == this.data.positionValue) {
      // 使用高级别列表
      this.setData({
        levels: promotionItem.higherLevels
        // TODO:
        // levels:this.data.allLevels
      });
      // 检查当前选中的级别是否存在于高级别列表中
      if (currentLevelId) {
        const levelExists = promotionItem.higherLevels.some(level => level.id === currentLevelId);
        // 如果当前选择的级别不在高级别列表中，则默认选择用户的当前级别
        if (!levelExists && userInfo.currentLevel) {
          // 查找用户当前级别信息
          const userLevel = promotionItem.higherLevels.find(level => level.id === userInfo.currentLevel);
          if (userLevel) {
            // 更新选中的级别为用户的当前级别
            // wx.setStorageSync('currentLevel', userLevel.id);
            wx.setStorageSync('practiceLevelId', userLevel.id);
            this.setData({
              currentLevel: userLevel.name,
              currentLevelId: userLevel.id
            });
          }
        }else{
          // 如果当前选择的级别不在高级别列表中，则默认选择用户的当前级别
          // wx.setStorageSync('practiceLevelId', userInfo.currentLevel);
//切换岗位后 如果缓存中没有记录切换的级别id 使用默认级别
          if(!wx.getStorageSync('practiceLevelId')){
            this.setData({
              currentLevel: userInfo.currentLevelName,
              currentLevelId: userInfo.currentLevel
            });
          }
        }
      }else{
        this.setData({
          currentLevel: promotionItem.higherLevels[0].name,
          currentLevelId: promotionItem.higherLevels[0].id
        });
      }
    } else {
      // 如果用户选择的不是自己所属的岗位，使用常规级别列表
      this.setData({
        levels: this.data.allLevels || []
      });
      
      // 检查当前选中的级别是否存在于常规级别列表中
      if (currentLevelId) {
        const levelExists = this.data.allLevels.some(level => level.id === currentLevelId);
        
        // 如果当前选择的级别不在常规级别列表中，则选择第一个级别
        if (!levelExists && this.data.allLevels.length > 0) {
          const firstLevel = this.data.allLevels[0];
          
          // 更新选中的级别为第一个级别
          // wx.setStorageSync('currentLevel', firstLevel.id);
          wx.setStorageSync('practiceLevelId', firstLevel.id);

          this.setData({
            currentLevel: firstLevel.name,
            currentLevelId: firstLevel.id
          });
          
        }
      }
    }
  },

  // 获取练习列表
  fetchPracticeList: function() {
    // wx.showLoading({
    //   title: '加载中...',
    //   mask: true
    // });

    const params = {
      positionName: this.data.positionValue,
      positionLevel: this.data.currentLevelId,
      openId: wx.getStorageSync('openId'),
    };
    return getPracticeList(params)
      .then(result => {
        if (result) {
          // 更新数据缓存
          let practiceData = {...this.data.practiceData};
          if (!practiceData[this.data.positionValue]) {
            practiceData[this.data.positionValue] = {};
          }

          practiceData[this.data.positionValue][this.data.currentLevelId] = result;

          this.setData({
            practiceData: practiceData
          });
          // 更新列表显示
          this.updatePracticeLists();
        }
      })
      .catch(error => {
        console.error('获取列表失败：', error);
        wx.showToast({
          title: '获取列表失败',
          icon: 'none'
        });
      })
      .finally(() => {
        // wx.hideLoading();
      });
  },

  // 更新列表数据
  updatePracticeLists: function() {
    let courses = this.getCurrentPractice();
    if (Array.isArray(courses)) {
      courses=courses.map(item=>{
        let tip=''
        if(item.questionMode=='时长'){
          tip=`${item.totalStudyDuration}/${item.totalRequireDuration}分钟`
        }else{
          tip=`${item.totalQuestionNum}/${item.practiceQuestionCount}题`
        }

        return {
          ...item,
          tip
        }
      })
      this.setData({
        requiredCourses: courses.filter(course => course && course.status=='必考'),
        practiceCourses :courses.filter(course => course && course.status=='必练')
      });
    } else {
      this.setData({
        requiredCourses: [],
        practiceCourses: []
      });
    }
  },

  // 获取当前岗位和级别的数据
  getCurrentPractice: function() {
    const { practiceData, positionValue, currentLevelId } = this.data;
    if (!practiceData || !positionValue || !currentLevelId) {
      return [];
    }
    const positionData = practiceData[positionValue];
    if (!positionData) {
      return [];
    }
    return positionData[currentLevelId] || [];
  },

  // 显示岗位选择抽屉
  showPositionDrawer: function() {
    this.setData({
      positionDrawerVisible: true,
      pageScroll: false // 禁用页面滚动
    });

    // 禁用页面滚动
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });

    // 设置页面样式，禁止滚动
    wx.setPageStyle({
      style: {
        overflow: 'hidden'
      }
    });
  },

  // 关闭岗位选择抽屉
  closePositionDrawer: function() {
    this.setData({
      positionDrawerVisible: false,
      pageScroll: true // 恢复页面滚动
    });

    // 恢复页面滚动
    wx.setPageStyle({
      style: {
        overflow: 'auto'
      }
    });
  },

  // 显示级别选择抽屉
  showLevelDrawer: function() {
    this.setData({
      levelDrawerVisible: true,
      pageScroll: false // 禁用页面滚动
    });

    // 禁用页面滚动
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });

    // 设置页面样式，禁止滚动
    wx.setPageStyle({
      style: {
        overflow: 'hidden'
      }
    });
  },

  // 关闭级别选择抽屉
  closeLevelDrawer: function() {
    this.setData({
      levelDrawerVisible: false,
      pageScroll: true // 恢复页面滚动
    });

    // 恢复页面滚动
    wx.setPageStyle({
      style: {
        overflow: 'auto'
      }
    });
  },

  // 选择岗位
  selectPosition: function(e) {
    const position = e.currentTarget.dataset.position;
    postPosition({
      openId: wx.getStorageSync('openId'),
      positionId: position.id
    }).then(res => {
      if(res.success){
        // 保存选择到专用存储
        wx.setStorageSync('practicePositionId', position.id);
        wx.setStorageSync('tabPositionId',this.data.tabPositionId)
        this.setData({
          currentPosition: position.name,
          positionValue: position.id
        });
        this.closePositionDrawer();
        
        // 切换岗位后更新级别列表
        this.updateLevelsList();
        
        // 重新获取列表
        this.fetchPracticeList();
      }
    })
  },

  // 选择级别
  selectLevel: function(e) {
    const level = e.currentTarget.dataset.level;
    postLevel({
      openId: wx.getStorageSync('openId'),
      levelId: level.id
    }).then(res => {
      if(res.success){
        // 保存选择到专用存储
        wx.setStorageSync('practiceLevelId', level.id);
        this.setData({
          currentLevel: level.name,
          currentLevelId: level.id
        });
        this.closeLevelDrawer();

        // 切换级别后重新获取列表
        this.fetchPracticeList();
      }
    })
  },

  // 详情页导航
  goCourseDetail: function(e) {
    const course = e.currentTarget.dataset.course;
    // 使用授权行为中的方法进行导航
    this.navigateToPractice(course);
  },

  // 关闭引导提示
  closeGuide: function() {
    this.setData({
      showGuide: false
    });
    // 存储到本地，下次不再显示
    wx.setStorageSync('practiceListGuideShown', true);
  },
  onHide: function() {
    // 当用户离开当前tab时，清除授权状态
    this.clearAuthStatus();

    // 确保页面离开时恢复滚动
    this.setData({
      positionDrawerVisible: false,
      levelDrawerVisible: false,
      pageScroll: true
    });

    // 恢复页面滚动
    wx.setPageStyle({
      style: {
        overflow: 'auto'
      }
    });
  },

  // 阻止页面滚动的事件处理函数
  catchTouchMove: function() {
    return false;
  },

  // Tab项被点击时触发
  onTabItemTap: function(item) {
    // 更新选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: wx.getStorageSync('selectedTab')});
    }
  },
})
