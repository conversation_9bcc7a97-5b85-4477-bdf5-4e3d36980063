/* components/identity-verify-popup/identity-verify-popup.wxss */

/* 身份认证弹窗样式 */
.identity-verify-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.35s ease;
}

.identity-verify-popup.visible {
  opacity: 1;
  visibility: visible;
}

.auth-modal {
  width: 85%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.identity-verify-popup.visible .auth-modal {
  transform: scale(1);
}

.auth-header {
  padding: 36rpx 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.auth-header::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -2rpx;
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #a18cd1, #fbc2eb);
  transform: translateX(-50%);
  border-radius: 2rpx;
}

.auth-icon {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
  border-radius: 50%;
  margin: 0 auto 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.auth-icon .iconfont {
  font-size: 48rpx;
  color: #fff;
}

.auth-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.auth-subtitle {
  font-size: 28rpx;
  color: #666;
}

.auth-form {
  padding: 36rpx;
}

.form-group {
  margin-bottom: 28rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  position: relative;
  padding-left: 14rpx;
}

.form-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 4rpx;
  height: 16rpx;
  background: linear-gradient(180deg, #a18cd1, #fbc2eb);
  transform: translateY(-50%);
  border-radius: 2rpx;
}

.form-input {
  width: 100%;
  height: 90rpx;
  background-color: #f9f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  font-size: 30rpx;
  color: #333;
  border: 1rpx solid #eee;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #a18cd1;
  box-shadow: 0 0 0 2rpx rgba(161, 140, 209, 0.1);
}

.form-input.error {
  border: 1rpx solid #ff6b6b;
  background-color: rgba(255, 107, 107, 0.05);
}

.form-error {
  display: block;
  font-size: 24rpx;
  color: #ff6b6b;
  margin-top: 10rpx;
  padding-left: 10rpx;
}

.form-hint {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  padding-left: 10rpx;
}

.auth-actions {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
  margin-top: 40rpx;
}

.auth-cancel-btn, .auth-submit-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.auth-cancel-btn {
  background-color: #f5f7fa;
  color: #666;
  border: 1rpx solid #eee;
}

.auth-cancel-btn:active {
  background-color: #eee;
}

.auth-submit-btn {
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(161, 140, 209, 0.3);
  position: relative;
  overflow: hidden;
}

.auth-submit-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
  transform: skewX(-25deg) translateX(-100%);
  transition: all 0.5s ease;
}

.auth-submit-btn:active::after {
  transform: skewX(-25deg) translateX(100%);
}

.auth-submit-btn[disabled] {
  background: linear-gradient(135deg, #d8d0e8, #fde9f6);
  box-shadow: none;
} 

