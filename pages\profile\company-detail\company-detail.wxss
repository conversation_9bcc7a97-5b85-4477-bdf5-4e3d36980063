.card {
  background-color: #fff;
  border-radius: 12px;
  margin: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.company-banner {
  background:linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: white;
  padding: 25px 20rpx;
  border-radius: 12px 12px 0 0;
}

.company-name {
  font-weight: bold;
  margin-bottom: 8px;
}

.company-period {
  opacity: 0.9;
}

.company-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
  padding: 20rpx 20rpx;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin: 0 20rpx 0rpx 20rpx;
}

.stat-item {
  flex: 1;
  padding: 0 10px;
}

.stat-value {
  font-weight: bold;
  color: var(--primary-color);
}

.stat-label {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

.section-title {
  font-weight: bold;
  margin: 10rpx 0 20rpx;
  display: flex;
  align-items: center;
}

.section-title .iconfont {
  margin-right: 20rpx;
  color: var(--primary-color);
  font-size:26rpx;
}

.position-card {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 20rpx;
  overflow: hidden;
  border: 1px solid #eaeaea;
}

.position-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f9f7ff;
}

.position-title {
  font-weight: bold;
}

.position-level {
  color: var(--primary-color);
  font-weight: bold;
}

.position-period {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

.position-content {
  padding: 20rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  margin-bottom:20rpx;
}

.summary-label {
  color: #666;
}

.summary-value {
  font-weight: bold;
}

.badges-container {
  margin-top: 20rpx;
}

.badge {
  display: inline-block;
  padding: 0px 10rpx;
  line-height: 40rpx;
  border-radius: 15rpx;
  background-color: #e7e2fa;
  color: var(--primary-color);
  font-size: 22rpx;
  margin-right: 10rpx;
  margin-bottom: 10px;
}