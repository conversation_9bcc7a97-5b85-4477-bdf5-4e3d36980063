/**
 * 授权行为 - 提供所有页面公共的授权检查、弹窗和导航功能
 */
const app = getApp()
import {getPositions,getLevels,getUserInfo,checkEnterpriseBinding} from '../api/user'
import request from '../utils/request';

module.exports = Behavior({
  data: {
    isAuthorized: false,  // 用户是否已授权
    isIdentityVerified: false, // 用户是否已认证
    showAuthPopup: false, // 是否显示授权弹窗
    showAuthModal: false, // 是否显示身份认证弹窗
    showSuccessModal: false, // 是否显示认证成功弹窗
    nextPath: '',         // 授权后需要跳转的路径
    authRequiredPages: [  // 需要授权的页面路径
      '/pages/practice/list',
      '/pages/promotion/list',
      '/pages/profile/index'
    ],
    // 导航栏相关数据
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 0, // 导航栏高度
    totalNavHeight: 0, // 总导航高度
    isIOS: false, // 是否为iOS设备
    chatPaddingTop: 0, // 聊天区域的内边距
    userInfo: {
      // avatar: '',
      realName: '用户名',
    },
    currentPosition:'',
    currentLevel:'',
    companyName:'',
    allTabs: [
      {
        id:1,
        pagePath: "/pages/home/<USER>",
        text: "首页",
        icon: "icon-shouye",
        iconPath: "/static/images/home.png",
        selectedIconPath: "/static/images/home-select.png",
        fontSize:"50rpx;"
      },
      {
        id:2,
        pagePath: "/pages/practice/list",
        text: "练习",
        icon: "icon-book-open",
        iconPath: "/static/images/practice.png",
        selectedIconPath: "/static/images/practice-select.png",
        fontSize:"50rpx"
      },
      {
        id:3,
        pagePath: "/pages/promotion/list",
        text: "考试",
        icon: "icon-trophy",
        iconPath: "/static/images/exam.png",
        selectedIconPath: "/static/images/exam-select.png", 
        fontSize:'55rpx'
      },
      {
        id:4,
        pagePath: "/pages/profile/index",
        text: "我的",
        icon: "icon-user",
        iconPath: "/static/images/user.png",
        selectedIconPath: "/static/images/user-select.png",
        fontSize:"70rpx"
      }
    ],
  },

  attached() {
    // 页面加载时检查授权状态
    // this.checkAuthStatus()
    
    // 页面加载时获取导航栏高度
    this.getNavHeight()
  },

  methods: {
    // 显示认证成功
    async onVerifySuccess(){
      // this.setData({
      //   showSuccessModal: true,
      // })
      setTimeout(async () => {
        // this.setData({
        //   showSuccessModal: false,
        // })
        // 更新用户信息
        await this.updateUserInfo()
        // 跳转到下一个页面
        const currentPage = getCurrentPages().pop();
        if (currentPage && ('/' + currentPage.route) === this.data.nextPath) {
          currentPage.onLoad();
          this.resetTabbar()
          this.updateTabBarAfterAuth()

        }else if (this.data.nextPath) {
          // navigateto 失败使用switchTab
          wx.navigateTo({
            url: this.data.nextPath,
            fail: (err) => {
              console.error('导航失败:', err)
              wx.switchTab({
                url: this.data.nextPath,
                success: (res) => {
                  // 重新定位tabbar
                  this.resetTabbar()
                this.updateTabBarAfterAuth()
                }
              })
            }
          });
        }

      }, 2000)
    },
    // 更新用户信息
    updateUserInfo(){
      // 获取用户基本信息
      return getUserInfo( wx.getStorageSync('openId'))
        .then(result => {
          // 检查result是否存在且有效
          if (result && result.user) {
            let avatar = result.user.avatar ? request.config.imgURL + result.user.avatar : '';

            this.setData({
              userInfo: {
                ...result.user,
                avatar: avatar,
                realName: result.user.realName || result.user.nickname || '用户名',
              },
              currentPosition: result.user.positionName || '',
              currentLevel: result.user.levelName || '无',
              companyName: result.user.companyName || ''
            });
            // 保存用户信息到本地
            wx.setStorageSync('userInfo', result.user);

            // 只有当adminAccessUrl存在时才保存
            if (result.user.adminAccessUrl) {
              wx.setStorageSync('adminAccessUrl', result.user.adminAccessUrl);
            }

          }
        })
        .catch(error => {
          console.error('获取用户信息失败：', error);
        });
  
    },
    resetTabbar(){
      this.data.allTabs.forEach((item,index)=>{

        if(item.pagePath===this.data.nextPath){
          wx.setStorageSync('selectedTab',index)
        }
      })
    },
    
/**
 * 授权状态变更后更新tabBar
 * @param {Boolean} isAuthorized - 授权状态，默认为true
 */
    updateTabBarAfterAuth (){
      try {
        // const app = getApp();
        // 获取当前页面
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        // 如果当前页面有tabBar实例，直接调用其checkIdentityStatus方法
        if (currentPage && currentPage.getTabBar) {
          const tabBar = currentPage.getTabBar();
          if (tabBar && tabBar.checkStatus) {
            tabBar.checkStatus();
          }
        }
      } catch (error) {
        console.error('更新tabBar失败', error);
      }
    },
    clearAuthStatus(){
      // 页面卸载时清除授权状态 会有闪烁问题
      this.setData({
        showAuthPopup: false,
        showAuthModal: false,
      })
    },
    /**
     * 检查用户授权状态
     */
    async checkAuthStatus() {
        const isAuthorized = wx.getStorageSync('isAuthorized') || false
        this.setData({
          isAuthorized
        })
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        const currentPath = '/' + currentPage.route
        // 如果没有授权，判断当前页面是否需要授权
        if (!isAuthorized) {
          if (pages.length === 0) return false
          // 判断当前页面是否需要授权
          if (this.data.authRequiredPages.includes(currentPath)) {
              this.setData({
                showAuthPopup: true,
                showAuthModal: false,
                nextPath: currentPath
              })
            }
            return false
        }else{
          this.setData({
            nextPath: currentPath
          })
          await this.checkIdentityStatus()
          return true
        }
    },
    /**
     * 检查用户身份认证状态
     */
    async checkIdentityStatus(){
        await this.checkBinding()

       // 如果已授权，判断用户是否已认证
        const isIdentityVerified = wx.getStorageSync('isIdentityVerified') || false
        this.setData({
          isIdentityVerified
        })
        if (!isIdentityVerified) {
          this.setData({
            showAuthPopup: false,
            // showAuthModal: true,
          })
        }
    },
  
    /**
     * 导航到需要授权的页面
     * @param {String} targetPath 目标页面路径
     */
    async navigateToAuthRequiredPage(targetPath) {
      let isIdentityVerified= await this.checkBinding()
        if (wx.getStorageSync('isAuthorized')&&isIdentityVerified) {
          this.navigateTo(targetPath)
        } else if(!wx.getStorageSync('isAuthorized')) {

          // 未授权，显示授权弹窗
          this.setData({
            showAuthPopup: true,
            showAuthModal: false,
            nextPath: targetPath
          })
        }else{
           // 未授权，显示授权弹窗
           this.setData({
            showAuthPopup: false,
            // showAuthModal: true,
            nextPath: targetPath
          })
          // 跳转到申请加入企业
          this.goToJoin()
         

        }
    },
    async goToJoin(){
      await this.updateUserInfo()

      // 获取用户信息，并检查是否存在
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.auditStatus) {
        if(userInfo.auditStatus == '审核中'){
          wx.showToast({
            title: '审核中请稍后...',
            icon: 'none'
          })
        } else if(userInfo.auditStatus == '通过'){
          this.onVerifySuccess()
        } else {
          // 其他状态（如审核失败、未申请等）跳转到申请页面
          wx.navigateTo({
            url: '/pages/profile/join-enterprise/join-enterprise',
          })
        }
      } else {
        // 用户信息不存在或缺少状态字段，跳转到申请页面
        wx.navigateTo({
          url: '/pages/profile/join-enterprise/join-enterprise',
        })
      }
    },
      /**
     * 导航到练习详情
     */
    navigateToPractice: function(course) {
      // 检查权限并跳转到需授权页面
      // 使用微信小程序的json格式传递参数
      const params = {
        fileId: course.examSubject,
        title: course.examSubjectName,
        status: course.status,
        positionLevel: course.positionLevel,
        positionName: course.positionName
      } 
      this.navigateToAuthRequiredPage(`/pages/practice/detail?info=${JSON.stringify(params)}`);
    },

    navigateTo(targetPath){
       // 已授权，直接导航
       wx.navigateTo({
        url: targetPath,
        fail: (err) => {
          console.error('导航失败:', err)
          // 可能是tabbar页面，尝试switchTab
          if (err.errMsg.indexOf('navigateTo:fail') !== -1) {
            wx.switchTab({
              url: targetPath,
              fail: (switchErr) => {
                console.error('切换标签页失败:', switchErr)
                wx.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
    },

    /**
     * 授权成功的回调函数
     * @param {Object} e 授权成功事件对象
     */
    onAuthSuccess(e) {
      const authData = e.detail || {}
      this.setData({
        isAuthorized: true,
      })
      // 更新用户信息
      // this.updateUserInfo()
      // this.checkIdentityStatus()
      // 跳转到企业申请
      this.goToJoin()
    },

      // 身份认证弹窗关闭回调
  onVerifyClose(e) {
    this.setData({
      [e.detail.type]: false
    });
    this.drawRadarChart()
  },

    /**
     * 关闭授权\认证弹窗的回调函数
     */
    onAuthClose(e) {
      console.log('onAuthClose',e)
      this.setData({
        [e.detail.type]: false
      })
    },

    /**
     * 获取导航栏高度
     * 此方法会计算并设置导航栏相关高度数据
     */
    getNavHeight() {
      try {
        const systemInfo = wx.getSystemInfoSync();
        const statusBarHeight = systemInfo.statusBarHeight;
        const isIOS = systemInfo.system.toLowerCase().includes('ios');

        // 根据不同系统计算导航栏高度
        let navBarHeight = 44; // 默认高度
        if (isIOS) {
          // 针对iOS的刘海屏做特殊处理
          const isIphoneX = /iPhone X|iPhone 11|iPhone 12|iPhone 13|iPhone 14|iPhone 15/.test(systemInfo.model);
          navBarHeight = isIphoneX ? 50 : 50; // iPhone X及以上机型导航栏更高
        } else {
          navBarHeight = 50; // 安卓导航栏一般更高
        }

        // 计算总导航高度(状态栏 + 导航栏)
        // const totalNavHeight = statusBarHeight + navBarHeight;
        const totalNavHeight =  navBarHeight+statusBarHeight;

        // 设置导航参数到data
        this.setData({
          statusBarHeight: statusBarHeight,
          navBarHeight: navBarHeight,
          totalNavHeight: totalNavHeight,
          isIOS: isIOS
        });


        // 延迟执行，确保DOM已更新
        setTimeout(() => {
          // 调用更新内边距方法，如果页面有实现此方法
          if (typeof this.updateChatPadding === 'function') {
            this.updateChatPadding();
          }
        }, 100);

      } catch (e) {
        console.error('获取导航栏高度失败', e);
      }
    },

    /**
     * 更新聊天区域内边距的默认实现
     * 各页面可以根据需要重写此方法
     */
    updateChatPadding() {
      const query = wx.createSelectorQuery();
      query.select('.custom-nav').boundingClientRect(rect => {
        if (rect) {
          this.setData({
            chatPaddingTop: rect.height
          });
        }
      }).exec();
    },
     // 获取用户信息
    // getCurrentUserInfo(){
    //   return Promise.all([
    //     this.getAllPositions(),
    //     this.getAllLevels()
    //   ])
    //   .then(result => {
    //     const previewLevel=wx.getStorageSync('userInfo').previewLevel
    //     const previewPosition=wx.getStorageSync('currentPosition')
    //     const currentPosition = result[0].rows.find(position => position.id === previewPosition);  
    //     const currentLevel = result[1].find(level => level.id === previewLevel);
    //     wx.setStorageSync('currentPositionName',currentPosition.name)
    //     wx.setStorageSync('currentLevelName',currentLevel.name)
    //     return {
    //       currentPosition: currentPosition.name,
    //       currentLevel: currentLevel.name
    //     }
    //   })
    //   .catch(error => {
    //     console.error('获取用户信息失败:', error);
    //     return {
    //       currentPosition: '',
    //       currentLevel: ''
    //     }
    //   })
      
    // },
    async checkBinding(){
      let isIdentityVerified=wx.getStorageSync('isIdentityVerified')
      // 未通过的时候调用接口重新获取状态
      if(!isIdentityVerified){
        const res = await checkEnterpriseBinding({openId:wx.getStorageSync('openId')});
        if (res && typeof res.isBound !== 'undefined') {
          wx.setStorageSync('isIdentityVerified', res.isBound);
        } else {
          // 设置默认值为false，表示未绑定
          wx.setStorageSync('isIdentityVerified', false);
        }
        isIdentityVerified=res.isBound
      }
      
      return isIdentityVerified
    },
    // 获取所有岗位
    async getAllPositions(){
      try {
        let isIdentityVerified= await this.checkBinding()
        if(isIdentityVerified){
          return new Promise((resolve, reject)=>{
            getPositions()
              .then(result => {
                if (result) {
                  resolve(result);
                } else {
                  resolve([]);
                }
              })
              .catch(error => {
                reject(error);
              });
          });
        } else {
          return Promise.resolve([]);
        }
      } catch (error) {
        console.error('检查企业绑定状态失败:', error);
        return Promise.resolve([]);
      }
    },
    // 获取所有级别
    getAllLevels(){
      return new Promise((resolve, reject)=>{
        getLevels()
          .then(result => {
            if (result) {
              resolve(result);
            } else {
              resolve([]);
            }
          })
          .catch(error => {
            console.error('获取级别列表失败:', error);
            reject(error);
          });
      });
    },
    // 绘制雷达图
    drawRadarChart: function() {
      const query = wx.createSelectorQuery();
      query.select('#radar-container1').boundingClientRect((rect) => {
        if (!rect) {
          console.error('无法获取雷达图容器尺寸');
          return;
        }
      
        
      const ctx = wx.createCanvasContext('radarCanvas');
      const data = this.data.radarData;
      const categories = data.categories;
      const series = data.series;
  
         // 获取实际容器尺寸
         const containerWidth = rect.width || 300; // 默认宽度300
         const containerHeight = rect.height || 300; // 默认高度300
         
      const centerX = containerWidth / 2;
      const centerY = containerHeight / 2;
      
      const radius = 90;  // 雷达图半径
      const angleStep = 2 * Math.PI / categories.length; // 每个维度之间的角度
  
      // 绘制雷达图网格
      ctx.setLineWidth(1);
      ctx.setStrokeStyle('#e4e4e4');
  
      // 绘制环形网格
      for (let r = 0.2; r <= 1; r += 0.2) {
        ctx.beginPath();
        for (let i = 0; i < categories.length; i++) {
          const angle = i * angleStep - Math.PI / 2; // 从上方开始
          const x = centerX + radius * r * Math.cos(angle);
          const y = centerY + radius * r * Math.sin(angle);
  
          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
        ctx.closePath();
        ctx.stroke();
      }
  
      // 绘制连接中心点的线
      for (let i = 0; i < categories.length; i++) {
        const angle = i * angleStep - Math.PI / 2; // 从上方开始
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
  
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.lineTo(x, y);
        ctx.stroke();
  
        // 绘制维度标签
        const labelX = centerX + (radius + 25) * Math.cos(angle);
        const labelY = centerY + (radius + 15) * Math.sin(angle);
  
        ctx.setFontSize(10);
        ctx.setFillStyle('#000000'); // 设置文字颜色为黑色
        ctx.setTextAlign('center');
        ctx.setTextBaseline('middle');
        ctx.fillText(categories[i], labelX, labelY);
      }
  
      // 绘制数据区域
      const colors = ['#a18cd1', '#8fc1ff']; // 当前水平和上次评估的颜色
      const colorsBg = ['rgba(161, 140, 209, 0.3)', 'rgba(56, 163, 209, 0.3)']; // 当前水平和上次评估的颜色
  
      for (let s = 0; s < series.length; s++) {
        const seriesData = series[s].data;
  
        // 绘制数据连线
        ctx.beginPath();
        for (let i = 0; i < categories.length; i++) {
          const value = seriesData[i] / 100; // 归一化值
          const angle = i * angleStep - Math.PI / 2; // 从上方开始
          const x = centerX + radius * value * Math.cos(angle);
          const y = centerY + radius * value * Math.sin(angle);
  
          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
        ctx.closePath();
  
        // 填充区域
        ctx.setFillStyle(colorsBg[s]); // 添加透明度
        ctx.fill();
  
        // 描边
        ctx.setLineWidth(1);
        ctx.setStrokeStyle(colors[s]);
        ctx.stroke();
  
        // 绘制数据点
        for (let i = 0; i < categories.length; i++) {
          const value = seriesData[i] / 100; // 归一化值
          const angle = i * angleStep - Math.PI / 2; // 从上方开始
          const x = centerX + radius * value * Math.cos(angle);
          const y = centerY + radius * value * Math.sin(angle);
  
          ctx.beginPath();
          ctx.arc(x, y, 2, 0, 2 * Math.PI);
          // ctx.setFillStyle('#fe3');
          // ctx.fill();
          ctx.setLineWidth(1);
          ctx.setStrokeStyle(colors[s]);
          ctx.stroke();
        }
      }
  
      ctx.draw();
      }).exec();
    },
    
  },
 
  onResize(size){
    this.getNavHeight();
  },
    
}) 