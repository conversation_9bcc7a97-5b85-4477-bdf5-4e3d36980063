<view>
	<view class="container">
        <view class="profile-card">
            <view class="profile-banner"></view>
            <view class="profile-content">
                <view class="profile-header">
                    <view class="profile-avatar">张</view>
                    <view class="profile-info">
                        <view class="profile-name">张小明</view>
                        <view class="profile-detail-group">
                            <view class="profile-detail">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="16" height="16">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                                男 | 28岁
                            </view>
                            <view class="profile-detail">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="16" height="16">
                                    <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                                    <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                                </svg>
                                餐饮服务
                            </view>
                        </view>
                    </view>
                </view>

                <view class="profile-stats">
                    <view class="stat-block">
                        <view class="stat-label">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="14" height="14">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            工作年限
                        </view>
                        <view class="stat-value">3年4个月</view>
                    </view>
                    <view class="stat-block">
                        <view class="stat-label">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="14" height="14">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="16" y1="2" x2="16" y2="6"></line>
                                <line x1="8" y1="2" x2="8" y2="6"></line>
                                <line x1="3" y1="10" x2="21" y2="10"></line>
                            </svg>
                            在线时长
                        </view>
                        <view class="stat-value">1年5个月</view>
                    </view>
                </view>
                
                <view class="profile-current">
                    <view class="profile-current-title">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="14" height="14">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>
                        当前任职
                    </view>
                    <view class="profile-current-company">阿依来餐饮集团</view>
                    <view class="profile-current-position">
                        领班
                        <span class="position-badge">P5</span>
                    </view>
                </view>
            </view>
        </view>

        <view class="card">
            <view class="section-title">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="18" height="18">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                    <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                    <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
                企业履历记录
            </view>

            <!-- 企业1 -->
            <view class="company-card" onclick="location.href='company_detail.html?id=1'">
                <view class="company-header">
                    <view class="company-name">阿依来餐饮集团</view>
                    <view class="company-period">2021.06 - 至今</view>
                </view>
                
                <view class="company-stats">
                    <view class="stat-item">
                        <view class="stat-value">153</view>
                        <view class="stat-label">总练习时长(小时)</view>
                    </view>
                    <view class="stat-item">
                        <view class="stat-value">24</view>
                        <view class="stat-label">通过考试</view>
                    </view>
                    <view class="stat-item">
                        <view class="stat-value">6</view>
                        <view class="stat-label">获得证书</view>
                    </view>
                </view>

                <view class="company-footer">
                    <view class="company-positions">
                        岗位：服务员 → 领班
                    </view>
                    <view class="company-arrow">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="16" height="16">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </view>
                </view>
            </view>

            <!-- 企业2 -->
            <view class="company-card" onclick="location.href='company_detail.html?id=2'">
                <view class="company-header">
                    <view class="company-name">幸福食品有限公司</view>
                    <view class="company-period">2020.02 - 2021.05</view>
                </view>
                
                <view class="company-stats">
                    <view class="stat-item">
                        <view class="stat-value">52</view>
                        <view class="stat-label">总练习时长(小时)</view>
                    </view>
                    <view class="stat-item">
                        <view class="stat-value">8</view>
                        <view class="stat-label">通过考试</view>
                    </view>
                    <view class="stat-item">
                        <view class="stat-value">2</view>
                        <view class="stat-label">获得证书</view>
                    </view>
                </view>

                <view class="company-footer">
                    <view class="company-positions">
                        岗位：厨师助理
                    </view>
                    <view class="company-arrow">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="16" height="16">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </view>
                </view>
            </view>
        </view>
    </view>
</view>