# 工具函数目录

本目录包含项目中使用的各种工具函数。

## dateFormat.js

提供跨平台兼容的日期格式化工具，特别解决了安卓设备上 `toLocaleTimeString` 的兼容性问题。

### 主要函数:

1. `getFormattedTime(date)` - 返回格式为 "HH:MM" 的时间字符串
2. `getFormattedTimeWithSeconds(date)` - 返回格式为 "HH:MM:SS" 的时间字符串
3. `getFormattedDateTime(date)` - 返回格式为 "YYYY-MM-DD HH:MM:SS" 的完整日期时间字符串

### 使用示例:

```javascript
import { getFormattedTime, getFormattedTimeWithSeconds, getFormattedDateTime } from '../../utils/dateFormat';

// 获取当前时间 (HH:MM)
const timeStr = getFormattedTime();  // 例如 "14:30"

// 获取当前时间，包含秒 (HH:MM:SS)
const timeWithSecondsStr = getFormattedTimeWithSeconds();  // 例如 "14:30:45"

// 获取完整日期时间 (YYYY-MM-DD HH:MM:SS)
const dateTimeStr = getFormattedDateTime();  // 例如 "2023-10-15 14:30:45"

// 格式化特定日期
const specificDate = new Date('2023-10-15T14:30:45');
const formattedTime = getFormattedTime(specificDate);  // "14:30"
```

这些函数替代了原先使用的 `toLocaleTimeString` 方法，确保在所有平台（包括安卓设备）上都能正确显示日期时间。 