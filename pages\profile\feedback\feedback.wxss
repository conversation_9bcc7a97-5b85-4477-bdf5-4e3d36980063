/* pages/profile/feedback.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.feedback-container {
  padding: 32rpx;
}

.feedback-intro {
  background: #fff;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  font-size: 28rpx;
  color: #666;
}

.feedback-form {
  background: #fff;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
}

.feedback-type {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.type-tag {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  background-color: #f8f9fa;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
}

.type-tag.active {
  background: linear-gradient(135deg, rgba(161, 140, 209, 0.2) 0%, rgba(251, 194, 235, 0.2) 100%);
  border-color: #a18cd1;
  color: #a18cd1;
}

.form-textarea {
  width: 100%;
  height: 240rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-size: 28rpx;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

.word-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 8rpx;
}

.image-upload {
  margin-top: 16rpx;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.preview-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
}

.preview-item image {
  width: 100%;
  height: 100%;
}

.preview-remove {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
}

.add-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  border: 1rpx dashed rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-image .iconfont {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.add-image .add-text {
  font-size: 24rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-size: 28rpx;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

.submit-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #fff;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 30rpx rgba(161, 140, 209, 0.4);
}

.submit-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 6rpx 20rpx rgba(161, 140, 209, 0.3);
}

.submit-button[disabled] {
  opacity: 0.5;
  transform: none;
  box-shadow: none;
}

.history-feedback {
  background: #fff;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.history-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.history-title .history-count {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

.history-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.history-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.history-type {
  font-size: 26rpx;
  color: #a18cd1;
  background: rgba(161, 140, 209, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
}

.history-status {
  font-size: 24rpx;
}

.history-status.resolved {
  color: #22c55e;
}

.history-status.pending {
  color: #eab308;
}

.history-content {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.history-date {
  font-size: 24rpx;
  color: #999;
}

.history-reply {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 16rpx;
  margin-top: 16rpx;
}

.history-reply .reply-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.history-reply .reply-content {
  font-size: 26rpx;
  color: #333;
}

/* 历史反馈图片列表 */
.history-images {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin: 16rpx 0;
}

.history-image-item {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.history-image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 加载更多提示 */
.load-more {
  text-align: center;
  padding: 24rpx 0 0;
  color: #999;
  font-size: 24rpx;
}

/* 空数据提示 */
.empty-history {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}
