/**
 * 徽章相关API的模拟数据
 */

// 徽章统计数据
const badgeStats = {
  total: 36,
  acquired: 15,
  completionRate: 41.7, // 百分比
  rankInClass: 5,
  totalClassmates: 32,
  lastAcquired: {
    id: 204,
    name: '厨艺精通',
    acquiredDate: '2024-02-15',
    icon: '/static/images/badges/cooking-master.png',
  }
};

// 徽章类别
const badgeCategories = [
  {
    id: 1,
    name: '学习成就',
    total: 9,
    acquired: 5,
    icon: '/static/icons/badge-learning.png',
    colorClass: 'learning'
  },
  {
    id: 2,
    name: '厨艺技能',
    total: 12, 
    acquired: 4,
    icon: '/static/icons/badge-cooking.png',
    colorClass: 'cooking'
  },
  {
    id: 3,
    name: '服务技巧',
    total: 8,
    acquired: 3,
    icon: '/static/icons/badge-service.png',
    colorClass: 'service'
  },
  {
    id: 4,
    name: '管理能力',
    total: 7,
    acquired: 3,
    icon: '/static/icons/badge-management.png',
    colorClass: 'management'
  }
];

// 徽章等级定义
const badgeLevels = {
  bronze: {
    name: '铜牌',
    icon: '/static/icons/badge-bronze.png',
    colorClass: 'bronze'
  },
  silver: {
    name: '银牌',
    icon: '/static/icons/badge-silver.png',
    colorClass: 'silver'
  },
  gold: {
    name: '金牌',
    icon: '/static/icons/badge-gold.png',
    colorClass: 'gold'
  }
};

// 徽章列表数据
const badgeList = [
  // 学习成就徽章
  {
    id: 101,
    categoryId: 1,
    name: '知识探索者',
    description: '完成50个学习模块',
    level: 'bronze',
    status: 'acquired', // acquired, in-progress, locked
    progress: 100,
    acquiredDate: '2023-08-10',
    icon: '/static/images/badges/knowledge-explorer-bronze.png',
    requirements: [
      '完成至少50个学习模块',
      '平均测验得分达到70%以上'
    ],
    benefits: [
      '学习币+50',
      '解锁进阶学习资源'
    ]
  },
  {
    id: 102,
    categoryId: 1,
    name: '知识探索者',
    description: '完成100个学习模块',
    level: 'silver',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-09-20',
    icon: '/static/images/badges/knowledge-explorer-silver.png',
    requirements: [
      '完成至少100个学习模块',
      '平均测验得分达到75%以上'
    ],
    benefits: [
      '学习币+100',
      '解锁专家讲座视频'
    ]
  },
  {
    id: 103,
    categoryId: 1,
    name: '知识探索者',
    description: '完成200个学习模块',
    level: 'gold',
    status: 'in-progress',
    progress: 72,
    acquiredDate: null,
    icon: '/static/images/badges/knowledge-explorer-gold.png',
    requirements: [
      '完成至少200个学习模块',
      '平均测验得分达到80%以上'
    ],
    benefits: [
      '学习币+200',
      '获得专家一对一指导机会'
    ]
  },
  {
    id: 104,
    categoryId: 1,
    name: '学习连续者',
    description: '连续7天学习',
    level: 'bronze',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-08-15',
    icon: '/static/images/badges/learning-streak-bronze.png',
    requirements: [
      '连续7天每天至少完成1个学习模块'
    ],
    benefits: [
      '学习币+30',
      '获得额外经验值加成'
    ]
  },
  {
    id: 105,
    categoryId: 1,
    name: '学习连续者',
    description: '连续30天学习',
    level: 'silver',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-09-12',
    icon: '/static/images/badges/learning-streak-silver.png',
    requirements: [
      '连续30天每天至少完成1个学习模块'
    ],
    benefits: [
      '学习币+100',
      '获得限定头像框'
    ]
  },
  {
    id: 106,
    categoryId: 1,
    name: '学习连续者',
    description: '连续100天学习',
    level: 'gold',
    status: 'in-progress',
    progress: 45,
    acquiredDate: null,
    icon: '/static/images/badges/learning-streak-gold.png',
    requirements: [
      '连续100天每天至少完成1个学习模块'
    ],
    benefits: [
      '学习币+300',
      '获得尊享会员1个月'
    ]
  },
  {
    id: 107,
    categoryId: 1,
    name: '测验高手',
    description: '完成50个测验',
    level: 'bronze',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-10-05',
    icon: '/static/images/badges/quiz-master-bronze.png',
    requirements: [
      '完成至少50个测验',
      '平均得分达到80%以上'
    ],
    benefits: [
      '学习币+50',
      '获得测验提示卡5张'
    ]
  },
  {
    id: 108,
    categoryId: 1,
    name: '测验高手',
    description: '完成100个测验',
    level: 'silver',
    status: 'in-progress',
    progress: 68,
    acquiredDate: null,
    icon: '/static/images/badges/quiz-master-silver.png',
    requirements: [
      '完成至少100个测验',
      '平均得分达到85%以上'
    ],
    benefits: [
      '学习币+150',
      '获得测验提示卡15张'
    ]
  },
  {
    id: 109,
    categoryId: 1,
    name: '测验高手',
    description: '完成200个测验',
    level: 'gold',
    status: 'locked',
    progress: 0,
    acquiredDate: null,
    icon: '/static/images/badges/quiz-master-gold.png',
    requirements: [
      '完成至少200个测验',
      '平均得分达到90%以上'
    ],
    benefits: [
      '学习币+300',
      '获得测验提示卡无限使用(30天)'
    ]
  },
  
  // 厨艺技能徽章
  {
    id: 201,
    categoryId: 2,
    name: '刀工入门',
    description: '掌握基础刀工技巧',
    level: 'bronze',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-08-25',
    icon: '/static/images/badges/knife-skills-bronze.png',
    requirements: [
      '完成刀工基础练习',
      '通过基础刀工考核'
    ],
    benefits: [
      '解锁进阶刀工视频教程',
      '获得个人主页刀工技能展示'
    ]
  },
  {
    id: 202,
    categoryId: 2,
    name: '刀工精通',
    description: '掌握进阶刀工技巧',
    level: 'silver',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-11-10',
    icon: '/static/images/badges/knife-skills-silver.png',
    requirements: [
      '完成刀工进阶练习',
      '通过进阶刀工考核',
      '完成3种复杂切法的实践'
    ],
    benefits: [
      '解锁高级刀工视频教程',
      '获得专业厨师刀具优惠券'
    ]
  },
  {
    id: 203,
    categoryId: 2,
    name: '刀工大师',
    description: '掌握高级刀工技巧',
    level: 'gold',
    status: 'in-progress',
    progress: 35,
    acquiredDate: null,
    icon: '/static/images/badges/knife-skills-gold.png',
    requirements: [
      '完成刀工高级练习',
      '通过高级刀工考核',
      '完成5种艺术切法的实践'
    ],
    benefits: [
      '获得刀工大师认证',
      '有机会参与平台刀工教学视频录制'
    ]
  },
  {
    id: 204,
    categoryId: 2,
    name: '厨艺精通',
    description: '掌握多种烹饪技法',
    level: 'bronze',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2024-02-15',
    icon: '/static/images/badges/cooking-master-bronze.png',
    requirements: [
      '熟练掌握至少5种烹饪技法',
      '完成基础烹饪技法考核'
    ],
    benefits: [
      '解锁特色菜谱',
      '获得烹饪技法徽章展示'
    ]
  },
  {
    id: 205,
    categoryId: 2,
    name: '厨艺精通',
    description: '掌握多种高级烹饪技法',
    level: 'silver',
    status: 'in-progress',
    progress: 50,
    acquiredDate: null,
    icon: '/static/images/badges/cooking-master-silver.png',
    requirements: [
      '熟练掌握至少10种烹饪技法',
      '完成进阶烹饪技法考核'
    ],
    benefits: [
      '解锁高级菜谱',
      '获得烹饪大师认证初级资格'
    ]
  },
  {
    id: 206,
    categoryId: 2,
    name: '厨艺精通',
    description: '掌握全面烹饪技法体系',
    level: 'gold',
    status: 'locked',
    progress: 0,
    acquiredDate: null,
    icon: '/static/images/badges/cooking-master-gold.png',
    requirements: [
      '熟练掌握至少15种烹饪技法',
      '完成高级烹饪技法考核',
      '提交个人创新菜品一份'
    ],
    benefits: [
      '获得烹饪大师称号',
      '优先获得线下活动参与资格'
    ]
  },
  
  // 服务技巧徽章  
  {
    id: 301,
    categoryId: 3,
    name: '服务礼仪',
    description: '掌握基本服务礼仪',
    level: 'bronze',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-09-05',
    icon: '/static/images/badges/service-etiquette-bronze.png',
    requirements: [
      '完成服务礼仪基础练习',
      '通过礼仪实操测试'
    ],
    benefits: [
      '获得服务礼仪认证标识',
      '解锁进阶服务技巧练习'
    ]
  },
  {
    id: 302,
    categoryId: 3,
    name: '服务礼仪',
    description: '掌握专业服务礼仪',
    level: 'silver',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-12-20',
    icon: '/static/images/badges/service-etiquette-silver.png',
    requirements: [
      '完成服务礼仪进阶练习',
      '通过模拟服务场景测试',
      '客户满意度评分达到4.5/5以上'
    ],
    benefits: [
      '获得高级服务礼仪认证',
      '解锁专属服务技巧视频'
    ]
  },
  {
    id: 303,
    categoryId: 3,
    name: '服务礼仪',
    description: '成为服务礼仪专家',
    level: 'gold',
    status: 'locked',
    progress: 0,
    acquiredDate: null,
    icon: '/static/images/badges/service-etiquette-gold.png',
    requirements: [
      '完成服务礼仪专家练习',
      '通过综合服务技能考核',
      '线下实操评分达到95分以上'
    ],
    benefits: [
      '获得服务礼仪培训师资格',
      '有机会参与平台服务标准制定'
    ]
  },
  {
    id: 304,
    categoryId: 3,
    name: '顾客关系',
    description: '基础顾客关系管理',
    level: 'bronze',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-10-15',
    icon: '/static/images/badges/customer-relation-bronze.png',
    requirements: [
      '完成顾客关系入门练习',
      '通过基础客户沟通考核'
    ],
    benefits: [
      '解锁顾客沟通技巧教程',
      '获得客户关系管理初级认证'
    ]
  },
  {
    id: 305,
    categoryId: 3,
    name: '顾客关系',
    description: '进阶顾客关系管理',
    level: 'silver',
    status: 'in-progress',
    progress: 60,
    acquiredDate: null,
    icon: '/static/images/badges/customer-relation-silver.png',
    requirements: [
      '完成顾客关系进阶练习',
      '通过客诉处理模拟考核',
      '客户满意度回访评分达到4.7/5以上'
    ],
    benefits: [
      '获得客户关系管理中级认证',
      '解锁VIP客户服务指南'
    ]
  },
  
  // 管理能力徽章
  {
    id: 401,
    categoryId: 4,
    name: '团队管理',
    description: '基础团队管理能力',
    level: 'bronze',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-11-15',
    icon: '/static/images/badges/team-management-bronze.png',
    requirements: [
      '完成团队管理基础练习',
      '提交团队管理案例分析',
      '通过团队管理基础测试'
    ],
    benefits: [
      '获得团队管理初级认证',
      '解锁团队建设资源包'
    ]
  },
  {
    id: 402,
    categoryId: 4,
    name: '团队管理',
    description: '进阶团队管理能力',
    level: 'silver',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2024-01-25',
    icon: '/static/images/badges/team-management-silver.png',
    requirements: [
      '完成团队管理进阶练习',
      '通过团队冲突解决测试',
      '完成团队建设方案设计'
    ],
    benefits: [
      '获得团队管理中级认证',
      '解锁高效团队构建指南'
    ]
  },
  {
    id: 403,
    categoryId: 4,
    name: '团队管理',
    description: '高级团队管理能力',
    level: 'gold',
    status: 'in-progress',
    progress: 25,
    acquiredDate: null,
    icon: '/static/images/badges/team-management-gold.png',
    requirements: [
      '完成团队管理高级练习',
      '提交完整团队发展规划',
      '通过团队绩效管理测试',
      '具备2年以上团队管理经验'
    ],
    benefits: [
      '获得团队管理高级认证',
      '有机会参与企业管理咨询项目'
    ]
  },
  {
    id: 404,
    categoryId: 4,
    name: '成本控制',
    description: '基础成本控制能力',
    level: 'bronze',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2023-12-10',
    icon: '/static/images/badges/cost-control-bronze.png',
    requirements: [
      '完成成本控制基础练习',
      '通过成本分析测试'
    ],
    benefits: [
      '获得成本控制初级认证',
      '解锁成本管理工具模板'
    ]
  },
  {
    id: 405,
    categoryId: 4,
    name: '成本控制',
    description: '进阶成本控制能力',
    level: 'silver',
    status: 'acquired',
    progress: 100,
    acquiredDate: '2024-02-05',
    icon: '/static/images/badges/cost-control-silver.png',
    requirements: [
      '完成成本控制进阶练习',
      '提交成本优化方案',
      '通过成本控制实例分析'
    ],
    benefits: [
      '获得成本控制中级认证',
      '解锁高级成本核算模板'
    ]
  }
];

// API调用处理函数
const badgeMock = {
  // 获取徽章统计数据
  'GET /api/badges/stats': (data) => {
    return {
      code: 0,
      message: 'success',
      data: badgeStats
    };
  },
  
  // 获取徽章类别列表
  'GET /api/badges/categories': (data) => {
    return {
      code: 0,
      message: 'success',
      data: badgeCategories
    };
  },
  
  // 获取徽章列表（支持按类别筛选）
  'GET /api/badges/list': (data) => {
    let result = [...badgeList];
    
    // 按类别筛选
    if (data.categoryId) {
      result = result.filter(item => item.categoryId === parseInt(data.categoryId));
    }
    
    // 按等级筛选
    if (data.level) {
      result = result.filter(item => item.level === data.level);
    }
    
    // 按状态筛选
    if (data.status) {
      result = result.filter(item => item.status === data.status);
    }
    
    // 按搜索词筛选
    if (data.keyword) {
      const keyword = data.keyword.toLowerCase();
      result = result.filter(item => 
        item.name.toLowerCase().includes(keyword) || 
        item.description.toLowerCase().includes(keyword)
      );
    }
    
    return {
      code: 0,
      message: 'success',
      data: result
    };
  },
  
  // 获取徽章详情
  'GET /api/badges/detail': (data) => {
    const badge = badgeList.find(item => item.id === parseInt(data.id));
    
    if (badge) {
      return {
        code: 0,
        message: 'success',
        data: badge
      };
    } else {
      return {
        code: 404,
        message: '徽章不存在',
        data: null
      };
    }
  }
};

export default badgeMock; 