/**
 * 证书相关API的模拟数据
 */

// 证书统计数据
const certificateStats = {
  total: 18,
  acquired: 12,
  inProgress: 3,
  locked: 3,
  completionRate: 66.7, // 百分比
  rankInClass: 3,
  totalClassmates: 32
};

// 证书类别
const certificateCategories = [
  {
    id: 1,
    name: '基础技能证书',
    total: 6,
    acquired: 5,
    icon: '/static/icons/cert-basic.png',
    colorClass: 'basic'
  },
  {
    id: 2,
    name: '烹饪技术证书',
    total: 4, 
    acquired: 3,
    icon: '/static/icons/cert-cooking.png',
    colorClass: 'cooking'
  },
  {
    id: 3,
    name: '管理能力证书',
    total: 5,
    acquired: 2,
    icon: '/static/icons/cert-management.png',
    colorClass: 'management'
  },
  {
    id: 4,
    name: '高级认证证书',
    total: 3,
    acquired: 2,
    icon: '/static/icons/cert-advanced.png',
    colorClass: 'advanced'
  }
];

// 证书列表数据
const certificateList = [
  // 基础技能证书
  {
    id: 101,
    categoryId: 1,
    name: '食品安全操作证书',
    description: '证明持有者掌握了基本的食品安全操作规范和知识',
    acquiredDate: '2023-09-15',
    expiryDate: '2025-09-15',
    level: 1,
    status: 'acquired', // acquired, in-progress, locked
    progress: 100,
    imageUrl: '/static/images/certificates/food-safety.png',
    requirements: [
      '完成食品安全基础练习',
      '通过理论考试（得分≥80分）',
      '完成实操考核'
    ],
    skills: ['食品储存', '卫生管理', '安全规范'],
    validationCode: 'FS20230915XH2301'
  },
  {
    id: 102,
    categoryId: 1,
    name: '厨房基础操作证书',
    description: '证明持有者掌握了厨房基本操作技能和安全规范',
    acquiredDate: '2023-10-20',
    expiryDate: '2025-10-20',
    level: 1,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/kitchen-basics.png',
    requirements: [
      '完成厨房基础练习',
      '通过理论考试（得分≥75分）',
      '完成厨房工具使用考核'
    ],
    skills: ['刀工基础', '厨房设备使用', '清洁与维护'],
    validationCode: 'KB20231020XH2302'
  },
  {
    id: 103,
    categoryId: 1,
    name: '服务礼仪证书',
    description: '证明持有者掌握了餐饮服务礼仪和客户服务技巧',
    acquiredDate: '2023-11-05',
    expiryDate: '2025-11-05',
    level: 1,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/service-etiquette.png',
    requirements: [
      '完成服务礼仪练习',
      '通过理论考试（得分≥80分）',
      '完成服务场景模拟考核'
    ],
    skills: ['客户接待', '投诉处理', '服务标准'],
    validationCode: 'SE20231105XH2303'
  },
  {
    id: 104,
    categoryId: 1,
    name: '餐饮英语证书',
    description: '证明持有者掌握了基本的餐饮行业英语交流能力',
    acquiredDate: '2023-12-10',
    expiryDate: '2025-12-10',
    level: 2,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/catering-english.png',
    requirements: [
      '完成餐饮英语练习',
      '通过听力与会话考试（得分≥70分）',
      '完成点餐场景模拟考核'
    ],
    skills: ['英语点餐', '常用餐饮术语', '外国客人交流'],
    validationCode: 'CE20231210XH2304'
  },
  {
    id: 105,
    categoryId: 1,
    name: '餐饮营养基础证书',
    description: '证明持有者掌握了基本的食材营养知识和健康饮食搭配技巧',
    acquiredDate: '2024-01-15',
    expiryDate: '2026-01-15',
    level: 2,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/nutrition-basics.png',
    requirements: [
      '完成营养学基础练习',
      '通过理论考试（得分≥75分）',
      '完成膳食搭配考核'
    ],
    skills: ['营养素认知', '健康食谱设计', '特殊饮食需求识别'],
    validationCode: 'NB20240115XH2305'
  },
  {
    id: 106,
    categoryId: 1,
    name: '厨房成本控制证书',
    description: '证明持有者掌握了基本的厨房成本管理和控制方法',
    acquiredDate: null,
    expiryDate: null,
    level: 2,
    status: 'in-progress',
    progress: 65,
    imageUrl: '/static/images/certificates/cost-control.png',
    requirements: [
      '完成成本控制练习',
      '通过理论考试（得分≥80分）',
      '完成成本分析实践作业'
    ],
    skills: ['食材采购', '库存管理', '成本核算'],
    validationCode: null
  },
  
  // 烹饪技术证书
  {
    id: 201,
    categoryId: 2,
    name: '中式烹饪基础证书',
    description: '证明持有者掌握了中式烹饪的基本技法和代表菜品制作',
    acquiredDate: '2023-08-20',
    expiryDate: '2025-08-20',
    level: 2,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/chinese-cooking.png',
    requirements: [
      '完成中式烹饪基础练习',
      '通过理论考试（得分≥75分）',
      '制作5道指定中式菜品'
    ],
    skills: ['炒', '煮', '蒸', '炖', '基础调味'],
    validationCode: 'CC20230820XH2306'
  },
  {
    id: 202,
    categoryId: 2,
    name: '西式烹饪基础证书',
    description: '证明持有者掌握了西式烹饪的基本技法和代表菜品制作',
    acquiredDate: '2023-09-25',
    expiryDate: '2025-09-25',
    level: 2,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/western-cooking.png',
    requirements: [
      '完成西式烹饪基础练习',
      '通过理论考试（得分≥75分）',
      '制作4道指定西式菜品'
    ],
    skills: ['煎', '烤', '炖', '西式调味'],
    validationCode: 'WC20230925XH2307'
  },
  {
    id: 203,
    categoryId: 2,
    name: '面点制作证书',
    description: '证明持有者掌握了各类面点的制作技巧和工艺',
    acquiredDate: '2023-11-15',
    expiryDate: '2025-11-15',
    level: 2,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/pastry-making.png',
    requirements: [
      '完成面点制作练习',
      '通过理论考试（得分≥75分）',
      '制作6种不同类型面点'
    ],
    skills: ['面团调制', '发酵控制', '成型技巧', '烘焙技术'],
    validationCode: 'PM20231115XH2308'
  },
  {
    id: 204,
    categoryId: 2,
    name: '创意料理证书',
    description: '证明持有者具备创新烹饪能力和美食创意设计能力',
    acquiredDate: null,
    expiryDate: null,
    level: 3,
    status: 'in-progress',
    progress: 40,
    imageUrl: '/static/images/certificates/creative-cooking.png',
    requirements: [
      '完成创意料理练习',
      '提交原创菜品配方3份',
      '现场制作2道创意菜品'
    ],
    skills: ['食材创新搭配', '菜品创意呈现', '口味创新'],
    validationCode: null
  },
  
  // 管理能力证书
  {
    id: 301,
    categoryId: 3,
    name: '厨房管理证书',
    description: '证明持有者具备厨房团队管理和运营能力',
    acquiredDate: '2023-10-10',
    expiryDate: '2025-10-10',
    level: 3,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/kitchen-management.png',
    requirements: [
      '完成厨房管理练习',
      '通过理论考试（得分≥80分）',
      '完成厨房管理案例分析',
      '具备2年以上厨房工作经验'
    ],
    skills: ['人员管理', '排班规划', '危机处理', '厨房运营优化'],
    validationCode: 'KM20231010XH2309'
  },
  {
    id: 302,
    categoryId: 3,
    name: '餐厅运营证书',
    description: '证明持有者具备餐厅日常运营和管理能力',
    acquiredDate: '2024-01-05',
    expiryDate: '2026-01-05',
    level: 3,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/restaurant-operation.png',
    requirements: [
      '完成餐厅运营练习',
      '通过理论考试（得分≥80分）',
      '完成餐厅运营方案设计',
      '具备1年以上餐厅工作经验'
    ],
    skills: ['餐厅日常管理', '顾客关系维护', '服务质量控制', '环境管理'],
    validationCode: 'RO20240105XH2310'
  },
  {
    id: 303,
    categoryId: 3,
    name: '餐饮市场营销证书',
    description: '证明持有者掌握了餐饮市场推广和营销策略',
    acquiredDate: null,
    expiryDate: null,
    level: 3,
    status: 'in-progress',
    progress: 20,
    imageUrl: '/static/images/certificates/catering-marketing.png',
    requirements: [
      '完成餐饮营销练习',
      '通过理论考试（得分≥80分）',
      '设计并提交营销方案',
      '完成营销案例分析'
    ],
    skills: ['市场分析', '推广策略', '品牌建设', '顾客心理学'],
    validationCode: null
  },
  {
    id: 304,
    categoryId: 3,
    name: '餐饮人力资源管理证书',
    description: '证明持有者具备餐饮企业人力资源规划和管理能力',
    acquiredDate: null,
    expiryDate: null,
    level: 3,
    status: 'locked',
    progress: 0,
    imageUrl: '/static/images/certificates/hr-management.png',
    requirements: [
      '完成人力资源管理练习',
      '通过理论考试（得分≥80分）',
      '完成员工培训计划设计',
      '具备2年以上管理经验'
    ],
    skills: ['招聘筛选', '培训体系', '绩效考核', '团队建设'],
    validationCode: null
  },
  {
    id: 305,
    categoryId: 3,
    name: '餐饮财务管理证书',
    description: '证明持有者掌握了餐饮企业财务分析和管理能力',
    acquiredDate: null,
    expiryDate: null,
    level: 4,
    status: 'locked',
    progress: 0,
    imageUrl: '/static/images/certificates/financial-management.png',
    requirements: [
      '完成财务管理练习',
      '通过理论考试（得分≥85分）',
      '完成财务分析报告',
      '具备会计或财务相关经验'
    ],
    skills: ['成本控制', '利润分析', '预算规划', '财务报表解读'],
    validationCode: null
  },
  
  // 高级认证证书
  {
    id: 401,
    categoryId: 4,
    name: '餐饮企业经营管理师',
    description: '高级认证，证明持有者具备餐饮企业全面经营管理能力',
    acquiredDate: '2023-12-15',
    expiryDate: '2026-12-15',
    level: 4,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/business-management.png',
    requirements: [
      '完成企业经营管理练习',
      '通过综合理论考试（得分≥85分）',
      '成功完成商业计划书设计',
      '通过面试考核',
      '具备3年以上餐饮管理经验'
    ],
    skills: ['战略规划', '全面管理', '危机处理', '业务拓展'],
    validationCode: 'BM20231215XH2311'
  },
  {
    id: 402,
    categoryId: 4,
    name: '高级厨师长认证',
    description: '高级认证，证明持有者具备卓越的烹饪技艺和厨房管理能力',
    acquiredDate: '2024-02-10',
    expiryDate: '2027-02-10',
    level: 4,
    status: 'acquired',
    progress: 100,
    imageUrl: '/static/images/certificates/master-chef.png',
    requirements: [
      '完成高级烹饪技术练习',
      '通过综合理论考试（得分≥90分）',
      '现场制作高级料理组合',
      '菜单设计与成本控制考核',
      '具备5年以上厨师经验'
    ],
    skills: ['菜单研发', '高级烹饪技法', '厨房团队领导', '食材鉴赏'],
    validationCode: 'MC20240210XH2312'
  },
  {
    id: 403,
    categoryId: 4,
    name: '餐饮集团运营总监',
    description: '高级认证，证明持有者具备餐饮集团多店运营和管理能力',
    acquiredDate: null,
    expiryDate: null,
    level: 5,
    status: 'locked',
    progress: 0,
    imageUrl: '/static/images/certificates/operations-director.png',
    requirements: [
      '完成集团运营管理练习',
      '通过高级管理理论考试（得分≥90分）',
      '多店运营方案设计',
      '通过管理案例分析',
      '具备5年以上餐饮管理经验，包括2年以上多店管理经验'
    ],
    skills: ['多店管理', '标准化建设', '品牌管理', '区域市场拓展'],
    validationCode: null
  }
];

// 我的证书成长路径数据
const certificateGrowthPath = {
  currentLevel: 3,
  nextMilestone: {
    name: '餐饮管理高级专家',
    requiredCertificates: 15,
    progress: 12
  },
  milestones: [
    {
      level: 1,
      name: '餐饮学徒',
      certificatesRequired: 3,
      completed: true,
      completedDate: '2023-10-01',
      rewards: ['基础技能徽章', '入门学习资料包']
    },
    {
      level: 2,
      name: '餐饮技术员',
      certificatesRequired: 7,
      completed: true,
      completedDate: '2023-11-20',
      rewards: ['技术专精徽章', '职业发展指导练习']
    },
    {
      level: 3,
      name: '餐饮专业管理师',
      certificatesRequired: 12,
      completed: true,
      completedDate: '2024-02-05',
      rewards: ['管理才能徽章', '行业资源对接机会']
    },
    {
      level: 4,
      name: '餐饮管理高级专家',
      certificatesRequired: 15,
      completed: false,
      completedDate: null,
      rewards: ['高级专家徽章', '导师计划资格', '行业峰会VIP邀请']
    },
    {
      level: 5,
      name: '餐饮行业领袖',
      certificatesRequired: 18,
      completed: false,
      completedDate: null,
      rewards: ['行业领袖勋章', '创业孵化支持', '投资人对接机会']
    }
  ],
  recentAchievements: [
    {
      date: '2024-02-10',
      title: '获得"高级厨师长认证"证书',
      icon: '/static/icons/achievement-certificate.png'
    },
    {
      date: '2024-02-05',
      title: '晋升为"餐饮专业管理师"',
      icon: '/static/icons/achievement-level.png'
    },
    {
      date: '2024-01-15',
      title: '获得"餐饮营养基础证书"',
      icon: '/static/icons/achievement-certificate.png'
    },
    {
      date: '2024-01-05',
      title: '获得"餐厅运营证书"',
      icon: '/static/icons/achievement-certificate.png'
    }
  ],


};
  // 用户证书列表
  const userCertificates = [
    {
      id: 1001,
      title: '服务流程',
      date: '2023-06-15',
      level: 'P3',
      icon: 'icon-award',
      number: 'CR20230615001',
      features: ['服务技能', '沟通能力', '团队协作'],
      position: '服务员',
      skills: [
        { name: '服务礼仪', value: 85 },
        { name: '菜品知识', value: 75 },
        { name: '沟通能力', value: 80 }
      ]
    },
    {
      id: 1002,
      title: '客户沟通',
      date: '2023-04-10',
      level: 'P2',
      icon: 'icon-paper-plane',
      number: 'CR20230410001',
      features: ['客户服务', '沟通技巧', '问题解决'],
      position: '前厅',
      skills: [
        { name: '沟通能力', value: 90 },
        { name: '应急处理', value: 70 },
        { name: '服务态度', value: 85 }
      ]
    },
    {
      id: 1003,
      title: '菜品知识',
      date: '2023-05-20',
      level: 'P2',
      icon: 'icon-utensils',
      number: 'CR20230520001',
      features: ['菜品介绍', '配料知识', '推荐能力'],
      position: '服务员',
      skills: [
        { name: '菜品介绍', value: 85 },
        { name: '推荐技巧', value: 75 },
        { name: '配料知识', value: 80 }
      ]
    },
    {
      id: 1004,
      title: '投诉处理',
      date: '2023-03-05',
      level: 'P1',
      icon: 'icon-service',
      number: 'CR20230305001',
      features: ['投诉受理', '问题解决', '顾客安抚'],
      position: '前厅',
      skills: [
        { name: '问题分析', value: 75 },
        { name: '情绪安抚', value: 85 },
        { name: '解决方案', value: 80 }
      ]
    },
    {
      id: 1005,
      title: '初级后勤',
      date: '2023-02-15',
      level: 'P1',
      icon: 'icon-logistics',
      number: 'CR20230215001',
      features: ['物资管理', '设备维护', '环境整理'],
      position: '迎宾',
      skills: [
        { name: '环境整理', value: 80 },
        { name: '物资管理', value: 75 },
        { name: '设备维护', value: 70 }
      ]
    },
    {
      id: 1006,
      title: '基础服务',
      date: '2023-01-10',
      level: 'P1',
      icon: 'icon-service',
      number: 'CR20230110001',
      features: ['服务礼仪', '基础接待', '服务流程'],
      position: '服务员',
      skills: [
        { name: '服务礼仪', value: 80 },
        { name: '接待能力', value: 75 },
        { name: '流程熟悉', value: 85 }
      ]
    },
    {
      id: 1007,
      title: '高级服务',
      date: '2022-12-15',
      level: 'P4',
      icon: 'icon-expert',
      number: 'CR20221215001',
      features: ['高级服务', '团队管理', '培训能力'],
      position: '服务员',
      skills: [
        { name: '团队管理', value: 85 },
        { name: '培训能力', value: 90 },
        { name: '服务创新', value: 80 }
      ]
    }
  ];

// API调用处理函数
const certificateMock = {
  // 获取证书统计数据
  'GET /api/certificates/stats': (data) => {
    return {
      code: 0,
      message: 'success',
      data: certificateStats
    };
  },
  
  // 获取证书类别列表
  'GET /api/certificates/categories': (data) => {
    return {
      code: 0,
      message: 'success',
      data: certificateCategories
    };
  },
  
  // 获取证书列表（支持按类别筛选）
  'GET /api/certificates/list': (data) => {
    let result = [...certificateList];
    
    // 按类别筛选
    if (data.categoryId) {
      result = result.filter(item => item.categoryId === parseInt(data.categoryId));
    }
    
    // 按状态筛选
    if (data.status) {
      result = result.filter(item => item.status === data.status);
    }
    
    // 按搜索词筛选
    if (data.keyword) {
      const keyword = data.keyword.toLowerCase();
      result = result.filter(item => 
        item.name.toLowerCase().includes(keyword) || 
        item.description.toLowerCase().includes(keyword)
      );
    }
    
    return {
      code: 0,
      message: 'success',
      data: result
    };
  },
  
  // 获取证书详情
  'GET /api/certificates/detail': (data) => {
    const certificate = certificateList.find(item => item.id === parseInt(data.id));
    
    if (certificate) {
      return {
        code: 0,
        message: 'success',
        data: certificate
      };
    } else {
      return {
        code: 404,
        message: '证书不存在',
        data: null
      };
    }
  },
  
  // 获取证书成长路径
  'GET /api/certificates/growth-path': (data) => {
    return {
      code: 0,
      message: 'success',
      data: certificateGrowthPath
    };
  },
  
  // 验证证书真伪
  'POST /api/certificates/validate': (data) => {
    const certificate = certificateList.find(item => item.validationCode === data.code);
    
    if (certificate) {
      return {
        code: 0,
        message: 'success',
        data: {
          isValid: true,
          certificate: certificate
        }
      };
    } else {
      return {
        code: 0,
        message: 'success',
        data: {
          isValid: false,
          message: '无效的证书验证码'
        }
      };
    }
  },
  // 获取用户证书列表
  'GET /api/user/certificates': (data) => {
    const { position, level, tab } = datq.data || {};
    console.log('position, level, tab',position, level, tab,userCertificates)
    let result = [...userCertificates];
      
    if (position && position !== '全部') {
      result = result.filter(cert => cert.position === position);
    }
    
    if (level && level !== '全部') {
      result = result.filter(cert => cert.level === level);
    }
    
    if (tab && tab !== '全部') {
      result = result.filter(cert => cert.position === tab);
    }
    return {
      code: 0,
      message: 'success',
      data: result
    };
  }
  
};

export default certificateMock; 