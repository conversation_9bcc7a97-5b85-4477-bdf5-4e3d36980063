/**
 * 用户模块Mock数据
 */

// 用户信息
const userInfo = {
  id: 1001,
  name: '张三',
  avatar: '/static/images/avatar.png',
  phone: '13800138000',
  idCard: '110101199001011234',
  level: 'P1',
  position: '服务员',
  joinDate: '2023-01-15'
};


// 用户徽章
const userBadges = [
  {
    id: 'badge_001',
    name: '学习先锋',
    description: '连续30天完成学习任务',
    earnedDate: '2023-04-15',
    image: 'https://cdn.example.com/badges/learning_pioneer.png'
  },
  {
    id: 'badge_002',
    name: '知识达人',
    description: '完成100道练习题',
    earnedDate: '2023-02-28',
    image: 'https://cdn.example.com/badges/knowledge_master.png'
  },
  {
    id: 'badge_003',
    name: '考试满分',
    description: '在任意考试中获得满分',
    earnedDate: '2023-06-10',
    image: 'https://cdn.example.com/badges/perfect_score.png'
  }
];

// 用户技能雷达图数据
const userSkillRadar = {
  categories: ['烹饪基础', '食材选择', '刀工技巧', '火候掌控', '摆盘艺术', '风味调配'],
  values: [85, 92, 78, 88, 75, 90]
};

// 用户徽章统计
const userBadgeStats = {
  total: 15,
  earned: 12,
  recent: [
    {
      id: 'badge_004',
      name: '实战能手',
      earnedDate: '2023-08-05',
      image: 'https://cdn.example.com/badges/practice_master.png'
    },
    {
      id: 'badge_005',
      name: '分享达人',
      earnedDate: '2023-07-22',
      image: 'https://cdn.example.com/badges/sharing_expert.png'
    }
  ]
};


// 练习记录统计mock数据
const practiceStatsData = {
  totalCount: 28,
  totalDuration: '133h',
  weeklyCount: 5,
  monthlyCount: 14
};

// 考试记录统计mock数据
const examStatsData = {
  totalCount: 12,
  passRate: '85%',
  averageScore: 78,
  pendingCertCount: 2
};

// 徽章统计数据
const badgeStatsData = {
  totalEarned: 8,
  unlockPercentage: '42%',
  highestLevel: '金质'
};

// 徽章分类数据
const badgeCategoriesData = [
  {
    title: '考试成就',
    icon: 'icon-trophy',
    badges: [
      {
        id: 1,
        name: '考试达人',
        icon: 'icon-crown',
        type: 'gold',
        date: '2023-06-15',
        unlocked: true,
        description: '连续通过5次考试',
        rarity: '金质',
        unlockTip: '连续通过5次考试获得'
      },
      {
        id: 2,
        name: '优秀考生',
        icon: 'icon-award',
        type: 'silver',
        date: '2023-05-20',
        unlocked: true,
        description: '考试平均分达到90分以上',
        rarity: '银质',
        unlockTip: '考试平均分达到90分以上获得'
      },
      {
        id: 3,
        name: '满分王者',
        icon: 'icon-medal',
        type: 'locked',
        unlocked: false,
        progress: 68,
        progressText: '获得满分解锁',
        description: '获得一次考试满分',
        rarity: '金质',
        unlockTip: '获得一次考试满分解锁'
      }
    ]
  },
  {
    title: '学习成就',
    icon: 'icon-book',
    badges: [
      {
        id: 4,
        name: '学习先锋',
        icon: 'icon-graduation',
        type: 'purple',
        date: '2023-06-03',
        unlocked: true,
        description: '连续学习30天',
        rarity: '紫质',
        unlockTip: '连续学习30天获得'
      },
      {
        id: 5,
        name: '百小时成就',
        icon: 'icon-clock',
        type: 'blue',
        date: '2023-05-18',
        unlocked: true,
        description: '累计学习时间达到100小时',
        rarity: '蓝质',
        unlockTip: '累计学习时间达到100小时获得'
      }
    ]
  }
];

// 练习记录数据
const practiceRecordsData = {
  total: 28,
  records: [
    {
      id: 1,
      category: '理论知识',
      date: '2024-03-21',
      title: '食品安全基础知识',
      duration: '45分钟',
      questionCount: 30,
      accuracy: 85,
      position: '初级服务员',
      level: 'L1'
    },
    {
      id: 2,
      category: '情景对话',
      date: '2024-03-20',
      title: '顾客投诉处理',
      duration: '30分钟',
      questionCount: 20,
      accuracy: 90,
      position: '初级服务员',
      level: 'L1'
    }
  ]
};

// 考试记录数据
const examRecordsData = {
  total: 12,
  records: [
    {
      id: 1,
      title: '初级服务员理论考试',
      date: '2024-03-21',
      status: '已通过',
      score: 92,
      duration: '45分钟',
      questionCount: 50,
      accuracy: 92,
      position: '服务员',
      level: 'L1'
    },
    {
      id: 2,
      title: '食品安全认证考试',
      date: '2024-03-15',
      status: '未通过',
      score: 65,
      duration: '60分钟',
      questionCount: 60,
      accuracy: 65,
      position: '厨师',
      level: 'L2'
    }
  ]
};

// 岗位列表
const positions ={
  "rows": [
      {
          "id": 49,
          "name": "切菜师",
          "code": "T1747218648544_1747218682036",
          "typeId": 6,
          "type": {
              "id": 6,
              "name": "后厨",
              "code": "T1747218648544"
          },
          "level": {
              "id": 17,
              "name": "P4",
              "code": "P4",
              "orderNum": 4
          },
          "isDefault": true,
          "employeeId": 42,
          "enterpriseId": "8ecca795-c9a0-4cd4-9b82-bf4d190d3f32",
          "createTime": "2025-05-27 13:44:13",
          "updateTime": "2025-05-27 13:44:13"
      },
      {
          "id": 48,
          "name": "烧烤师",
          "code": "T1747218648544_1747218673641",
          "typeId": 6,
          "type": {
              "id": 6,
              "name": "后厨",
              "code": "T1747218648544"
          },
          "level": {
              "id": 18,
              "name": "P3",
              "code": "P3",
              "orderNum": 3
          },
          "isDefault": false,
          "employeeId": 42,
          "enterpriseId": "8ecca795-c9a0-4cd4-9b82-bf4d190d3f32",
          "createTime": "2025-05-27 13:44:13",
          "updateTime": "2025-05-27 13:44:13"
      }
  ],
  "total": 2,
  "employee": {
      "id": 42,
      "name": "曾",
      "openId": "oxiSG65bMvpNcF9TORr5mvW-HXo4",
      "departmentId": 22,
      "entryTime": "2025-05-29",
      "status": "1"
  }
};

/**
 * 用户授权
 * @param {Object} params 授权参数
 * @returns {Object} 授权结果
 */
const authorize = (params) => {
  const { nickname, phone } = params;
  
  // 验证必填参数
  if (!nickname || !phone) {
    return {
      code: 1,
      message: '昵称和手机号不能为空',
      data: null
    };
  }
  
  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(phone)) {
    return {
      code: 1,
      message: '手机号格式不正确',
      data: null
    };
  }
  
  // 模拟成功返回
  return {
    code: 0,
    message: '授权成功',
    data: {
      token: 'mock_token_' + Date.now(),
      userInfo: {
        ...userInfo,
        nickname,
        phone
      }
    }
  };
};

// API调用处理函数
const userMock = {

  // 获取岗位列表
  'GET /api/wechat/employee/promotion-positions': (data) => {
    return {
      code: 0,
      message: 'success',
      data: positions
    };
  },
  // 获取用户信息
  'GET /api/user/info': (data) => {
    return {
      code: 0,
      message: 'success',
      data: userInfo
    };
  },
  
  // 获取用户证书列表
  'GET /api/user/certificates': (data) => {
    return {
      code: 0,
      message: 'success',
      data: userCertificates
    };
  },
  
  // 获取用户徽章列表
  'GET /api/user/badges': (data) => {
    return {
      code: 0,
      message: 'success',
      data: userBadges
    };
  },
  
  // 获取用户技能雷达数据
  'GET /api/user/skill-radar': (data) => {
    return {
      code: 0,
      message: 'success',
      data: userSkillRadar
    };
  },
  
  // 获取用户徽章统计
  'GET /api/user/badge-stats': (data) => {
    return {
      code: 0,
      message: 'success',
      data: userBadgeStats
    };
  },
  
  // 获取徽章详情
  'GET /api/user/badge/detail': (data) => {
    const { id } = data;
    const badge = userBadges.find(b => b.id === id);
    
    if (!badge) {
      return {
        code: 404,
        message: '徽章不存在',
        data: null
      };
    }
    
    return {
      code: 0,
      message: 'success',
      data: badge
    };
  },
  
  // 更新用户信息
  'POST /api/user/update': (data) => {
    // 模拟更新成功
    Object.assign(userInfo, data);
    return {
      code: 0,
      message: '更新成功',
      data: userInfo
    };
  },
  
  // 用户授权
  'POST /api/user/authorize': (data) => {
    return authorize(data);
  },

  // 获取用户练习统计数据
  'GET /api/user/practice-stats': (data) => {
    return {
      code: 0,
      message: 'success',
      data: practiceStatsData
    };
  },

  // 获取用户考试统计数据
  'GET /api/user/exam-stats': (data) => {
    return {
      code: 0,
      message: 'success',
      data: examStatsData
    };
  },

  // 获取用户练习记录
  'GET /api/user/practice-records': (data) => {
    return {
      code: 0,
      message: 'success',
      data: practiceRecordsData
    };
  },

  // 获取用户考试记录
  'GET /api/user/exam-records': (data) => {
    return {
      code: 0,
      message: 'success',
      data: examRecordsData
    };
  },


};

module.exports = {
  userMock
}; 