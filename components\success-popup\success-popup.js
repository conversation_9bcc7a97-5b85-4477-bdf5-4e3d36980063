import { submitIdentity } from '../../api/user';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    // 下一个页面路径，认证成功后跳转
    nextPath: {
      type: String,
      value: ''
    }
  },
  /**
   * 组件选项
   */
  options: {
    styleIsolation: 'apply-shared'  // 使用apply-shared让组件可以使用app.wxss中的样式
  },


  /**
   * 组件的初始数据
   */
  data: {
    form: {
      realPhone: '',
      realName: '',
      idNumber: ''
    },
    errors: {
      realPhone: '',
      realName: '',
      idNumber: ''
    },
    loading: false,
    companyName: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 处理输入变化
    handleInputChange(e) {
      const { field } = e.currentTarget.dataset;
      const { value } = e.detail;

      this.setData({
        [`form.${field}`]: value,
        [`errors.${field}`]: ''
      });
    },

    // 验证手机号
    validaterealPhone() {
      const realPhoneReg = /^1[3-9]\d{9}$/;
      if (!this.data.form.realPhone) {
        this.setData({ 'errors.realPhone': '请输入手机号' });
        return false;
      }
      if (!realPhoneReg.test(this.data.form.realPhone)) {
        this.setData({ 'errors.realPhone': '请输入正确的11位手机号' });
        return false;
      }
      return true;
    },

    // 验证姓名
    validateName() {
      if (!this.data.form.realName) {
        this.setData({ 'errors.realName': '请输入姓名' });
        return false;
      }
      if (this.data.form.realName.length < 2) {
        this.setData({ 'errors.realName': '姓名长度至少为2个字符' });
        return false;
      }
      return true;
    },

    // 验证身份证号
    validateidNumber() {
      const idNumberReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!this.data.form.idNumber) {
        this.setData({ 'errors.idNumber': '请输入身份证号' });
        return false;
      }
      if (!idNumberReg.test(this.data.form.idNumber)) {
        this.setData({ 'errors.idNumber': '请输入正确的身份证号码' });
        return false;
      }
      return true;
    },

    // 验证表单
    validateForm() {
      const realPhoneValid = this.validaterealPhone();
      const nameValid = this.validateName();
      const idNumberValid = this.validateidNumber();

      return realPhoneValid && nameValid && idNumberValid;
    },

    // 提交认证
    submitVerify() {
      if (!this.validateForm()) {
        return;
      }

      this.setData({ loading: true });

      const verifyData = {
        realPhone: this.data.form.realPhone,
        realName: this.data.form.realName,
        idNumber: this.data.form.idNumber,
        openId: wx.getStorageSync('openId')
      };

      submitIdentity(verifyData).then(res => {
        if (res && res.success) {
          // 设置认证状态
          wx.setStorageSync('isIdentityVerified', true);
          // 关闭弹窗
          this.hidePopup();
          this.setData({
            companyName: res.companyName
          });

          // 通知外部组件认证成功
          this.triggerEvent('verifysuccess', res);
        } else {
          wx.showToast({
            title: '校验失败，请仔细检查信息是否填写正确哦~',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('身份认证失败:', err);
        wx.showToast({
          title: err.message || '校验失败，请仔细检查信息是否填写正确哦~',
          icon: 'none'
        });
      }).finally(() => {
        this.setData({ loading: false });
      });
    },

    // 取消认证
    cancelVerify() {
      this.hidePopup();
    },

    // 隐藏弹窗
    hidePopup() {
      this.setData({
        visible: false,
        form: {
          realPhone: '',
          realName: '',
          idNumber: ''
        },
        errors: {
          realPhone: '',
          realName: '',
          idNumber: ''
        },
        loading: false
      });

      this.triggerEvent('close',{type:'showSuccessModal'});
    },

    // 阻止弹窗内点击事件冒泡
    preventBubble() {
      return;
    }
  }
});
