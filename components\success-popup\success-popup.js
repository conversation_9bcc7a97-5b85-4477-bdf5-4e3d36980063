import { submitIdentity } from '../../api/user';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    // 下一个页面路径，认证成功后跳转
    nextPath: {
      type: String,
      value: ''
    }
  },
  /**
   * 组件选项
   */
  options: {
    styleIsolation: 'apply-shared'  // 使用apply-shared让组件可以使用app.wxss中的样式
  },


  /**
   * 组件的初始数据
   */
  data: {
    errors: {
      realPhone: '',
      realName: '',
      idNumber: ''
    },
    loading: false,
    companyName: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {

    // 阻止弹窗内点击事件冒泡
    preventBubble() {
      return;
    }
  }
});
