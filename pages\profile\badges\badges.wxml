<!--pages/profile/badges.wxml-->
<view class="container">
  <!-- 成就概览 -->
  <stats-summary 
    title="成就统计" 
    stats="{{statsArray}}"
  />

  <!-- 标签页切换 -->
  <tab-navigator
    tabs="{{tabs}}"
    activeTab="{{currentTab}}"
    isObjectTab="{{true}}"
    labelKey="label"
    valueKey="value"
    bind:change="handleTabChange"
  />

  <!-- 徽章分类 -->
  <scroll-view 
    scroll-y 
    class="categories-container"
    bindscrolltolower="loadMore"
    bindrefresherrefresh="onRefresh"
    refresher-enabled="{{true}}"
    refresher-triggered="{{isRefreshing}}"
  >
    <block wx:for="{{filteredBadges}}" wx:for-item="category" wx:key="index">
      <view class="category-container">
        <view class="category-title">
          <text class="iconfont {{category.icon}}"></text>
          <text>{{category.title}}</text>
        </view>
        
        <view class="badges-row">
          <view 
            wx:for="{{category.badges}}" 
            wx:for-item="badge"
            wx:key="index"
            class="badge-card"
            data-badge="{{badge}}"
            bindtap="showBadgeDetail"
          >
            <view 
              class="badge-icon {{badge.type}}"
            >
              <text class="iconfont {{badge.icon}}"></text>
            </view>
            <text class="badge-name">{{badge.name}}</text>
            <text class="badge-date" wx:if="{{badge.date}}">{{badge.date}} 获得</text>
            
            <!-- 未解锁状态 -->
            <view class="badge-locked" wx:if="{{!badge.unlocked}}">
              <text class="iconfont icon-lock"></text>
              <text class="progress-text">{{badge.progressText}}</text>
              <view class="progress-pill">
                <view 
                  class="progress-fill"
                  style="width: {{badge.progress}}%"
                ></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
    <empty-tip   wx:if="{{filteredBadges.length === 0}}" text="暂无徽章" />
  </scroll-view>

  <!-- 徽章详情弹窗 -->
  <view class="badge-detail-popup" wx:if="{{showBadgeDetailPopup}}">
    <view class="detail-header">
      <text>徽章详情</text>
      <text class="iconfont icon-close" bindtap="closeBadgeDetail"></text>
    </view>
    <view class="detail-content" wx:if="{{currentBadge}}">
      <view 
        class="detail-icon {{currentBadge.type}}"
      >
        <text class="iconfont {{currentBadge.icon}}"></text>
      </view>
      <text class="detail-name">{{currentBadge.name}}</text>
      <text class="detail-desc">{{currentBadge.description}}</text>
      <view class="detail-progress" wx:if="{{!currentBadge.unlocked}}">
        <text class="progress-label">解锁进度</text>
        <view class="progress-pill">
          <view 
            class="progress-fill"
            style="width: {{currentBadge.progress}}%"
          ></view>
        </view>
        <text class="progress-text">{{currentBadge.progress}}%</text>
      </view>
      <view class="detail-stats">
        <view class="stat-item">
          <text class="stat-label">获得时间</text>
          <text class="stat-value">{{currentBadge.date || '未获得'}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">稀有度</text>
          <text class="stat-value">{{currentBadge.rarity}}</text>
        </view>
      </view>
      <text class="detail-tip">{{currentBadge.unlockTip}}</text>
    </view>
  </view>
</view>