<!-- components/chat-container/chat-container.wxml -->
<scroll-view 
  class="chat-container" 
  scroll-y="{{!isRecording}}"
  scroll-top="{{scrollTop}}"
  scroll-with-animation="true"
  bindscrolltoupper="onScrollToUpper"
  style="padding-top: {{totalNavHeight}}px; padding-bottom: {{isRecordDetail?0:(keyboardHeight > 0 ? keyboardHeight + 400 : 140) }}rpx;"
>
  <view class="chat-list">
    <!-- 消息列表 -->
    <block wx:for="{{messages}}" wx:key="index">
      <message-item 
        message="{{item}}" 
        message-index="{{index}}"
        pageType="{{pageType}}"
        isPracticeStarted="{{isPracticeStarted}}"
        isNextBtnDisabled="{{isNextBtnDisabled}}"
        bind:playAudio="onPlayMessageAudio"
        bind:startPractice="onStartPractice"
        bind:nextQuestion="onGetNextQuestion"
        isRecordDetail="{{isRecordDetail}}"
      />
    </block>

    <!-- 考试完成提示 -->
  <view class="exam-finished-tip" wx:if="{{examFinished}}">
    {{examFinishedText}}
  </view>

  <!-- 获取试题提示 -->
  <view class="get-question-tip" wx:if="{{showGetQuestionTip}}">
    {{QuestionTipText}}
    <button class="get-question-btn" bindtap="onGetQuestion">
      <text class="iconfont icon-reset"></text>
      重新获取</button>
  </view>
  
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text>正在加载更多...</text>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-indicator" wx:if="{{isLoading}}">
      {{loadingText}}
      <view class="loading-dots">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
    </view>
  </view>
</scroll-view>