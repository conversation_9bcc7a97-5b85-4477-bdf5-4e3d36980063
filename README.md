# 餐烤餐考小程序

## 项目简介
餐烤餐考是一个专注于餐饮行业从业人员学习和考试的微信小程序平台。该平台提供在线练习、模拟考试、专业认证等功能，帮助用户提升专业技能和获取相关资格认证。

## 技术栈
- 框架：微信小程序原生框架
- 开发语言：JavaScript
- 样式：WXSS
- 配置：JSON
- 版本：小程序基础库 2.19.4

## 项目结构
```
├── api/            # API接口目录
├── behaviors/      # 小程序behaviors
├── components/     # 公共组件
├── mock/          # 模拟数据
├── pages/         # 页面文件
│   ├── home/          # 首页
│   ├── master-chat/   # 大师对话
│   ├── practice/      # 练习模块
│   ├── promotion/     # 考试模块
│   └── profile/       # 个人中心
├── static/        # 静态资源
├── utils/         # 工具函数
├── app.js         # 小程序入口文件
├── app.json       # 小程序全局配置
├── app.wxss       # 全局样式
└── project.config.json  # 项目配置文件
```

## 主要功能
1. **首页**
   - 功能概览
   - 最新资讯
   - 学习路径推荐

2. **练习系统**
   - 在线练习
   - 题目列表
   - 练习详情

3. **考试系统**
   - 考试列表
   - 开始考试
   - 考试报告

4. **个人中心**
   - 练习记录
   - 考试记录
   - 我的证书
   - 我的徽章
   - 通知设置
   - 意见反馈

## 开发环境搭建
1. 安装最新版本的微信开发者工具
2. 克隆项目代码
3. 在微信开发者工具中导入项目
4. 填入自己的小程序AppID（测试可使用测试号）

## 项目配置
主要配置项在 `project.config.json` 中：
- 启用ES6转换
- 启用增强编译
- 使用新版本的组件框架
- 支持NPM包

## 开发规范
1. **命名规范**
   - 文件夹：小写中划线
   - 文件：小写中划线
   - 变量：驼峰命名
   - 组件：小写中划线

2. **代码风格**
   - 使用2空格缩进
   - 遵循ESLint规范
   - 组件化开发
   - 模块化管理

## 部署说明
1. 在微信开发者工具中进行代码上传
2. 在小程序后台发布新版本
3. 提交审核
4. 审核通过后上线

## 版本历史
详见 `changelog/` 目录

## 贡献指南
1. Fork 本仓库
2. 创建新的功能分支
3. 提交代码
4. 创建 Pull Request

## 联系方式
- 反馈建议：通过小程序内的反馈功能
- 技术支持：请提交Issue

## 许可证
版权所有 © 2024 餐烤餐考团队 