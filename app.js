// app.js
import { initNavigationGuard } from './utils/navigator';
import eventBus from './utils/event-bus';
import { wxLogin, getUserInfo, validateToken,checkAuthorization ,checkIdentity,checkEnterpriseBinding} from './api/user';
import request from './utils/request';
App({
  // 小程序启动时执行，对应uniapp的onLaunch
  onLaunch() {
    // 初始化全局事件总线
    this.globalData.eventBus = eventBus;
    // ios端音频不能在静音下播放处理
    wx.setInnerAudioOption({
      obeyMuteSwitch: false,
      success: function (res) {
          console.log("开启静音模式下播放音乐的功能");
      },
      fail: function (err) {
          console.log("静音设置失败");
      },
    });
    // 初始化导航守卫
    initNavigationGuard();

    // 初始化日志
    this.initLogs();

    // 获取设备信息和安全区域设置
    this.getSystemInfo();

    // 初始化adminAccessUrl
    this.initAdminAccessUrl();

    // 微信登录获取openId
    this.loginWithWechat();
  },
  
  // 获取系统信息并设置安全区域
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      // 判断是否为iOS设备
      const isIOS = systemInfo.system.toLowerCase().includes('ios');
      // 判断是否为全面屏手机
      const isFullScreen = !!(systemInfo.safeArea && systemInfo.safeArea.bottom > 0 && 
                      systemInfo.safeArea.bottom !== systemInfo.screenHeight);
      // 判断是否为刘海屏iPhone
      const isIphoneX = isIOS && /iPhone X|iPhone 11|iPhone 12|iPhone 13|iPhone 14|iPhone 15/.test(systemInfo.model);
      
      // 计算底部安全区高度
      const safeAreaBottom = isIphoneX ? 34 : (isFullScreen ? 20 : 0);
      
      // 保存到全局数据
      this.globalData.systemInfo = systemInfo;
      this.globalData.isIOS = isIOS;
      this.globalData.isFullScreen = isFullScreen || isIphoneX;
      this.globalData.safeAreaBottom = safeAreaBottom;
      
      console.log('系统安全区域信息:', {
        model: systemInfo.model,
        system: systemInfo.system,
        isIOS: isIOS,
        isFullScreen: isFullScreen || isIphoneX,
        isIphoneX: isIphoneX,
        safeAreaBottom: safeAreaBottom,
        safeArea: systemInfo.safeArea
      });
    } catch (e) {
      console.error('获取系统信息失败', e);
    }
  },
  
  // 微信登录获取openId
  loginWithWechat() {
    wx.login({
      success: (res) => {
        if (res.code) {
          console.log('获取code成功:', res.code);
          // 将code发送到后端换取openId
          this.getWxOpenId(res.code);
        } else {
          console.error('wx.login 调用失败:', res.errMsg);
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('wx.login 接口调用失败:', err);
        wx.showToast({
          title: '登录失败，请检查网络',
          icon: 'none'
        });
      }
    });
  },
  
  // 获取openId
  getWxOpenId(code) {
    wxLogin(code)
      .then(data => {
        const { openid } = data;
        wx.setStorageSync('openId', openid);
        // 检查授权状态
        this.getAuthStatus(openid);
        // 检查身份认证状态
        this.checkIdentityVerification(openid);
        // 获取openId成功后触发事件，通知其他页面
        this.globalData.eventBus.emit('login:success', { openId: openid });
        
      })
      .catch(err => {
        console.error('获取openId失败:', err);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      });
  },
  
  // 获取用户信息
  fetchUserInfo() {
    const openId = this.globalData.openId || wx.getStorageSync('openId');
    if (!openId||!this.globalData.isAuthorized) return;
    getUserInfo(openId)
      .then(res => {
        // 保存用户信息
        this.globalData.userInfo = res.user;
        // 更新授权状态
        this.globalData.isAuthorized = true;
        // 保存用户信息到本地
        wx.setStorageSync('userInfo', res.user);
        // wx.setStorageSync('currentPosition', res.user.currentPosition);
        // wx.setStorageSync('currentLevel', res.user.currentLevel);
        // 触发事件，通知其他页面用户信息已更新
        this.globalData.eventBus.emit('userInfo:updated', res.user);
      })
      .catch(err => {
        console.error('获取用户信息失败:', err);
      });
  },
  
  initLogs() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    console.log('系统信息:', systemInfo);
    
    // 清空旧日志
    this.globalData.logs = [];
    
    // 记录启动时间
    const now = new Date();
    this.globalData.logs.push({
      type: 'launch',
      timestamp: now.getTime(),
      date: now.toLocaleString()
    });
  },

  // 初始化adminAccessUrl
  initAdminAccessUrl() {
    // 检查本地存储中是否有缓存的adminAccessUrl
    const cachedUrl = wx.getStorageSync('adminAccessUrl');
    if (cachedUrl) {
      // 如果有缓存，设置到request配置中
      request.setAdminAccessUrl(cachedUrl);
      console.log('从缓存恢复adminAccessUrl:', cachedUrl);
    } else {
      console.log('未找到缓存的adminAccessUrl，将使用默认baseURL');
    }
  },

  // 检查授权状态
  getAuthStatus(openid) {
    // 如果没有提供openid，尝试从缓存获取
    if (!openid) {
      openid = wx.getStorageSync('openId');
      console.error('无法检查授权状态: 缺少openId');
      return Promise.reject('缺少openId');
    }
    
    return checkAuthorization(openid)
      .then(data => {
        if(data.authorized){
          this.globalData.isAuthorized = true;
          // 保存openId到本地存储
          wx.setStorageSync('token', openid);
          wx.setStorageSync('isAuthorized', true);
          // 更新全局数据
          this.globalData.openId = openid;
          console.log('授权状态检查: 已授权');
         
          // 获取openId后可以继续检查用户信息
          this.fetchUserInfo();
        } else {
          this.globalData.isAuthorized = false;
          wx.setStorageSync('isAuthorized', false);
          console.log('授权状态检查: 未授权');
        }
         // 触发授权状态更新事件
         this.globalData.eventBus.emit('isAuthorized:updated', data.authorized);
          
         return Promise.resolve(data);
      })
      .catch(err => {
        console.error('检查授权状态失败:', err);
        return Promise.reject(err);
      });
  },
  
  // 检查身份认证状态
  async checkIdentityVerification(openId) {
    // 如果没有提供openId，尝试从缓存获取
    if (!openId) {
      openId = wx.getStorageSync('openId');
      if (!openId) {
        console.error('无法检查身份认证状态: 缺少openId');
        return Promise.reject('缺少openId');
      }
    }
    await checkEnterpriseBinding({openId:openId}).then(res=>{
      this.globalData.isIdentityVerified = res.isBound;
      wx.setStorageSync('isIdentityVerified', res.isBound);
    })
    return checkIdentity({openId: openId}).then(res => {
      // this.globalData.isIdentityVerified = res.verified;
      // wx.setStorageSync('isIdentityVerified', res.verified);
      wx.setStorageSync('isNewUser', res.isNewUser);
      // 更新全局数据
      this.globalData.eventBus.emit('isIdentityVerified:updated', res.verified);
      
      return Promise.resolve(res);
    }).catch(err => {
      console.error('检查身份认证状态失败:', err);
      return Promise.reject(err);
    });
  },
  // 小程序显示时执行，对应uniapp的onShow
  onShow: function() {
    console.log('App Show');
    
    // 如果有openId，重新检查授权和认证状态
    const openId = wx.getStorageSync('openId');
    if (openId) {
      console.log('重新检查用户授权和认证状态');
      // 重新检查授权状态
      this.getAuthStatus(openId)
       // 检查身份认证状态
       this.checkIdentityVerification(openId);
    }else{
      this.loginWithWechat();
    }
  },
  
  // 小程序隐藏时执行，对应uniapp的onHide
  onHide: function() {
    console.log('App Hide');
  },
  
  // 全局数据
  globalData: {
    userInfo: null,
    openId: null,
    logs: [],
    eventBus: null,
    isAuthorized: false,
    isIdentityVerified: false,
    // 设备信息相关
    systemInfo: null,
    isIOS: false,
    isFullScreen: false,
    safeAreaBottom: 0
  }
}) 
