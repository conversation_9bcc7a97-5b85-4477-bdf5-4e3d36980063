<!--pages/profile/join-enterprise/join-enterprise.wxml-->
<view class="container">
  <view class="form-container">
    <!-- 页面说明 -->
    <view class="page-intro">
      <text class="intro-text">请填写以下信息申请加入企业</text>
    </view>

    <!-- 申请表单 -->
    <view class="form-content">
      <!-- 企业邀请码 -->
      <view class="form-group">
        <text class="form-label">企业邀请码<text class="required">*</text></text>
        <view class="input-wrapper">
          <input
            class="form-input"
            value="{{formData.inviteCode}}"
            placeholder="请输入企业邀请码"
            type="text"
            bindinput="onInviteCodeInput"
          />
          <view class="loading-icon" wx:if="{{isLoadingEnterprise}}">
            <text class="loading-text">...</text>
          </view>
        </view>
      </view>

      <!-- 企业名称 -->
      <view class="form-group">
        <text class="form-label">企业名称<text class="required">*</text></text>
        <view class="form-display">
          <text class="display-text" wx:if="{{formData.enterpriseName}}">{{formData.enterpriseName}}</text>
          <text class="placeholder-text" wx:else>请先输入企业邀请码</text>
        </view>
      </view>

      <!-- 姓名 -->
      <view class="form-group">
        <text class="form-label">姓名<text class="required">*</text></text>
        <input
          class="form-input"
          value="{{formData.realName}}"
          placeholder="请输入您的真实姓名"
          type="text"
          bindinput="onRealNameInput"
        />
      </view>

      <!-- 手机号 -->
      <view class="form-group">
        <text class="form-label">手机号<text class="required">*</text></text>
        <input
          class="form-input"
          value="{{formData.phone}}"
          placeholder="请输入手机号"
          type="number"
          bindinput="onPhoneInput"
        />
      </view>

      <!-- 身份证号 -->
      <view class="form-group">
        <text class="form-label">身份证号<text class="required">*</text></text>
        <input
          class="form-input"
          value="{{formData.idNumber}}"
          placeholder="请输入身份证号"
          type="idcard"
          bindinput="onIdNumberInput"
        />
      </view>

      <!-- 归属部门 -->
      <view class="form-group">
        <text class="form-label">归属部门<text class="required">*</text></text>
        <input
          class="form-input"
          value="{{formData.department}}"
          placeholder="请输入归属部门"
          type="text"
          bindinput="onDepartmentInput"
        />
      </view>

      <!-- 岗位名称 -->
      <view class="form-group">
        <text class="form-label">岗位名称<text class="required">*</text></text>
        <picker
          class="form-picker"
          mode="selector"
          range="{{positionNames}}"
          value="{{positionNameIndex}}"
          bindchange="onPositionNameChange"
          disabled="{{positionNames.length === 0}}"
        >
          <view class="picker-content">
            <text class="picker-text">{{formData.positionName}}</text>
            <!-- <text class="picker-placeholder" wx:else>
              {{positionNames.length > 0 ? '请选择岗位名称' : '请先输入有效的企业邀请码'}}
            </text> -->
            <text class="iconfont icon-right picker-arrow"></text>
          </view>
        </picker>
      </view>

      <!-- 岗位等级 -->
      <view class="form-group">
        <text class="form-label">岗位等级<text class="required">*</text></text>
        <picker
          class="form-picker"
          mode="selector"
          range="{{positionLevels}}"
          value="{{positionLevelIndex}}"
          bindchange="onPositionLevelChange"
          disabled="{{positionLevels.length === 0}}"
        >
          <view class="picker-content">
            <text class="picker-text">{{formData.positionLevel}}</text>
            <!-- <text class="picker-placeholder" wx:else>
              {{positionLevels.length > 0 ? '请选择岗位等级' : '请先选择岗位名称'}}
            </text> -->
            <text class="iconfont icon-right picker-arrow"></text>
          </view>
        </picker>
      </view>

      <!-- 提交按钮 -->
      <button
        class="submit-button"
        bindtap="onSubmit"
        disabled="{{!isValid || isLoading}}"
        loading="{{isLoading}}"
      >
        {{isLoading ? '提交中...' : '提交申请'}}
      </button>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-section">
      <view class="tips-title">
        <text class="iconfont icon-info"></text>
        <text>温馨提示</text>
      </view>
      <view class="tips-content">
        <text class="tip-item">• 请确保填写的信息真实有效</text>
        <text class="tip-item">• 企业邀请码请向企业管理员获取</text>
        <text class="tip-item">• 申请提交后，企业管理员将进行审核</text>
        <!-- <text class="tip-item">• 审核结果将通过短信或系统通知告知</text> -->
      </view>
    </view>
  </view>

    <!-- 认证成功 -->
  <success-popup
    visible="{{showSuccessModal}}"
  />
</view>
