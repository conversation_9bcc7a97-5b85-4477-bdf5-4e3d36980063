import { get, post } from '../utils/request';

/**
 * 获取首页推荐练习
 * @returns {Promise<Array>} 推荐练习列表
 */
export const getRecommendCourses = () => {
  return get('/home/<USER>');
};

/**
 * 获取老用户上次练习记录
 * @param {Object} params - 查询参数
 * @param {string} params.openId - 用户openId
 * @returns {Promise<Array>} 上次练习记录列表
 */
export const getRecentPracticeSubjects = (params) => {
  return get('/wechat/home/<USER>', params);
};

/**
 * 获取用户状态
 * @returns {Promise<Object>} 用户状态信息
 */
export const getUserStatus = () => {
  return get('/user/status');
};


/**
 * 获取用户统计数据
 * @returns {Promise<Object>} 统计数据
 */
export const getPracticeStatistic = () => {
  return get('/wechat/practice/statistics');
};

/**
 * 获取首页配置信息
 * @returns {Promise<Object>} 配置信息数据
 */
export const getInfoConfig = () => {
  return get('/wechat/getInfoConfig');
};

/**
 * 提交用户认证信息
 * @param {Object} data - 认证数据
 * @returns {Promise<Object>} 提交结果
 */
export const submitUserAuth = (data) => {
  return post('/user/auth', data);
};

/**
 * 发送问题给餐考师
 * @param {string} question - 用户提问内容
 * @returns {Promise<Object>} 餐考师回复
 */
export const sendQuestionToCankaoshi = (question,recordId) => {
  return post('/wechat/home/<USER>', { question:question,id:recordId });
};

/**
 * 获取通知公告
 * @returns {Promise<Array>} 通知公告列表
 */
export const getNoticeList = () => {
  return get('/wechat/announcements/current');
};
/**
 * 获取通知公告详情
 * @param {string} id - 通知公告ID
 * @returns {Promise<Object>} 通知公告详情
 */
export const getNoticeDetail = (id) => {
  return get(`/wechat/announcements/${id}`);
};

