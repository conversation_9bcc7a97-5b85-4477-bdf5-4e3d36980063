<!--pages/notice/list.wxml-->
<view class="notice-list-container">
  <!-- 头部标题 -->
  <view class="header">
    <view class="title">通知公告</view>
    <view class="subtitle">及时了解最新动态</view>
  </view>

  <!-- 通知列表 -->
  <view class="notice-list">
    <block wx:if="{{noticeList.length > 0}}">
      <view
        class="notice-item"
        wx:for="{{noticeList}}"
        wx:key="id"
        bindtap="viewNoticeDetail"
        data-notice="{{item}}"
      >
        <!-- 通知图标 -->
        <view class="notice-icon">
          <text class="iconfont icon-bell"></text>
        </view>

        <!-- 通知内容 -->
        <view class="notice-content">
          <view class="notice-title">{{item.title}}</view>
          <view class="notice-summary">{{item.summary || item.plainContent || item.content}}</view>
          <view class="notice-meta">
            <text class="notice-time">{{item.createTime}}</text>
            <text class="notice-status" wx:if="{{item.isNew}}">新</text>
          </view>
        </view>

        <!-- 箭头图标 -->
        <view class="notice-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
     <empty-tip wx:if="{{noticeList.length === 0 && !isLoading}}"></empty-tip>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-item" wx:for="{{[1,2,3]}}" wx:key="*this">
      <view class="loading-icon"></view>
      <view class="loading-content">
        <view class="loading-title"></view>
        <view class="loading-summary"></view>
        <view class="loading-meta"></view>
      </view>
    </view>
  </view>

  <!-- 加载更多提示 -->
  <view class="load-more" wx:if="{{noticeList.length > 0 && !hasMore}}">
    <text>已显示全部通知</text>
  </view>
</view>
