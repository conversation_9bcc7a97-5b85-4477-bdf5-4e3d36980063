  <view class="container">
        <view class="company-banner">
            <view class="company-name" id="companyName">阿依来餐饮集团</view>
            <view class="company-period" id="companyPeriod">2021.06 - 至今</view>
        </view>

        <view class="company-stats">
            <view class="stat-item">
                <view class="stat-value" id="practiceHours">153</view>
                <view class="stat-label">总练习时长(小时)</view>
            </view>
            <view class="stat-item">
                <view class="stat-value" id="examPassed">24</view>
                <view class="stat-label">通过考试</view>
            </view>
            <view class="stat-item">
                <view class="stat-value" id="certificatesEarned">6</view>
                <view class="stat-label">获得证书</view>
            </view>
        </view>

        <view class="card">
            <view class="section-title">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" width="18" height="18">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="8.5" cy="7" r="4"></circle>
                    <line x1="20" y1="8" x2="20" y2="14"></line>
                    <line x1="23" y1="11" x2="17" y2="11"></line>
                </svg>
                岗位记录
            </view>

            <!-- 岗位1 -->
            <view class="position-card">
                <view class="position-header">
                    <view>
                        <view class="position-title">领班</view>
                        <view class="position-period">2022.03 - 至今</view>
                    </view>
                    <view class="position-level">P4 → P5</view>
                </view>
                <view class="position-content">
                    <view class="summary-item">
                        <view class="summary-label">练习时长</view>
                        <view class="summary-value">86小时</view>
                    </view>
                    <view class="summary-item">
                        <view class="summary-label">考试通过</view>
                        <view class="summary-value">12次</view>
                    </view>
                    <view class="summary-item">
                        <view class="summary-label">获得证书</view>
                        <view class="summary-value">4个</view>
                    </view>

                    <view class="badges-container">
                        <view class="badge">阿依来菜品一帅九将</view>
                        <view class="badge">团队管理标准</view>
                        <view class="badge">质量管控</view>
                        <view class="badge">客户沟通技巧</view>
                    </view>
                </view>
            </view>

            <!-- 岗位2 -->
            <view class="position-card">
                <view class="position-header">
                    <view>
                        <view class="position-title">服务员</view>
                        <view class="position-period">2021.06 - 2022.03</view>
                    </view>
                    <view class="position-level">P2 → P4</view>
                </view>
                <view class="position-content">
                    <view class="summary-item">
                        <view class="summary-label">练习时长</view>
                        <view class="summary-value">67小时</view>
                    </view>
                    <view class="summary-item">
                        <view class="summary-label">考试通过</view>
                        <view class="summary-value">12次</view>
                    </view>
                    <view class="summary-item">
                        <view class="summary-label">获得证书</view>
                        <view class="summary-value">2个</view>
                    </view>

                    <view class="badges-container">
                        <view class="badge">餐中服务应急处理标准</view>
                        <view class="badge">预定流程标准</view>
                    </view>
                </view>
            </view>
        </view>
    </view>