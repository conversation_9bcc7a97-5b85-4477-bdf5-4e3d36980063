Component({
  properties: {
    module: {
      type: Object,
      value: {}
    }
  },
  options: {
    styleIsolation: 'apply-shared'  // 使用apply-shared让组件可以使用app.wxss中的样式
  },
  
  
  methods: {
    handleClick() {
      this.triggerEvent('tap', this.properties.module);
    },
    
    handleButtonClick(e) {
      // if (this.properties.module.buttonType === 'disabled') return;
      // // 阻止事件冒泡
      // e.stopPropagation();
      console.log('handleButtonClick',this.properties.module)
      this.triggerEvent('buttonClick', this.properties.module);
    }
  }
}) 