// api/practice.js
import { get, post } from '../utils/request';

// Dify API配置
const DIFY_BASE_URL = 'https://dify-cankao.lingmiaoai.com/v1';
const DIFY_API_KEY = 'app-igwgEj1Ofk5kDfE6IbgZApnP';
// const DIFY_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZGFlNTA5M2YtYWQ4YS00Nzc5LWIwNjktZmE4ZmE2ZjRmZjRkIiwiZXhwIjoxNzQ0NjkxMTQwLCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.W1hU7JCeddRsVB3agtzTZJqNpwwUnXt9Layq-iqIfNY';

/**
 * 获取练习列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} 练习列表
 */
export const getPracticeList = (params) => {
  return get('/practice/list', params);
};

/**
 * 获取练习详情
 * @param {String} id - 练习ID
 * @returns {Promise<Object>} 练习详情
 */
export const getPracticeDetail = (id) => {
  return get('/practice/detail', { id });
};

/**
 * 提交练习答案
 * @param {Object} data - 练习记录数据
 * @returns {Promise<Object>} 提交结果
 */
export const submitPractice = (data) => {
  return post('/practice/submit', data);
};


/**
 * 获取练习记录
 * @returns {Promise<Array>} 练习记录列表
 */
export const getPracticeRecords = () => {
  return get('/practice/records');
};

/**
 * 获取岗位列表
 * @returns {Promise<Array>} 岗位列表
 */
export const getPositions = () => {
  return get('/practice/positions');
};

/**
 * 获取级别列表
 * @returns {Promise<Array>} 级别列表
 */
export const getLevels = () => {
  return get('/practice/levels');
};

/**
 * 获取练习消息记录
 * @param {String} courseId - 练习ID
 * @returns {Promise<Array>} 消息记录列表
 */
export const getCourseMessages = (courseId) => {
  return get('/practice/course/messages', { courseId });
};

/**
 * 发送用户消息
 * @param {string} courseId - 练习ID
 * @param {string} content - 消息内容
 * @returns {Promise<Object>} - AI回复
 */
export const sendUserMessage = (courseId, content) => {
  return post('/practice/course/send-message', { courseId, content });
};

/**
 * 获取推荐练习
 * @param {Number} limit - 限制数量
 * @returns {Promise<Array>} 推荐练习列表
 */
export const getRecommendedCourses = (limit = 3) => {
  return get('/practice/recommended', { limit });
};

/**
 * 调用Dify出题工作流，获取练习题目
 * @param {Object} params - 请求参数
 * @param {string} params.practiceId - 练习ID
 * @param {string} params.userId - 用户ID
 * @param {string} params.difficulty - 难度级别(可选)
 * @returns {Promise<Object>} - 返回题目信息
 */
export const getPracticeQuestion = (params) => {
  return post(
  '/agent/workflows/run',
  params,
  );
  // return post(
  //   '/workflows/run', 
  //   params, 
  //   { 'Authorization': `Bearer ${DIFY_API_KEY}`,
  //     'Content-Type': 'application/json'
  //   },
  //   DIFY_BASE_URL
  // );
};

/**
 * 调用Dify练习工作流，提交用户回答并获取评估
 * @param {Object} params - 请求参数
 * @param {string} params.questionId - 题目ID
 * @param {string} params.question - 题目内容
 * @param {string} params.standardAnswer - 标准答案
 * @param {string} params.userAnswer - 用户回答
 * @param {string} params.userId - 用户ID
 * @returns {Promise<Object>} - 返回评估结果
 */
export const evaluatePracticeAnswer = (params) => {
  return post(
    '/workflows/run', 
    params, 
    { 'Authorization': `Bearer app-j6b4K2FITQjQZe48ix5sV5Ee`,
      'Content-Type': 'application/json'
    },
    DIFY_BASE_URL
  );
};

/**
 * 获取实践练习列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Array>} - 练习列表
 */
export const getPracticeCourses = (params) => {
  return get('/practice/courses', params);
};

/**
 * 获取练习详情
 * @param {string} id - 练习ID
 * @returns {Promise<Object>} - 练习详情
 */
export const getPracticeCourseDetail = (id) => {
  return get('/practice/course/detail', { id });
}; 