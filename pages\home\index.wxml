<view class="container">
  <!-- Banner区域 -->
  <view class="banner">
    <view class="banner-left">
    <view class="user-title" wx:if="{{isAuthorized&&isIdentityVerified}}">
      <!-- <text>hi,快来开启您的{{currentPosition}} ({{currentLevel}}) 之旅吧~</text> -->
      <text>hi,快来开启您的晋升之旅吧~</text>
    </view>
    <text class="banner-title">{{bannerConfig.mainTitle}}</text>
    <text class="banner-subtitle">{{bannerConfig.subTitle}}</text>
    </view>
    <image class="banner-pattern" src="{{bannerConfig.enterpriseLogo ? imgURL + bannerConfig.enterpriseLogo : '/static/images/logo.png'}}" mode="aspectFit"></image>
  </view>

<!-- {{isAuthorized}}
{{isIdentityVerified}} -->
  <!-- 初始化版块 -->
  <view class="init-section" wx:if="{{isNeedAuth&&(!isAuthorized|| !isIdentityVerified)}}">
    <view class="init-section-header">
      <view class="init-section-title">
        <text class="iconfont icon-star"></text>
        <text>餐饮服务技能提升平台</text>
      </view>
    </view>
    <view class="init-section-content">
      <view class="feature-grid">
        <view class="feature-item" bindtap="checkFeatureAccess" data-feature="practice">
          <view class="feature-icon practice-icon">
            <text class="iconfont icon-book-open"></text>
          </view>
          <text class="feature-name">智能练习</text>
        </view>
        <view class="feature-item" bindtap="checkFeatureAccess" data-feature="exam">
          <view class="feature-icon exam-icon">
            <text class="iconfont icon-trophy"></text>
          </view>
          <text class="feature-name">晋升考试</text>
        </view>
        <view class="feature-item" bindtap="checkFeatureAccess" data-feature="analysis">
          <view class="feature-icon analysis-icon">
            <text class="iconfont icon-chart-bar"></text>
          </view>
          <text class="feature-name">能力分析</text>
        </view>
        <view class="feature-item" bindtap="checkFeatureAccess" data-feature="aimaster">
          <view class="feature-icon aimaster-icon">
            <text class="iconfont icon-chef-hat"></text>
          </view>
          <text class="feature-name">助手</text>
        </view>
      </view>
      <view class="init-guide">
        <text class="guide-title">开启您的晋升之旅吧！</text>
        <text class="guide-desc">点击上方功能，开始学习和提升</text>
      </view>
    </view>
  </view>

  <!-- 上次练习记录 (老用户) -->
  <!-- <view class="study-card" wx:if="{{!isNewUser && isAuthorized && isIdentityVerified}}">
    <view class="study-card-header">
      <view class="study-card-title">
        <text class="iconfont icon-history"></text>
        <text>上次练习记录</text>
      </view>
      <view class="study-card-time">今日</view>
    </view>
    <view class="study-card-content">
      <view class="study-course-title">
        基础知识
        <text class="course-tag">服务员 p2</text>
      </view>
      <text class="study-course-subtitle">服务礼仪标准认证证书</text>
      <view class="progress-container">
        <view class="progress-info">
          <text></text>
          <text>考试资格进度65%</text>
        </view>
        <view class="progress-bar-wrapper">
          <view class="progress-bar-fill" style="width: 65%"></view>
        </view>
      </view>
    </view>
    <view class="study-card-footer">
      <button class="continue-btn" bindtap="continueLearning">
        <text class="iconfont icon-play"></text>
        继续练习
      </button>
    </view>
  </view> -->

<!-- 当前岗位等级 -->
  <!-- <view class="position-level">
    欢迎加入我们，开启您的 {{currentPosition}} ({{currentLevel}}) 之旅
  </view> -->

  <!-- 通知公告模块 -->
  <view class="home-card notice-card" wx:if="{{!isNewUser}}">
    <view class="home-header">
      <view class="home-title">
        <text class="iconfont icon-bell1"></text>
        <text>通知公告</text>
      </view>
      <view class="notice-more" bindtap="viewAllNotices">
        更多 <text class="iconfont icon-right"></text>
      </view>
    </view>
    <view class="notice-content">
      <!-- 轮播滚动容器 -->
      <view class="notice-swiper-container" wx:if="{{displayNoticeList.length > 0}}">
        <!-- <swiper
          class="notice-swiper"
          vertical="{{true}}"
          autoplay="{{displayNoticeList.length > 1}}"
          interval="{{3000}}"
          duration="{{500}}"
          circular="{{displayNoticeList.length > 1}}"
          indicator-dots="{{false}}"
          display-multiple-items="{{1}}"
        >
          <swiper-item
            class="notice-swiper-item"
            wx:for="{{displayNoticeList}}"
            wx:key="id"
          > -->
            <view class="notice-item"   wx:for="{{displayNoticeList}}"
            wx:key="id" bindtap="viewNoticeDetail" data-id="{{item.id}}">
              <view class="notice-item-title">
                <text class="notice-badge" wx:if="{{item.isNew}}">NEW</text>
                {{item.title}}
              </view>
              <view class="notice-item-meta">
                <text class="notice-time">{{item.createTime || item.time}}</text>
                <text class="iconfont icon-right"></text>
              </view>
            </view>
          <!-- </swiper-item>
        </swiper> -->
      </view>
      <!-- 空状态 -->
      <empty-tip text="暂无通知公告" showImg="{{false}}" wx:if="{{displayNoticeList.length === 0}}"/>
    </view>
  </view>

  <!-- 推荐练习内容 (新用户) -->
  <view class="home-card recommend-card" wx:if="{{!isNeedAuth ||( isAuthorized && isIdentityVerified)}}">
    <view class="home-header">
      <view class="home-title">
        <text class="iconfont icon-fire"></text>
        <text>{{isNewUser?'推荐练习内容':'上次练习记录'}}</text>
      </view>
      <view class="recommend-badge" wx:if="{{isNewUser}}">新手必学</view>
    </view>
    <view class="recommend-courses" wx:if="{{recommendCourses.length>0}}">
      <view class="recommend-course" wx:for="{{recommendCourses}}" wx:key="index" bindtap="goCourseDetail" data-course="{{item}}">
        <view class="recommend-title">
          <!-- <view class="course-icon">
            <text class="iconfont {{item.icon||'icon-book'}}"></text>
          </view> -->
          <view class="course-info">
            <view class="course-name">{{item.examSubjectName}}</view>
            <!-- <view class="course-desc">{{item.desc}}</view> -->

          </view>
          <view class="course-tag" wx:if="{{isNewUser}}">{{item.status}}</view>
          <view class="course-tag" wx:else>{{item.positionNameZh}} ({{item.positionLevelZh}})</view>
        </view>
        <view wx:if="{{!isNewUser}}">
          <view class="stat-row" >考试资格进度{{item.examQualification}}%</view>
          <view class="progress-bar">
            <view class="progress-bar-fill" style="width: {{item.examQualification}}%"></view>
          </view>
        </view>
        <view wx:else>
        </view>
        <!-- <view class="course-action">
          <text class="iconfont icon-right"></text>
        </view> -->
      </view>
      <!-- <view class="all-courses-btn" bindtap="goAllCourses">
        查看全部练习 <text class="iconfont icon-right"></text>
      </view> -->
    </view>
    <view class="recommend-courses" wx:else>
      <empty-tip text="暂无推荐内容" />
    </view>
  </view>

  <!-- 综合统计区域 -->
  <view class="stats-container" wx:if="{{!isNeedAuth||( isAuthorized && isIdentityVerified)}}">
    <view class="stats-header">
      <text class="stats-title">练习/考试</text>
      <view class="stats-tabs">
        <view
          class="stats-tab {{currentTab === 'practice' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="practice"
        >练习</view>
        <view
          class="stats-tab {{currentTab === 'exam' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="exam"
        >考试</view>
      </view>
    </view>

    <!-- 练习统计 -->
    <view class="stats-content" wx:if="{{currentTab === 'practice'}}">
      <view class="empty-container" wx:if="{{isZero}}" >
        <empty-tip text="您还没有学习，快去学习吧！"/>
        </view>
        <view class="position-stats" wx:else>
          <view class="position-stat-item {{item.className}}"
          wx:for="{{practiceStatItems}}" wx:key="key">
          <view class="position-stat-item-content">
            <view class="position-name">
              <text class="iconfont {{item.icon}}"></text>
              {{item.title}}
            </view>
            <view class="position-value">{{practiceStats[item.key].minutes}}<i>分钟</i></view>
          </view>
          <view class="iconBG">
            <text class="iconfont {{item.icon}}"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 考试统计 -->
    <view class="stats-content" wx:if="{{currentTab === 'exam'}}">
      <view class="cert-stats">
        <view class="cert-stat-item" wx:for="{{examStatItems}}" wx:key="key">
          <view class="cert-icon">
            <text class="iconfont {{item.icon}}"></text>
          </view>
          <view class="cert-value">{{examStats[item.key]}}<i>个</i></view>
          <view class="cert-label">{{item.title}}</view>
        </view>
      </view>
      <button class="view-cert-btn" bindtap="viewCertificates">
        <text class="iconfont icon-trophy"></text>
        查看我的证书
      </button>
    </view>
  </view>

  <!-- 问餐考师悬浮按钮 -->
  <movable-area class="master-btn-container">
    <movable-view
      class="master-btn"
      direction="all"
      inertia="true"
      out-of-bounds="true"
      x="{{masterBtnPosition.x}}"
      y="{{masterBtnPosition.y}}"
      bindtap="goMasterChat"
      damping="40"
      friction="5"
      animation="{{false}}"
      bindchange="onMasterBtnPositionChange"
    >
      <view class="chef-hat-icon">
        <text class="iconfont icon-chef-hat"></text>
      </view>
      <view class="master-btn-text">
        <text class="question">遇到问题？</text>
        <text class="ask-master">问问餐考师</text>
      </view>
    </movable-view>
  </movable-area>

  <!-- 身份认证弹窗组件 -->
  <identity-verify-popup
    visible="{{showAuthModal}}"
    nextPath="{{nextPath}}"
    bindverifysuccess="onVerifySuccess"
    bindclose="onVerifyClose"
  />

  <!-- 授权弹窗组件 -->
  <auth-popup
    visible="{{showAuthPopup}}"
    nextPath="{{nextPath}}"
    bindauthsuccess="onAuthSuccess"
    bindclose="onAuthClose"
  />
  <!-- 认证成功 -->
  <success-popup
    visible="{{showSuccessModal}}"
  />
</view>