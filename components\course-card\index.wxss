/* components/course-card/index.wxss */

/* 练习卡片样式 */
.category-card {
  background: #fff;
  border-radius: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.category-card.current-position {
  border: none;
  box-shadow: 0 20rpx 50rpx rgba(126, 87, 194, 0.15);
}

.category-card.current-position::before {
  content: '';
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(161, 140, 209, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.card-header {
  padding: 32rpx 36rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background:linear-gradient(135deg, rgba(161, 140, 209, 0.15) 0%,rgba(237, 163, 254, 0.5) 100%);
  /* linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%) */
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
}
.flex-box{
  width:70%;
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.card-content {
  padding: 24rpx 36rpx;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #777;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.stat-label .iconfont {
  font-size: 24rpx;
  margin-right: 10rpx;
  color: #a18cd1;
}

.stat-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.progress-bar {
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin: 12rpx 0;
  overflow: hidden;
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #a18cd1, #fbc2eb);
  border-radius: 8rpx;
  position: relative;
  transition: width 0.3s ease;
}

.course-type-badge {
  /* position: absolute; */
  /* top: 32rpx; */
  /* right: 84rpx; */
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}
.course-type-badge .iconfont{
  font-size: 20rpx;
  margin-right: 4rpx;
}

.required-badge {
  background: linear-gradient(135deg, #fbc2eb 0%, #a18cd1 100%);
  color: #fff;
}

.practice-badge {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: #fff;
} 