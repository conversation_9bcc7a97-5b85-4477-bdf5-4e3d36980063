/**
 * 练习相关API的模拟数据
 */

// 练习统计数据
const practiceStats = {
  totalPractices: 320,
  completedPractices: 218,
  totalQuestions: 4280,
  correctRate: 86.5,
  weakCategories: [
    { id: 2, name: '食品安全', correctRate: 75.8 },
    { id: 5, name: '服务礼仪', correctRate: 79.2 }
  ],
  strongCategories: [
    { id: 1, name: '烹饪技巧', correctRate: 92.4 },
    { id: 3, name: '菜品知识', correctRate: 90.1 }
  ],
  practiceTime: 2840, // 分钟
  weeklyPractices: [12, 8, 15, 10, 18, 5, 9], // 最近7天练习次数
  monthlyProgress: 68.2, // 本月进度百分比
};

// 推荐练习数据
const practiceRecommend =[
  
     { id: 51, title: '餐具及环境的卫生标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
            fileId: '3822c943-4106-4e4e-a0f6-b497c01095b8' 
           }, 
    
       { id: 2, title: '阿依来菜品一帅九将知识标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
            fileId: 'bf8f0cae-75fd-4338-bf29-06f3654046fe' 
           }, 
       { id: 3, title: '阿依来菜品及酒水知识标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
            fileId: 'e45c4132-51bc-4087-8f98-ea857ae36a33' 
           },
    { id: 1, title: '服务礼仪标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
              fileId: 'bcbd628c-2a41-4f6b-9450-e2271ec16edd' 
             }, 
];
// 练习列表数据
const practiceList = {
  '27': {
    '16': [
        
         { id: 51, title: '餐具及环境的卫生标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '3822c943-4106-4e4e-a0f6-b497c01095b8' 
               }, 
        
           { id: 2, title: '阿依来菜品一帅九将知识标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'bf8f0cae-75fd-4338-bf29-06f3654046fe' 
               }, 
           { id: 3, title: '阿依来菜品及酒水知识标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'e45c4132-51bc-4087-8f98-ea857ae36a33' 
               }, 
           { id: 4, title: '阿依来特色菜品介绍标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'af5a2550-038e-4212-8f2a-5b43e10df390' 
               }, 
           { id: 5, title: '餐前餐尾卫生标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'ea83878f-7fcc-41c8-9d59-04ba0c9f88cb' 
               }, 
           { id:6, title: '客诉处理指南', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '7914460e-4431-485a-8d1b-fb281373de25' 
               }, 
           { id: 7, title: '餐中服务技能标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'a0eb4710-f1eb-45e3-a437-0d4217e0c0c2' 
               }, 
           { id: 8, title: '餐前服务员每日工作流程标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '8661753e-3998-4209-907f-fd4ab40804a7' 
               }, 
           { id: 9, title: '餐中服务员每日工作流程标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '815c9bcc-dc29-4e4e-b340-431f01e854b8' 
               }, 
           { id: 10, title: '餐尾服务员每日工作流程标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'ce0ff83f-c53b-491e-b4f2-480166b30f0b' 
               }, 
           { id: 11, title: '收藏、打卡、好评培训话术标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'a72e5fa4-8009-44b4-a636-dc6a25ca0950' 
               }, 
           { id: 12, title: '预定流程标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'caadcf34-7824-468c-b5a7-f9f0633e272c' 
               }, 
           { id: 13, title: '5S重点知识', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'ff8d6c4a-dfb1-4d9f-89f3-c594eb7058bc' 
               }, 
           { id: 14, title: '餐饮行业食品安全重点知识', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '3a27c6f1-007b-42ff-95a4-2a3952ef9f1a' 
               }, 
           { id: 15, title: '餐厅消防安全知识培训', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '63c0fecf-5572-47cc-8eb6-076dd6b332df' 
               }, 
         
           { id: 17, title: '原材料粗加工标准原材料粗加工标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'd86ff2cc-6251-4821-b009-b192979d404c' 
               }, 
         { id: 18, title: '蔬菜、调料及采购部收货验货标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '7ae92c22-ddfc-4ea3-a560-b792903ddbb6' 
               }, 
        
           { id: 19, title: '厨房设备安全操作规范', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'a014fa95-a0c1-4111-8d2a-f16faaacc1a2 ' 
               }, 
           { id: 20, title: '厨房设备清洁保养操作规范', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'bd5e4051-84d2-4351-9dc7-d4706260de4c' 
               }, 
           
           { id: 22, title: '厨房二次生命期贴标要求', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'e341f4ff-3670-48c4-9e4c-dfac673fe1cb' 
               }, 
           { id: 25, title: '烧烤岗位原材料特性问答', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '60992cb6-f4de-487d-b53c-3b60edfecf60' 
               }, 
           { id: 26, title: '生火点碳技能标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'd3432196-911f-47ad-ac37-3edcbe388665' 
               }, 
        { id: 27, title: '阿依来烧烤岗位穿串标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: 'cd6086a3-4d77-4510-9b2a-500a802c8cfb' 
               }, 
        { id: 30, title: '烧烤小料孜然粉碎方法', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '92530ebe-a327-4c71-a65c-929b947d7bd8' 
               }, 
        { id: 32, title: '烧烤岗位流程', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '4fda68d3-a8a0-4d68-8899-3b89e59c9bc0' 
               }, 
        { id: 33, title: '烧烤岗位备餐安排', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                fileId: '87295242-edcf-481f-b7f0-65ab49ec99cb' 
               }, 
        { id: 33, title: '奥城厨房排班表（阿依来特性）', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                  fileId: '543cb61f-509b-4b40-b8d4-9c7b2274d056' 
                 }, 
          { id: 33, title: '红旗路厨房排班表（阿依来特性）', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                    fileId: '6ce21676-b854-4880-b32b-81d0e2caab0b' 
                   }, 
            { id: 33, title: '金钟河厨房排班表（阿依来特性）', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                      fileId: 'c07933f0-7d1f-431a-b097-4bb896619250' 
                     }, 
              { id: 33, title: '烧烤部门盘点表', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                        fileId: '4e4731f5-fbac-455f-b2d0-7a8bcc4acd6e' 
                       }, 
                { id: 33, title: '门店简介及店内活动', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                          fileId: '45f9912d-3424-4797-80d7-0a96b945ccf9' 
                         }, 
                   { id: 1, title: '服务礼仪标准', desc: '基础知识', progress: 87, hours: 5.2, required: true, questions: 48, 
                            fileId: 'bcbd628c-2a41-4f6b-9450-e2271ec16edd' 
                           }, 
    ],
    'P2': [
      { id: 21, title: '高级服务', desc: '精致服务认证证书', progress: 55, hours: 3.2, required: true, questions: 38 },
      { id: 22, title: '顾客投诉处理', desc: '餐厅投诉处理认证证书', progress: 42, hours: 2.8, required: true, questions: 30 },
      { id: 23, title: '团队协作', desc: '团队协作能力认证证书', progress: 25, hours: 1.5, required: false, questions: 20 }
    ]
  },
  'cashier': {
    'P1': [
      { id: 31, title: '收银基础', desc: '收银基础操作认证证书', progress: 80, hours: 4.5, required: true, questions: 40 },
      { id: 32, title: '账务处理', desc: '账务处理认证证书', progress: 60, hours: 3.0, required: true, questions: 35 }
    ]
  }
}
// 岗位数据
const practicePositions = [
  {
    label: '服务员',
    value: 'waiter',
    icon: 'icon-user',
    desc: '餐厅服务与顾客沟通'
  },
  {
    label: '收银员',
    value: 'cashier',
    icon: 'icon-money',
    desc: '收银操作与账务管理'
  },
  {
    label: '保安',
    value: 'security',
    icon: 'icon-shield',
    desc: '餐厅安全与秩序维护'
  }
];

// 级别数据
const practiceLevels = [
  {
    label: 'P1 级别',
    value: 'P1',
    desc: '基础知识 · 入门必备'
  },
  {
    label: 'P2 级别',
    value: 'P2',
    desc: '进阶知识 · 提升服务质量'
  },
  {
    label: 'P3 级别',
    value: 'P3',
    desc: '专业技能 · 解决复杂问题'
  },
  {
    label: 'P4 级别',
    value: 'P4',
    desc: '专家水平 · 指导他人工作'
  }
];

// 题目类型
const questionTypes = {
  SINGLE_CHOICE: 'single_choice',
  MULTIPLE_CHOICE: 'multiple_choice',
  TRUE_FALSE: 'true_false',
  FILL_BLANK: 'fill_blank',
  SHORT_ANSWER: 'short_answer'
};

// 练习详情（包含题目）
const practiceDetails = {
  1001: {
    id: 1001,
    title: '刀工基础训练',
    description: '掌握切菜的基本技巧，提高刀工精准度和速度',
    categoryId: 1,
    categoryName: '烹饪技巧',
    totalQuestions: 20,
    duration: 30,
    difficultyLevel: 1,
    difficultyName: '入门',
    questions: [
      {
        id: 10001,
        type: 'single_choice',
        content: '切姜丝时，姜应该沿哪个方向切才能得到最佳的姜丝？',
        options: [
          { id: 'A', text: '顺着姜的纤维方向切' },
          { id: 'B', text: '垂直于姜的纤维方向切' },
          { id: 'C', text: '45度角切' },
          { id: 'D', text: '方向无关紧要' }
        ],
        correctAnswer: 'B',
        explanation: '垂直于姜的纤维方向切能得到长度均匀、不易断的姜丝。'
      },
      {
        id: 10002,
        type: 'multiple_choice',
        content: '以下哪些是切菜前刀具准备的正确做法？',
        options: [
          { id: 'A', text: '确保刀具锋利' },
          { id: 'B', text: '使用潮湿的刀' },
          { id: 'C', text: '选择合适大小的刀具' },
          { id: 'D', text: '确保切菜板稳固不滑动' }
        ],
        correctAnswer: ['A', 'C', 'D'],
        explanation: '切菜前应确保刀具锋利、大小合适，并且切菜板稳固。潮湿的刀会增加危险性。'
      },
      {
        id: 10003,
        type: 'true_false',
        content: '切菜时，手指应该完全伸直以远离刀刃。',
        correctAnswer: false,
        explanation: '切菜时，应该将手指弯曲成"猫爪"形状，指尖向内收，以保护指尖不被切到。'
      }
      // 更多题目...
    ]
  },
  // 更多练习详情...
};

// 练习记录
const practiceRecords = [
  {
    id: 3001,
    practiceId: 1001,
    practiceTitle: '刀工基础训练',
    categoryName: '烹饪技巧',
    score: 92,
    correctCount: 18,
    totalCount: 20,
    duration: 28, // 分钟
    completeTime: '2023-11-05 14:58',
    difficultyLevel: 1,
    difficultyName: '入门'
  },
  {
    id: 3002,
    practiceId: 1002,
    practiceTitle: '火候掌控技巧',
    categoryName: '烹饪技巧',
    score: 84,
    correctCount: 21,
    totalCount: 25,
    duration: 32,
    completeTime: '2023-11-08 10:47',
    difficultyLevel: 2,
    difficultyName: '基础'
  },
  {
    id: 3003,
    practiceId: 1004,
    practiceTitle: '中式面点制作技巧',
    categoryName: '菜品知识',
    score: 95,
    correctCount: 21,
    totalCount: 22,
    duration: 29,
    completeTime: '2023-11-02 09:49',
    difficultyLevel: 3,
    difficultyName: '进阶'
  },
  {
    id: 3004,
    practiceId: 1006,
    practiceTitle: '顾客投诉处理',
    categoryName: '服务礼仪',
    score: 80,
    correctCount: 12,
    totalCount: 15,
    duration: 18,
    completeTime: '2023-11-12 14:08',
    difficultyLevel: 3,
    difficultyName: '进阶'
  }
  // 更多记录...
];

// API调用处理函数
const practiceMock = {
  // 获取练习统计数据
  'GET /api/practice/stats': (data) => {
    return {
      code: 0,
      message: 'success',
      data: practiceStats
    };
  },

    // 获取岗位列表
    'GET /api/practice/positions': () => {
      return {
        code: 0,
        message: 'success',
        data: practicePositions
      };
    },
    'GET /api/practice/levels': () => {
      return {
        code: 0,
        message: 'success',
        data: practiceLevels
      };
    },
  // 获取推荐练习
  'GET /api/wechat/practice/recommend': () => {
    return {
      code: 0,
      message: 'success',
      data: practiceRecommend
    };
  },
  // 获取练习列表（支持分类筛选）
  'GET /api/wechat/practice/list': (data) => {
    const { position = 27, level = 16 } = data;
    
    // 获取对应岗位和级别的练习数据
    let courseList = [];
    if (practiceList[position] && practiceList[position][level]) {
      courseList = practiceList[position][level];
    }
    return {
      code: 0,
      message: 'success',
      data: courseList
    };
  },
  
  // 获取练习详情（包含题目）
  'GET /api/practice/detail': (data) => {
    const practiceId = parseInt(data.id);
    const detail = practiceDetails[practiceId];
    
    if (detail) {
      return {
        code: 0,
        message: 'success',
        data: detail
      };
    } else {
      return {
        code: 404,
        message: '练习不存在',
        data: null
      };
    }
  },
  
  // 提交练习答案
  'POST /api/practice/submit': (data) => {
    const { practiceId, answers, duration } = data;
    
    // 模拟评分计算
    const questionCount = practiceDetails[practiceId]?.questions.length || 20;
    const correctCount = Math.floor(Math.random() * 5) + (questionCount - 5); // 随机生成一个接近满分的正确数
    const score = Math.round((correctCount / questionCount) * 100);
    
    const result = {
      practiceId,
      score,
      correctCount,
      totalCount: questionCount,
      duration,
      wrongQuestions: [] // 实际应用中这里会包含错题信息
    };
    
    return {
      code: 0,
      message: 'success',
      data: result
    };
  },

  // 获取练习记录
  'GET /api/practice/records': (data) => {
    return {
      code: 0,
      message: 'success',
      data: practiceRecords
    };
  }
};

module.exports = {
  practiceMock
}; 