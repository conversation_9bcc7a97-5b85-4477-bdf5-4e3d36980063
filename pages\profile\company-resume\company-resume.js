import { getUserInfo, getCompanyInfo, uploadResume } from '../../../api/user';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 0, // 导航栏高度
    totalNavHeight: 0, // 总导航高度
    isIOS: false, // 是否为iOS设备
    
    // 用户信息
    userInfo: {
      realName: '',
      phone: '',
      idNumber: ''
    },
    
    // 企业信息
    companyName: '',
    companyLogo: '',
    position: '',
    joinDate: '',
    
    // 电子简历
    resumeUrl: '',
    resumeSize: '',
    
    // 工作经历
    workExperience: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
    // 获取用户信息
    this.getUserInfo();
    
    // 获取企业信息
    this.getCompanyInfo();
    
    // 模拟获取工作经历数据
    this.getMockWorkExperience();
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.switchTab({
          url: '/pages/profile/index'
        });
      }
    });
  },
  
  /**
   * 获取用户信息
   */
  getUserInfo() {
    const openId = wx.getStorageSync('openId');
    if (!openId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    // 调用获取用户信息API
    getUserInfo({openId: openId}).then(res => {
      if (res && res.userInfo) {
        this.setData({
          userInfo: res.userInfo
        });
      }
    }).catch(err => {
      console.error('获取用户信息失败:', err);
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },
  
  /**
   * 获取企业信息
   */
  getCompanyInfo() {
    const openId = wx.getStorageSync('openId');
    if (!openId) return;
    
    // 调用获取企业信息API
    getCompanyInfo({openId: openId}).then(res => {
      if (res && res.company) {
        this.setData({
          companyName: res.company.name || '',
          companyLogo: res.company.logo || '',
          position: res.company.position || '',
          joinDate: res.company.joinDate || '',
          resumeUrl: res.company.resumeUrl || '',
          resumeSize: res.company.resumeSize || ''
        });
      }
    }).catch(err => {
      console.error('获取企业信息失败:', err);
    });
  },
  
  /**
   * 模拟获取工作经历数据
   */
  getMockWorkExperience() {
    // 这里使用模拟数据，实际开发中应该从API获取
    const workExperience = [
      {
        title: '厨师长',
        date: '2022.05 - 至今',
        description: '负责菜品研发和厨房管理，培训新员工，制定食品安全标准'
      },
      {
        title: '副厨师长',
        date: '2020.03 - 2022.05',
        description: '协助厨师长进行日常厨房管理，负责部分特色菜品制作'
      },
      {
        title: '厨师',
        date: '2018.06 - 2020.03',
        description: '负责中式菜品烹饪，确保食品质量和标准'
      }
    ];
    
    this.setData({
      workExperience: workExperience
    });
  },
  
  /**
   * 查看电子简历
   */
  viewResume() {
    if (!this.data.resumeUrl) {
      wx.showToast({
        title: '暂无简历文件',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '打开中...',
      mask: true
    });
    
    wx.downloadFile({
      url: this.data.resumeUrl,
      success: res => {
        if (res.statusCode === 200) {
          const filePath = res.tempFilePath;
          wx.openDocument({
            filePath: filePath,
            showMenu: true,
            success: () => {
              console.log('打开文档成功');
            },
            fail: err => {
              console.error('打开文档失败:', err);
              wx.showToast({
                title: '打开文档失败',
                icon: 'none'
              });
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '下载文档失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('下载文档失败:', err);
        wx.showToast({
          title: '下载文档失败',
          icon: 'none'
        });
      }
    });
  },
  
  /**
   * 上传电子简历
   */
  uploadResume() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['pdf', 'doc', 'docx'],
      success: res => {
        const tempFilePath = res.tempFiles[0].path;
        const fileName = res.tempFiles[0].name;
        const fileSize = (res.tempFiles[0].size / 1024).toFixed(2) + 'KB';
        
        wx.showLoading({
          title: '上传中...',
          mask: true
        });
        
        // 调用上传简历API
        const openId = wx.getStorageSync('openId');
        if (!openId) {
          wx.hideLoading();
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }
        
        // 上传文件
        wx.uploadFile({
          url: uploadResume,
          filePath: tempFilePath,
          name: 'resume',
          formData: {
            openId: openId,
            fileName: fileName
          },
          success: res => {
            const data = JSON.parse(res.data);
            if (data.success) {
              this.setData({
                resumeUrl: data.resumeUrl,
                resumeSize: fileSize
              });
              
              wx.showToast({
                title: '上传成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: data.message || '上传失败',
                icon: 'none'
              });
            }
          },
          fail: err => {
            console.error('上传简历失败:', err);
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            });
          },
          complete: () => {
            wx.hideLoading();
          }
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新，重新获取数据
    this.getUserInfo();
    this.getCompanyInfo();
    
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的企业履历 - 餐烤餐考',
      path: '/pages/profile/index'
    };
  }
}) 