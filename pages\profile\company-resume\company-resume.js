import { getUserInfo, getCompanyInfo, uploadResume } from '../../../api/user';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
    // 获取用户信息
    this.getCompanyInfo();
    
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.switchTab({
          url: '/pages/profile/index'
        });
      }
    });
  },
  
  /**
   * 获取用户信息
   */
  getUserInfo() {
    const openId = wx.getStorageSync('openId');
    if (!openId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    // 调用获取用户信息API
    getUserInfo({openId: openId}).then(res => {
      if (res && res.userInfo) {
        this.setData({
          userInfo: res.userInfo
        });
      }
    }).catch(err => {
      console.error('获取用户信息失败:', err);
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },
  
  /**
   * 获取企业信息
   */
  getCompanyInfo() {
    const openId = wx.getStorageSync('openId');
    if (!openId) return;
    
    // 调用获取企业信息API
    getCompanyInfo({openId: openId}).then(res => {
      if (res && res.company) {
        this.setData({
          companyName: res.company.name || '',
          companyLogo: res.company.logo || '',
          position: res.company.position || '',
          joinDate: res.company.joinDate || '',
          resumeUrl: res.company.resumeUrl || '',
          resumeSize: res.company.resumeSize || ''
        });
      }
    }).catch(err => {
      console.error('获取企业信息失败:', err);
    });
  },
  
  /**
   * 模拟获取工作经历数据
   */
  getMockWorkExperience() {
    // 这里使用模拟数据，实际开发中应该从API获取
    const workExperience = [
      {
        title: '厨师长',
        date: '2022.05 - 至今',
        description: '负责菜品研发和厨房管理，培训新员工，制定食品安全标准'
      },
      {
        title: '副厨师长',
        date: '2020.03 - 2022.05',
        description: '协助厨师长进行日常厨房管理，负责部分特色菜品制作'
      },
      {
        title: '厨师',
        date: '2018.06 - 2020.03',
        description: '负责中式菜品烹饪，确保食品质量和标准'
      }
    ];
    
    this.setData({
      workExperience: workExperience
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新，重新获取数据
    this.getUserInfo();
    this.getCompanyInfo();
    
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
}) 