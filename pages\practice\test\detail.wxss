.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 聊天容器 */
.chat-container {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

.chat-list {
  padding-bottom: 30rpx;
}

/* 系统消息 */
.system-message {
  text-align: center;
  margin: 20rpx 0;
  padding: 10rpx 0;
}

.system-text {
  display: inline-block;
  padding: 10rpx 30rpx;
  background-color: rgba(0, 0, 0, 0.05);
  color: #999999;
  font-size: 24rpx;
  border-radius: 8rpx;
}

/* 系统消息项 */
.system-message-item {
  text-align: center;
  margin: 20rpx auto;
  padding: 10rpx 30rpx;
  color: #999999;
  font-size: 24rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
}

/* 消息项 */
.message-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}

/* AI消息 */
.ai-message {
  justify-content: flex-start;
}

/* 用户消息 */
.user-message {
  justify-content: flex-end;
}

.avatar-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background-color: #a18cd1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-icon text {
  font-size: 45rpx;
  color: #ffffff;
}

.avatar-icon image {
  width: 100%;
  height: 100%;
}

/* 消息内容 */
.message-content {
  max-width: 70%;
  margin: 0 20rpx;
  display: flex;
  flex-direction: column;
}

/* 消息气泡 */
.message-bubble {
  padding: 20rpx 24rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.5;
  position: relative;
  word-break: break-all;
}

/* 语音消息气泡 */
.voice-bubble {
  display: flex;
  align-items: center;
  min-width: 120rpx;
}

.voice-icon {
  margin-right: 10rpx;
  font-size: 50rpx;
  display: flex;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* AI消息气泡 */
.ai-message .message-bubble {
  background-color: #ffffff;
  color: #333333;
  border-top-left-radius: 0;
}

/* 用户消息气泡 */
.user-message .message-bubble {
  background-color: #a18cd1;
  color: #ffffff;
  border-top-right-radius: 0;
}

/* 消息时间 */
.message-time {
  font-size: 22rpx;
  color: #999999;
  margin-top: 10rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999999;
  font-size: 24rpx;
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
}

.loading-dots {
  display: flex;
  align-items: center;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #a18cd1;
  margin: 0 8rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* 输入区域容器 */
.input-container {
  position: fixed;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

/* 输入类型切换按钮 */
.input-type-switch {
  width: 70rpx;
  height: 70rpx;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 50rpx;
  color: #999999;
}

/* 输入框容器 */
.input-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  padding: 0 20rpx;
  margin: 0 20rpx;
  position: relative;
}

/* 文本输入框 */
.input-textarea {
  flex: 1;
  height: 70rpx;
  min-height: 70rpx;
  max-height: 140rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 15rpx 20rpx 15rpx 0;
  box-sizing: border-box;
}

/* 输入工具栏 */
.input-tools {
  display: flex;
  align-items: center;
}

/* 表情按钮 */
.emoji-button {
  font-size: 50rpx;
  color: #999999;
  margin-right: 20rpx;
}

/* 发送按钮 */
.send-button {
  width: 60rpx;
  height: 60rpx;
  /* border-radius: 50%; */
  /* background-color: #e0e0e0; */
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  /* color: #ffffff; */
  transition: all 0.3s;
}

.send-button.active {
  background-color: #a18cd1;
}

/* 语音输入框 */
.voice-input-box {
  flex: 1;
  margin: 0 20rpx;
}

/* 语音按钮 */
.voice-button {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  transition: all 0.3s;
}

.voice-button.recording {
  background-color: #e0e0e0;
}

/* 录音提示蒙层 */
.recording-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s;
}

.recording-mask.cancelling {
  background-color: rgba(241, 67, 67, 0.5);
}

/* 录音指示器 */
.recording-indicator {
  width: 300rpx;
  height: 300rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 录音图标 */
.recording-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #a18cd1;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
}

.recording-icon.cancel-icon {
  background-color: #f14343;
}

/* 录音波纹 */
.recording-waves {
  position: absolute;
  width: 100%;
  height: 100%;
}

.wave {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4rpx solid #a18cd1;
  border-radius: 50%;
  animation: wave 1.5s infinite;
}

@keyframes wave {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

/* 取消图标 */
.cancel-icon-inner {
  font-size: 60rpx;
  color: #ffffff;
}

/* 录音文字 */
.recording-text {
  font-size: 28rpx;
  color: #ffffff;
  margin-bottom: 20rpx;
}

/* 取消提示 */
.cancel-tip {
  font-size: 24rpx;
  color: #cccccc;
}

/* 录音时间 */
.recording-time {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #ffffff;
}

/* 评估结果 */
.evaluation-result {
  margin-top: 10rpx;
  font-size: 24rpx;
}

/* 成绩 */
.score {
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.score.correct {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.score.incorrect {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
} 