<!-- components/cert-detail-popup/cert-detail-popup.wxml -->
<wxs module="dateFormatter">
  function formatDate(dateStr) {
    if (!dateStr) return '';
    if (dateStr.indexOf(' ') >= 0) {
      return dateStr.split(' ')[0];
    }
    return dateStr;
  }
  module.exports = {
    format: formatDate
  };
</wxs>

<view class="cert-detail" bindlongpress="handleLongPress">
  <view class="theme-gold">
    <!-- <view class="border-frame"></view>
    <view class="corner-decoration top-left"></view>
    <view class="corner-decoration top-right"></view>
    <view class="corner-decoration bottom-left"></view>
    <view class="corner-decoration bottom-right"></view> -->
    <image
      src="/static/images/certificates_bg.jpg"
      mode="scaleToFill" class="bg"
    />
    <view class="header">
      <!-- <image src="{{enterpriseLogo}}" class="logo"></image>
      <view class="title">餐考认证</view>
      <view class="subtitle">CERTIFICATION</view> -->
      <view class="name">{{certData.employeeName}}</view>
      <view class="course-name">恭喜您已顺利通过餐烤餐考《{{certData.certificateName}}》课程学习，考核成绩合格，特发此证。</view>
      <view class="level">
        <view class="text">《{{certData.certificateName}}》   {{certData.positionNameInfo.name}}（{{certData.positionLevelInfo.name}}级）
          </view>
        <image
          src="/static/images/name_bg.png"
          mode="scaleToFill"
        class="level_bg"/>

      </view>
      <view class="certificate-number">证书编号：{{certData.certificateNo }}</view>

    </view>

    <!-- <view class="decorative-line"></view>

    <view class="content">
      <view class="recipient-name">{{certData.employeeName}}</view>
      <view class="certificate-text">
        <text>已完成《{{certData.certificateName}}》课程学习，\n考核成绩合格，特发此证。</text>
      </view>
    </view> 
        
    <view class="decorative-line"></view>-->

    <view class="footer">
        <view class="certificate">
          <view class="item">
            <view class="text">餐考认证</view>

            <image
              src="/static/images/jiang.png"
              mode="scaleToFill" class="certificate_bg"
            />
          </view>
          <view class="item">
             <image
              src="{{logo}}" class="logo"
              mode="scaleToFill"
            />
            <image
              src="/static/images/jiang.png"
              mode="scaleToFill" class="certificate_bg"
            />
           
          </view>
        </view>
        <view class="issuer-info">
          <!-- <view class="issuer-name">{{certData.organization || '餐烤餐考培训中心'}}</view> -->
          <view class="date-range">
            <view class="date-item">
              <text class="date-label">颁发日期：</text>
              <text>{{dateFormatter.format(certData.obtainTime)}}</text>
          </view>
            <view class="date-item">
              <text class="date-label">有效期至：</text>
              <text>{{dateFormatter.format(certData.validUntil)}}</text>
          </view>
          </view>
        </view>
        <!-- <view class="seal">
          <image src="{{logo}}" class="seal-image" mode="widthFix"></image>
        </view> -->
        <!-- <view class="certificate-number">证书编号：{{certData.certificateNo }}</view> -->
    </view>

    <!-- <view class="watermark">认证证书</view> -->
  </view>
  
  <!-- 提示信息 -->
  <view class="tips-popup {{showTips ? 'show' : ''}}" wx:if="{{showTips}}">
    {{tipsMsg}}
  </view>
  
  <!-- 保存中加载状态 -->
  <view class="saving-overlay" wx:if="{{saving}}">
    <view class="saving-content">
      <view class="saving-icon"></view>
      <text>证书保存中...</text>
    </view>
  </view>
  
  <!-- 离屏Canvas，用于生成图片 -->
  <canvas type="2d" id="certCanvas" class="cert-canvas"></canvas>
</view> 