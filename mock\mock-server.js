/**
 * Mock数据服务
 * 在开发阶段模拟后端接口返回数据
 */
import userMock from './modules/user';
import certificateMock from './modules/certificate';
import practiceMock from './modules/practice';
import promotionMock from './modules/promotion';
import badgeMock from './modules/badge';
import homeMock from './modules/home';
import masterChatMock from './modules/master-chat';
import examMock from './modules/exam';

// 状态码常量
const CODE = {
  SUCCESS: 0,
  ERROR: 1,
  NOT_FOUND: 404
};

/**
 * 创建标准响应格式
 * @param {any} data 响应数据
 * @param {number} code 状态码
 * @param {string} message 响应消息
 * @returns {Object} 标准响应对象
 */
const createResponse = (data, code = CODE.SUCCESS, message = 'success') => {
  return {
    code,
    message,
    data
  };
};

/**
 * 创建Mock处理函数
 * @param {Function} handler 处理函数
 * @returns {Function} 标准化的处理函数
 */
const createMockHandler = (handler) => {
  return (params) => {
    try {
      const result = handler(params);
      
      // 如果返回的不是标准响应格式，则进行包装
      if (typeof result === 'object' && result !== null && 'code' in result && 'data' in result) {
        return result;
      } else {
        return createResponse(result);
      }
    } catch (err) {
      console.error('[MOCK] 处理异常:', err);
      return createResponse(null, CODE.ERROR, err.message || '处理异常');
    }
  };
};

/**
 * 注册Mock API
 * @param {Object} mockModules Mock模块对象
 * @returns {Object} 注册后的Mock API
 */
const registerMockApis = (mockModules) => {
  const apis = {};
  
  // 合并所有模块的Mock API
  Object.values(mockModules).forEach(module => {
    if (!module) return;
    
    Object.entries(module).forEach(([key, handler]) => {
      if (typeof handler === 'function') {
        apis[key] = createMockHandler(handler);
      }
    });
  });
  
  return apis;
};

// 注册所有Mock API
const mockApis = registerMockApis({
  userMock,
  certificateMock,
  practiceMock,
  promotionMock,
  badgeMock,
  homeMock,
  masterChatMock,
  examMock
});

export default mockApis; 