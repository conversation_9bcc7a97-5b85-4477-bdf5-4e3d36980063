.container {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
  height: 100vh;
  overflow: hidden;
  padding-bottom:0px !important;
}

/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  background-color: #f8f8f8;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  background-color: #f8f8f8;
}



/* 考试进度 */
.exam-countdown {
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 2rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  z-index: 9;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
}

.exam-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.countdown-box {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.countdown-box .iconfont {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.countdown-warning {
  color: #ff4d4f;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.progress-bar {
  height: 6rpx;
  background-color: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(to right, #5a86ff, #46a6ff);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 提交试卷按钮样式 */
.end-exam-btn {
  background-color: #ff6b6b;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  text-align: center;
}

.custom-nav {
  width: 100%;
  background-color: #f8f8f8;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}


.nav-content {
  overflow: hidden;
  text-align: center;
}
