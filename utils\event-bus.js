/**
 * 简单的事件总线实现
 * 用于组件间通信
 */
class EventBus {
  constructor() {
    this.events = {};
  }
  
  /**
   * 订阅事件
   * @param {String} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(eventName, callback) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    
    this.events[eventName].push(callback);
  }
  
  /**
   * 取消订阅
   * @param {String} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(eventName, callback) {
    if (!this.events[eventName]) {
      return;
    }
    
    if (!callback) {
      // 如果没有提供回调，则移除所有该事件的监听器
      this.events[eventName] = [];
      return;
    }
    
    this.events[eventName] = this.events[eventName].filter(
      cb => cb !== callback
    );
  }
  
  /**
   * 触发事件
   * @param {String} eventName - 事件名称
   * @param {Any} data - 传递的数据
   */
  emit(eventName, data) {
    if (!this.events[eventName]) {
      return;
    }
    
    this.events[eventName].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`事件${eventName}处理错误:`, error);
      }
    });
  }
  
  /**
   * 只订阅一次事件
   * @param {String} eventName - 事件名称
   * @param {Function} callback - 回调函数
   */
  once(eventName, callback) {
    const onceWrapper = data => {
      callback(data);
      this.off(eventName, onceWrapper);
    };
    
    this.on(eventName, onceWrapper);
  }
  
  /**
   * 清空所有事件
   */
  clear() {
    this.events = {};
  }
}

// 导出事件总线实例
export default new EventBus(); 