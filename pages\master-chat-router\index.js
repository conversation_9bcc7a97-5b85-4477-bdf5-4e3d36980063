Page({
  data: {
    // 餐烤师URL配置
    masterUrls: {
      frontDesk: 'https://dify-cankao.dev.lingmiaoai.com/chat/55dLh2po8AqIuktu',  // 前厅餐烤师
      kitchen: 'https://dify-cankao.dev.lingmiaoai.com/chat/iAgUb9TXMUyTWGWJ'     // 后厨餐烤师
    },
    // 显示加载中状态
    isLoading: true,
    // 错误信息
    errorMsg: '',
    // 当前的URL
    currentUrl: ''
  },
  
  // 是否已执行过查询用户信息
  hasExecuted: false,
  
  onLoad: function() {
    // 页面加载后立即获取用户类型并跳转
    this.getUserPositionType();
  },

  onShow: function() {
  },
  
  // 获取用户岗位类型
  getUserPositionType: function() {
    
    
    // 直接从缓存中读取用户信息
    try {
      const userInfo = wx.getStorageSync('userInfo');
      
      
      let positionTypeName = '';
      if (userInfo && userInfo.positionTypeName) {
        positionTypeName = userInfo.positionTypeName;
      } else {
        positionTypeName = '前厅'; // 默认前厅
      }
      
      // 直接打开URL
      this.openMasterUrl(positionTypeName);
    } catch (error) {
      // 发生错误时使用默认URL
      this.openMasterUrl('前厅');
    }
  },
  
  // 直接打开URL
  openMasterUrl: function(positionTypeName) {
    let masterUrl = '';
    
    // 判断岗位类型，设置对应的URL
    if ( positionTypeName === '后厨') {
      masterUrl = this.data.masterUrls.kitchen;
    } else {
      // 默认前厅
      masterUrl = this.data.masterUrls.frontDesk;
    }
    
    // 直接跳转到webview
    wx.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(masterUrl)}`,
      fail: (err) => {
        console.error('master-chat-router: 跳转失败', err);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },
}); 