<!-- components/message-item/message-item.wxml -->
<view class="message-item {{message.type === 'ai' ? 'ai-message' : 'user-message'}}">
  <!-- AI消息 -->
  <block wx:if="{{message.type === 'ai'}}">
    <view class="avatar-icon">
      <!-- <view class="aiIcon"> -->
        <image src="{{message.avatar||logo}}" mode="aspectFill"></image>
      <!-- </view> -->
    </view>
    <view class="message-content {{isRecordDetail&&message.answerType=='analysis'?(message.result?'success':'error'):''}}">
      <view class="message-bubble">

        <view wx:if="{{isRecordDetail&&message.answerType=='analysis'}}" class="tag">{{message.result}}
          <text class="iconfont {{message.result?'icon-check':'icon-cuowu'}}"></text>
        </view>
        <text wx:if="{{!message.isAudioOnly}}">{{message.content}}</text>
        <!-- 流式输出时的打字机光标 -->
        <text wx:if="{{message.isStreaming}}" class="typing-cursor">|</text>
        <!-- 操作按钮区域 -->
        <view class="question-container" wx:if="{{!isRecordDetail&&!message.isStreaming}}">
          <!-- 音频播放控件 - 只在流式输出完成后显示 -->
          <view class="audio-player {{message.isPlaying ? 'playing' : ''}}" wx:if="{{message.content && !message.isStreaming}}">
            <view class="audio-play-btn" bindtap="onPlayMessageAudio" data-type="ai" data-index="{{message.index}}">
              <text class="iconfont {{message.isPlaying ? 'icon-tingzhi' : 'icon-bofan'}}"></text>
               <view class="play-text">{{message.isPlaying?'暂停语音':'播放语音'}}</view>
            </view>
            <!-- <view class="audio-waveform {{message.content.length < 20 ? 'short-text' : (message.content.length > 50 ? 'long-text' : 'medium-text')}}">
              <view class="wave-line"></view>
              <view class="wave-line"></view>
              <view class="wave-line"></view>
              <view class="wave-line"></view>
              <view class="wave-line"></view>
            </view> -->
          </view>

          <!-- 练习页面特有按钮 - 开始练习 -->
          <view class="practice-btn start-practice-btn"
                wx:if="{{pageType === 'practice' && message.showStartButton && !isPracticeStarted}}"
                bindtap="onStartPractice">
            <text>开始练习</text>
          </view>

          <!-- 练习页面特有按钮 - 下一题 -->
          <view class="practice-btn next-question {{isNextBtnDisabled ? 'btn-disabled' : ''}}"
                wx:if="{{pageType === 'practice' && message.showNextQuestion}}"
                catchtap="onGetNextQuestion"
                data-disabled="{{isNextBtnDisabled}}">
            <text>下一题</text>
          </view>
        </view>
      </view>
      <!-- <text class="message-time">{{message.time}}</text> -->
    </view>
  </block>

  <!-- 用户消息 -->
  <block wx:if="{{message.type === 'user'}}">
    <view class="message-content">
      <view class="message-bubble">
        <text>{{message.content}}</text>
        <view class="question-container question-container-user" wx:if="{{message.isAudio}}">
          <view class="audio-player {{message.isPlaying ? 'playing' : ''}}" wx:if="{{message.content}}">
            <view class="audio-play-btn" bindtap="onPlayMessageAudio" data-type="user" data-is-audio="{{message.isAudio}}" data-index="{{message.index}}">
              <text class="iconfont {{message.isPlaying ? 'icon-Pause' : 'icon-playfill'}}"></text>
            </view>
            <view class="audio-waveform {{message.content.length < 20 ? 'short-text' : (message.content.length > 50 ? 'long-text' : 'medium-text')}}">
              <view class="wave-line"></view>
              <view class="wave-line"></view>
              <view class="wave-line"></view>
              <view class="wave-line"></view>
              <view class="wave-line"></view>
            </view>
          </view>
        </view>
      </view>
      <!-- <text class="message-time">{{message.time}}</text> -->
    </view>
    <view class="avatar-icon">
      <image src="{{avatar}}" mode="aspectFill"></image>
    </view>
  </block>

  <!-- 系统消息特殊样式 -->
  <block wx:if="{{message.type === 'system'}}">
    <view class="system-message-item">
      <text>{{message.content}}</text>
    </view>
  </block>
</view>