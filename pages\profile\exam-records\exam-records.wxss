.container {
}
#examStats{
  overflow: hidden;
}


.filter-button {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  /* background: linear-gradient(135deg, rgba(161, 140, 209, 0.1) 0%, rgba(251, 194, 235, 0.1) 100%); */
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
  /* border: 2rpx dashed rgba(126, 87, 194, 0.3); */
  border: 2rpx dashed #fff;
  transition: all 0.3s;
  line-height: normal;
  height: 50rpx;
  width: auto;
  justify-content: center;
  padding:0 10rpx;
}

.filter-button::after {
  border: none;
}

.filter-button text {
  margin: 0 8rpx;
}

.filter-button .iconfont {
  font-size: 32rpx;
}

.records-container {
  flex: 1;
  /* height: calc(100vh - 400rpx); */
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* 确保scroll-view占满容器 */
.records-container scroll-view {
  height: 100% !important;
  width: 100%;
  transition: opacity 0.2s ease; /* 添加过渡效果 */
}

/* 切换时的样式 */
.records-container scroll-view.switching {
  opacity: 0.8;
}

.exam-card {
  background: #fff;
  border-radius: 24rpx;
  margin: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.exam-status {
  position: absolute;
  right: 32rpx;
  top: 32rpx;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  border-radius: 24rpx;
}

.exam-status.status-pass {
  background-color: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.exam-status.status-fail {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.exam-header {
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.exam-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.exam-date {
  font-size: 24rpx;
  color: #999;
}

.exam-content {
  padding: 32rpx;
}

.score-section {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.exam-score {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  font-size: 36rpx;
  font-weight: bold;
  background-color: #a18cd1;
  color:#fff;

}

.exam-score.score-high {
  background-color: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.exam-score.score-medium {
  background-color: rgba(234, 179, 8, 0.1);
  color: #eab308;
}

.exam-score.score-low {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.exam-info {
  flex: 1;
}

.info-label {
  color: #333;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 26rpx;
  color: #999;
}

.detail-row {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  color: #666;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 160rpx;
  color: #999;
}



.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
  color: #999;
}

.empty-state .iconfont {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  color: #ddd;
}

.empty-state .empty-tip {
  font-size: 24rpx;
  margin-top: 16rpx;
  color: #a18cd1;
}

/* 加载更多提示样式 */
.load-more {
  padding: 30rpx;
  text-align: center;
}

.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #a18cd1;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 省略号加载动画 */
.loading-dots {
  display: flex;
  margin-left: 8rpx;
}

.loading-dots .dot {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background-color: #a18cd1;
  margin: 0 2rpx;
  animation: loading-dot 1.4s infinite ease-in-out;
}

.loading-dots .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots .dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dots .dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loading-dot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
} 