// pages/promotion/list.js
import {  getPositionCertification, applyExamReview } from '../../api/promotion'
const authBehavior = require('../../behaviors/auth-behavior')
import { postPosition } from '../../api/practice'
import { getPositionList } from '../../api/user'

Page({
  // 使用授权行为
  behaviors: [authBehavior],

  data: {
    currentModule: null,
    examModules: [],
    isLoading: false,
    showApplyPopup: false,
    employeeInfo:{},
    nextLevel:'',
    positionDrawerVisible: false,
    positions:[],
    currentPosition:'',
    positionValue:''

  },

  onLoad: function() {
    wx.setStorageSync('promotionPositionId',null)
    this.init()
  },

  onShow: function() {
    this.init()
  },
  async init(){
     // 检查授权状态，并根据授权结果决定是否加载数据
     const authResult = this.checkAuthStatus();

     // 只有在已授权的情况下才加载数据
     if (authResult) {
       // 如果需要重新加载数据，可以调用 initData 方法
        getPositionList().then(res=>{
          this.setData({
            positions:res.rows
          })
        })
      await this.setupCurrentPositionAndLevel()
      this.initData()
     }

    // 更新TabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: wx.getStorageSync('selectedTab')});
    }

    // 检查是否需要从报告页重定向过来
    const needRedirect = wx.getStorageSync('needRedirectToPromotion');
    if (needRedirect) {
      // 清除标记
      wx.removeStorageSync('needRedirectToPromotion');

      // 确保当前页面是promotion/list
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      if (currentPage.route !== 'pages/promotion/list') {
        wx.reLaunch({
          url: '/pages/promotion/list'
        });
      }
    }
  },
  // 设置当前岗位和级别
  setupCurrentPositionAndLevel: function() {
    // 优先从缓存获取练习页面选择的岗位和级别
    let promotionPositionId = wx.getStorageSync('promotionPositionId');
      // 如果有缓存的练习页面岗位选择
    if (promotionPositionId && this.data.positions.length > 0) {
      const currentPosition = this.data.positions.find(position => position.id === promotionPositionId);
      if (currentPosition) {
        this.setData({
          currentPosition: currentPosition.name,
          positionValue: currentPosition.id
        });
      } else {
        // 如果找不到缓存的岗位，回退到用户默认岗位
        this.useDefaultPosition();
      }
    } else {
      // 没有缓存的练习页面岗位选择，使用默认岗位
      this.useDefaultPosition();
    }
  },
  // 使用用户默认岗位
  useDefaultPosition: function() {
    this.setData({
      currentPosition: wx.getStorageSync('userInfo').positionName,
      positionValue: wx.getStorageSync('userInfo').positionId,
    });
    wx.setStorageSync('promotionPositionId',wx.getStorageSync('userInfo').positionId);
  },
  
  // 选择岗位
  selectPosition: function(e) {
    const position = e.currentTarget.dataset.position;
    // postPosition({
    //   openId: wx.getStorageSync('openId'),
    //   positionId: position.id
    // }).then(res => {
    //   if(res.success){
        // 保存选择到专用存储
        wx.setStorageSync('promotionPositionId', position.id);
        
        this.setData({
          currentPosition: position.name,
          positionValue: position.id
        });
        this.closePositionDrawer();
        // 重新获取列表
        this.initData();
      },
    // })


  // 显示岗位选择抽屉
  showPositionDrawer: function() {
    this.setData({
      positionDrawerVisible: true,
      pageScroll: false // 禁用页面滚动
    });

    // 禁用页面滚动
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });

    // 设置页面样式，禁止滚动
    wx.setPageStyle({
      style: {
        overflow: 'hidden'
      }
    });
  },

  // 关闭岗位选择抽屉
  closePositionDrawer: function() {
    this.setData({
      positionDrawerVisible: false,
      pageScroll: true // 恢复页面滚动
    });

    // 恢复页面滚动
    wx.setPageStyle({
      style: {
        overflow: 'auto'
      }
    });
  },


  // Tab项被点击时触发
  onTabItemTap: function(item) {
    // 更新选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: wx.getStorageSync('selectedTab')});
    }
  },

  async initData() {
    // 返回列表清空数据，避免数据缓存
    this.setData({
      isLoading: true,
      // examModules:null,
      // employeeInfo:null,
    });

    try {
      if(wx.getStorageSync('isIdentityVerified')){
        // 使用新的API获取岗位认证信息，openId会自动添加到请求头中
        const result = await getPositionCertification({positionId:this.data.positionValue});

        // 修复数据结构判断逻辑，API直接返回了data部分的内容
        if (!result || !result.exams || !Array.isArray(result.exams)) {
          throw new Error('获取数据失败');
        }
        // 更新员工信息
        const employeeInfo = result.employee || {};

        // 转换exams数据为examModules格式
        this.setData({
          nextLevel:result.employee.nextLevel
        })
        const examModules = result.exams.map(exam => {
          // 根据status设置buttonType、buttonText和buttonIcon
          let buttonType, buttonText, buttonIcon,disabledText;

          // 1. 待审核 2. 已审核 3. 待申请 4. 已通过 5. 继续考试
          switch (exam.status) {
            case -1:
              buttonType = 'disabled';
              buttonText = '考试';
              buttonIcon = 'icon-edit';
              disabledText = '您未达到考试资格，请继续完成练习再来申请';
              break;
            
            case 0:
            case 3:
              buttonType = 'apply-exam';
              buttonText = '申请';
              buttonIcon = 'icon-paper-plane';
              break;
            case 1:
              buttonType = 'disabled';
              buttonText = '审核中';
              buttonIcon = 'icon-edit';
              disabledText = '考试申请正在审核中，请耐心等待';
              break;
            case 2:
              buttonType = 'take-exam';
              buttonText = '考试';
              buttonIcon = 'icon-edit';
              break;
            case 4:
              buttonType = 'view-report';
              buttonText = '查看报告';
              buttonIcon = 'icon-eye';
              break;
            case 5:
                buttonType = 'take-exam';
                buttonText = '继续考试';
                buttonIcon = 'icon-edit';
                break;
            case 6:
                buttonType = 'view-cert';
                buttonText = '查看证书';
                buttonIcon = 'icon-eye';
                break;
            default:
              buttonType = 'disabled';
              buttonText = '未知';
              buttonIcon = 'icon-question';
          }

          return {
            id: exam.examSubject, // 使用examSubject作为ID
            title: exam.examSubjectName,
            examId:exam.examId,
            buttonType,
            buttonText,
            buttonIcon,
            disabledText,
            status: exam.status,
            examConfig: exam.examConfig,
            employeeInfo:employeeInfo,
            certificationId:exam.certificationId
          };
        });


        this.setData({
          examModules,
          employeeInfo,
          isLoading: false
        });
      }
    } catch (error) {
      console.error('获取考试数据失败：', error);
      wx.showToast({
        title: '加载数据失败，请重试',
        icon: 'none'
      });
      this.setData({
        isLoading: false
      });
    }
  },

  // handleCardClick(e) {
  //   const moduleId = e.currentTarget.dataset.id;
  //   const module = this.data.examModules.find(item => item.id === moduleId);
  //   console.log('点击了模块卡片：', module.title);
  // },

  async handleButtonClick(e) {
    const moduleId = e.currentTarget.dataset.id;
    const module = this.data.examModules.find(item => item.id === moduleId);

    if (!module) return;
    // 如果是禁用状态，直接提示用户
    if (module.buttonType === 'disabled') {
      wx.showToast({
        title: module?.disabledText||'',
        icon: 'none'
      });
      return;
    }

    this.setData({
      currentModule: module
    });

    switch (module.buttonType) {
      case 'take-exam':
        this.goExam(module);
        break;
      case 'view-report':
        const examId = module.examId;
        this.navigateToAuthRequiredPage(`/pages/promotion/report?examId=${examId}`)
        break ;
      case 'apply-exam':
        this.showApplyModal(module);
        break;
      case 'view-cert':
        this.viewCertificate(module);
        break;
      default:
        wx.showToast({
          title: '该功能暂不可用',
          icon: 'none'
        });
    }
  },

  showApplyModal(module) {
    this.setData({
      showApplyPopup: true
    });
  },

  closePopup() {
    this.setData({
      showApplyPopup: false
    });
  },

  async confirmApply() {
    if (!this.data.currentModule) return;

    try {
      // 使用新的API调用申请考试审核接口
      await applyExamReview(this.data.currentModule.id,this.data.currentModule.examConfig.positionName,this.data.currentModule.examConfig.positionLevel); // 传递examSubject作为knowledgeBaseId
      wx.showToast({
        title: '申请成功！考试申请已提交审核',
        icon: 'none',
        duration: 3000
      });
      this.closePopup();
      this.initData(); // 刷新列表
    } catch (error) {
      console.error('申请考试失败：', error);
      wx.showToast({
        title: '申请失败，请重试',
        icon: 'none'
      });
    }
  },

  async goExam(module) {
    // 使用授权导航
    wx.setStorageSync('examInfo', {
      ...module.examConfig,
      ...module.employeeInfo,
      title:module.title,
      nextLevel:this.data.nextLevel
    })
    // 继续考试
    if(module.status==5){
      this.navigateToAuthRequiredPage(`/pages/promotion/exam?examId=${module.examId}&knowledgeBaseId=${module.id}&status=${module.status}`);
    }else{
    // const examConfig = module.examConfig || {};

    this.navigateToAuthRequiredPage(`/pages/promotion/start?title=${module.title}&&knowledgeBaseId=${module.id}`);
    }
  },

  //查看证书
  async viewCertificate(module) {
    try {
      // const certificate = await getCertificate(module.id);
      // 使用授权导航
      this.navigateToAuthRequiredPage('/pages/promotion/certificate/certificate?certId='+module.certificationId);
    } catch (error) {
      console.error('获取证书失败：', error);
      wx.showToast({
        title: '获取证书失败，请重试',
        icon: 'none'
      });
    }
  },
  onHide: function() {
    // 当用户离开当前tab时，清除授权状态
    this.clearAuthStatus();
  },
})
