Component({
    /**
   * 组件选项
   */
    options: {
      styleIsolation: 'apply-shared'  // 使用apply-shared让组件可以使用app.wxss中的样式
    },
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹框
    visible: {
      type: Boolean,
      value: false
    },
    // 证书图片URL
    certificateUrl: {
      type: String,
      value: ''
    },
    // 弹框标题
    title: {
      type: String,
      value: '证书详情'
    },
    // 图片显示模式
    imageMode: {
      type: String,
      value: 'widthFix'
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: false
    },
    // 是否显示分享按钮
    showShareButton: {
      type: Boolean,
      value: false
    },
    // 是否显示保存按钮
    showSaveButton: {
      type: Boolean,
      value: true
    },
    // 是否允许点击遮罩关闭
    maskClosable: {
      type: Boolean,
      value: true
    },
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 错误信息
    error: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    imageLoaded: false,
    imageError: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 遮罩点击事件
     */
    onMaskTap() {
      if (this.properties.maskClosable) {
        this.onClose();
      }
    },

    /**
     * 关闭弹框
     */
    onClose() {
      this.triggerEvent('close');
    },

    /**
     * 图片长按事件
     */
    onImageLongPress() {
      console.log('长按证书图片');
      
      if (!this.properties.certificateUrl) {
        wx.showToast({
          title: '图片地址无效',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 显示操作菜单
      wx.showActionSheet({
        itemList: ['保存图片到相册', '预览图片'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 保存图片到相册
            this.saveImage();
          } else if (res.tapIndex === 1) {
            // 预览图片
            this.previewImage();
          }
        },
        fail: (err) => {
          console.log('用户取消操作', err);
        }
      });

      // 触发长按事件
      this.triggerEvent('longpress', {
        certificateUrl: this.properties.certificateUrl
      });
    },

    /**
     * 图片点击事件
     */
    onImageTap() {
      console.log('点击证书图片');
      this.previewImage();
      
      // 触发点击事件
      this.triggerEvent('imagetap', {
        certificateUrl: this.properties.certificateUrl
      });
    },

    /**
     * 图片加载完成
     */
    onImageLoad() {
      this.setData({
        imageLoaded: true,
        imageError: false
      });
      
      this.triggerEvent('imageload');
    },

    /**
     * 图片加载失败
     */
    onImageError() {
      this.setData({
        imageLoaded: false,
        imageError: true
      });
      
      this.triggerEvent('imageerror');
    },

    /**
     * 重试加载
     */
    onRetry() {
      this.setData({
        imageError: false
      });
      
      this.triggerEvent('retry');
    },

    /**
     * 分享按钮点击
     */
    onShare() {
      this.triggerEvent('share', {
        certificateUrl: this.properties.certificateUrl
      });
    },

    /**
     * 保存按钮点击
     */
    onSave() {
      this.saveImage();
    },

    /**
     * 预览图片
     */
    previewImage() {
      if (!this.properties.certificateUrl) {
        wx.showToast({
          title: '图片地址无效',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      wx.previewImage({
        current: this.properties.certificateUrl,
        urls: [this.properties.certificateUrl],
        success: () => {
          console.log('图片预览成功');
        },
        fail: (err) => {
          console.error('图片预览失败:', err);
          wx.showToast({
            title: '图片预览失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    /**
     * 保存图片到相册
     */
    saveImage() {
      if (!this.properties.certificateUrl) {
        wx.showToast({
          title: '图片地址无效',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      console.log('开始保存证书图片:', this.properties.certificateUrl);

      // 显示加载提示
      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      // 先下载图片到本地
      wx.downloadFile({
        url: this.properties.certificateUrl,
        success: (downloadRes) => {
          console.log('图片下载成功:', downloadRes.tempFilePath);
          
          // 保存图片到相册
          wx.saveImageToPhotosAlbum({
            filePath: downloadRes.tempFilePath,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000
              });
              console.log('证书图片保存成功');
              
              // 触发保存成功事件
              this.triggerEvent('savesuccess', {
                certificateUrl: this.properties.certificateUrl
              });
            },
            fail: (saveErr) => {
              wx.hideLoading();
              console.error('保存图片失败:', saveErr);
              
              // 检查是否是权限问题
              if (saveErr.errMsg.includes('auth')) {
                this.handlePermissionError();
              } else {
                wx.showToast({
                  title: '保存失败，请重试',
                  icon: 'none',
                  duration: 2000
                });
              }
              
              // 触发保存失败事件
              this.triggerEvent('savefail', {
                error: saveErr,
                certificateUrl: this.properties.certificateUrl
              });
            }
          });
        },
        fail: (downloadErr) => {
          wx.hideLoading();
          console.error('图片下载失败:', downloadErr);
          wx.showToast({
            title: '图片下载失败',
            icon: 'none',
            duration: 2000
          });
          
          // 触发下载失败事件
          this.triggerEvent('downloadfail', {
            error: downloadErr,
            certificateUrl: this.properties.certificateUrl
          });
        }
      });
    },

    /**
     * 处理权限错误
     */
    handlePermissionError() {
      wx.showModal({
        title: '需要相册权限',
        content: '保存图片需要访问您的相册权限，请在设置中开启',
        showCancel: true,
        cancelText: '取消',
        confirmText: '去设置',
        success: (modalRes) => {
          if (modalRes.confirm) {
            // 打开设置页面
            wx.openSetting({
              success: (settingRes) => {
                console.log('设置页面返回:', settingRes);
                if (settingRes.authSetting['scope.writePhotosAlbum']) {
                  wx.showToast({
                    title: '权限已开启，请重试保存',
                    icon: 'none',
                    duration: 2000
                  });
                }
              }
            });
          }
        }
      });
    }
  }
});
