<!--pages/practice/detail.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <custom-nav-bar
    title="{{title}}"
    desc="{{desc}}"
    show-back="{{false}}"
    nav-bar-height="{{navBarHeight}}"
    bind:back="goBack"
  >
    <view slot="rightBtn" class="nav-time">
        {{formatTime}}

    </view>

  </custom-nav-bar>

  <!-- 聊天内容区域 -->
  <chat-container
  id="chatContainer"
    messages="{{messages}}"
    is-recording="{{isRecording}}"
    total-nav-height="{{navBarHeight}}"
    keyboard-height="{{keyboardHeight}}"
    has-more="{{hasMore}}"
    is-loading="{{isLoading}}"
    page-type="practice"
    is-practice-started="{{isPracticeStarted}}"
    is-next-btn-disabled="{{isNextBtnDisabled}}"
    bind:scrollToUpper="onScrollToUpper"
    bind:playMessageAudio="playMessageAudio"
    bind:startPractice="startPractice"
    bind:getNextQuestion="getNextQuestion"
    loadingText="{{loadingText}}"
    QuestionTipText="{{questionTipText}}"
    showGetQuestionTip="{{showGetQuestionTip}}"
    bind:getQuestion="onGetQuestion"
  />
  <!-- 底部输入区域 -->
  <input-area
    input-content="{{inputContent}}"
    is-voice-mode="{{isVoiceMode}}"
    is-recording="{{isRecording}}"
    keyboard-height="{{keyboardHeight}}"
    bind:sendMessage="sendMessage"
    bind:inputFocus="onInputFocus"
    bind:inputBlur="onInputBlur"
    bind:toggleInputType="toggleInputType"
    bind:startRecording="startRecording"
    bind:stopRecording="stopRecording"
    bind:touchMove="onTouchMove"
    bind:cancelRecording="cancelRecording"
    bind:inputChange="onInputChange"
  />

  <!-- 录音提示蒙层 -->
  <recording-mask
    is-recording="{{isRecording}}"
    is-cancelling="{{isCancelling}}"
    recording-time="{{recordingTime}}"
    show-countdown="{{showCountdown}}"
    countdown-number="{{countdownNumber}}"
    bind:cancelCountdown="onCancelCountdown"
    bind:slideChange="onSlideChange"
    bind:recordingEnd="onRecordingEnd"
  />
</view>