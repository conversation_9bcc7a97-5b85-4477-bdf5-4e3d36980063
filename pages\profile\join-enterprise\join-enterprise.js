// pages/profile/join-enterprise/join-enterprise.js
import { 
  getEnterpriseByInviteCode, 
  getPositionWithLevels, 
  getPositionLevels, 
  applyToEnterprise ,
  getTopLevelDepartments
} from '../../../api/user';
const authBehavior = require('../../../behaviors/auth-behavior')
Page({
  behaviors: [authBehavior],
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      enterpriseInviteCode: '',
      enterpriseId: '',
      realName: '',
      phone: '',
      idCard: '',
      departmentId: '',
      positionId: '',
      levelId: '',
      positionTypeId:'',//岗位分类
      gender:'',//性别
      enteryTime:'',//入职时间
    },

    // 下拉选择数据
    positionNames: [],
    positionLevels: [],
    genderOptions: ['男', '女'],
    genderName:'',
    departmentList:[],

    // 下拉选择索引
    positionNameIndex: -1,
    positionLevelIndex: -1,
    genderIndex: 'male',
    
    // 表单验证状态
    isValid: false,
    
    // 加载状态
    isLoading: false,
    isLoadingEnterprise: false,
    
    // 企业信息
    enterpriseInfo: {},
    adminAccessUrl:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化页面
    this.initPage();
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 可以在这里预加载一些数据
    console.log('申请加入企业页面初始化');

  },

  /**
   * 输入企业邀请码
   */
  onInviteCodeInput(e) {
    const inviteCode = e.detail.value.trim();
    this.setData({
      'formData.enterpriseInviteCode': inviteCode,
      'formData.enterpriseId': '' // 清空企业名称
    });
    
    // 如果邀请码不为空，自动获取企业信息
    if (inviteCode) {
      this.getEnterpriseInfo(inviteCode);
    } else {
      // 清空相关数据
      this.setData({
        enterpriseInfo: null,
        positionNames: [],
        positionLevels: [],
        positionNameIndex: -1,
        positionLevelIndex: -1,
        'formData.positionId': '',
        'formData.levelId': ''
      });
    }
    
    this.validateForm();
  },

  /**
   * 根据邀请码获取企业信息
   */
  async getEnterpriseInfo(inviteCode) {
    try {
      this.setData({ isLoadingEnterprise: true });
      
      const result = await getEnterpriseByInviteCode(inviteCode);
      
      if (result) {
        this.setData({
          'formData.enterpriseId': result.uuid,
          enterpriseInfo: result,
          // adminAccessUrl:result.adminAccessUrl
          adminAccessUrl:"https://cankao-admin.dev.lingmiaoai.com"
        });
        
        // 获取该企业的岗位列表
        this.getPositionsList();
        this.getDepartmentList()
      } else {
        this.setData({
          'formData.enterpriseId': '',
          enterpriseInfo: null
        });
      }
    } catch (error) {
      console.error('获取企业信息失败:', error);
      this.setData({
        'formData.enterpriseId': '',
        enterpriseInfo: null
      });
    } finally {
      this.setData({ isLoadingEnterprise: false });
    }
  },

  /**
   * 获取部门列表
   */
  async getDepartmentList(enterpriseId) {
    try {
      const result = await getTopLevelDepartments(
        this.data.adminAccessUrl
      );
      if (result && Array.isArray(result)) {
        this.setData({
          departmentList: result,
          departmentIndex: -1,
          'formData.departmentId': ''
        });
      }
    } catch (error) {
    }
  },
   /**
   * 获取岗位名称列表
   */
   async getPositionsList() {
    try {
      const result = await getPositionWithLevels(
        this.data.adminAccessUrl
      );
      if (result && Array.isArray(result)) {
        this.setData({
          positionNames: result,
          positionNameIndex: -1,
          'formData.positionId': ''
        });
      }
    } catch (error) {
    }
  },

  /**
   * 获取岗位等级列表
   */
  async getPositionLevelsList(positionName) {
    try {
      const result = await getPositionLevels(positionName);
      if (result && Array.isArray(result)) {
        this.setData({
          positionLevels: result,
          positionLevelIndex: -1,
          'formData.levelId': ''
        });
      }
    } catch (error) {
      console.error('获取岗位等级列表失败:', error);
    }
  },

  /**
   * 输入姓名
   */
  onRealNameInput(e) {
    this.setData({
      'formData.realName': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 输入手机号
   */
  onPhoneInput(e) {
    this.setData({
      'formData.phone': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 输入身份证号
   */
  onidCardInput(e) {
    this.setData({
      'formData.idCard': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 输入归属部门
   */
  onDepartmentInput(e) {
    this.setData({
      'formData.departmentId': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 选择性别
   */
  onGenderChange(e) {
    const index = parseInt(e.detail.value);
    const gender = index==0?'male':'female';
    this.setData({
      genderName:this.data.genderOptions[index],
      genderIndex: index,
      'formData.gender': gender
    });

    this.validateForm();
  },

  /**
   * 选择入职时间
   */
  onEnteryTimeChange(e) {
    this.setData({
      'formData.enteryTime': e.detail.value
    });
    this.validateForm();
  },

  /**
   * 选择岗位名称
   */
  onPositionNameChange(e) {
    const index = parseInt(e.detail.value);
    const positionName = this.data.positionNames[index];
    
    this.setData({
      positionNameIndex: index,
      'formData.positionId': positionName || ''
    });
    
    // 获取对应的岗位等级列表
    if (positionName) {
      // this.getPositionLevelsList(positionName);
      // if (result && Array.isArray(result)) {
      //   this.setData({
      //     positionLevels: result,
      //     positionLevelIndex: -1,
      //     'formData.levelId': ''
      //   });
      // }
    } else {
      this.setData({
        positionLevels: [],
        positionLevelIndex: -1,
        'formData.levelId': ''
      });
    }
    
    this.validateForm();
  },

  /**
   * 选择岗位等级
   */
  onPositionLevelChange(e) {
    const index = parseInt(e.detail.value);
    const positionLevel = this.data.positionLevels[index];
    
    this.setData({
      positionLevelIndex: index,
      'formData.levelId': positionLevel || ''
    });
    
    this.validateForm();
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { formData } = this.data;

    // 验证必填字段
    const isValid = formData.enterpriseInviteCode.trim() !== '' &&
                   formData.enterpriseId.trim() !== '' &&
                   formData.realName.trim() !== '' &&
                   formData.phone.trim() !== '' &&
                   formData.idCard.trim() !== '' &&
                   formData.gender.trim() !== '' &&
                   formData.enteryTime.trim() !== '' &&
                   formData.departmentId.trim() !== '' &&
                   formData.positionId.trim() !== '' &&
                   formData.levelId.trim() !== '' &&
                   this.validatePhone(formData.phone) &&
                   this.validateidCard(formData.idCard);

    this.setData({ isValid });
  },

  /**
   * 验证手机号格式
   */
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  /**
   * 验证身份证号格式
   */
  validateidCard(idCard) {
    const idRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idRegex.test(idCard);
  },

  /**
   * 提交申请
   */
  async onSubmit() {
    if (!this.data.isValid) {
      wx.showToast({
        title: '请完善所有必填信息',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    try {
      this.setData({ isLoading: true });
      

      await applyToEnterprise(this.data.formData).then(res=>{
        if(res && res.success){
          wx.showToast({
            title: '申请提交成功',
            icon: 'success',
            duration: 2000
          });
          // 设置认证状态
          wx.setStorageSync('isIdentityVerified', true);
            // 延迟返回上一页
          setTimeout(() => {
            // TODO:
            // wx.navigateBack();
            this.onVerifySuccess();
          }, 1000);
        }
      })
      
    
      
    } catch (error) {
      console.error('提交申请失败:', error);
      wx.showToast({
        title: error.message || '提交申请失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ isLoading: false });
    }
  }
});
