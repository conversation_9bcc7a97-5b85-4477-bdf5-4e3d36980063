// pages/profile/join-enterprise/join-enterprise.js
import {
  getEnterpriseByInviteCode,
  getPositionWithLevels,
  getPositionLevels,
  applyToEnterprise ,
  getTopLevelDepartments
} from '../../../api/user';
import request from '../../../utils/request';
const authBehavior = require('../../../behaviors/auth-behavior')
Page({
  behaviors: [authBehavior],
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      enterpriseInviteCode: '',
      enterpriseId: '',
      realName: '',
      phone: '',
      idCard: '',
      departmentId: '',
      positionId: '',
      levelId: '',
      positionTypeId:'',//岗位分类
      gender:'',//性别
      entryTime:'',//入职时间
    },

    // 下拉选择数据
    positionNames: [],
    positionLevels: [],
    genderOptions: ['男', '女'],
    genderName:'',
    departmentList:[],

    // 新增：为picker提供显示文本的数组
    departmentNames: [],  // 部门名称数组
    positionDisplayNames: [], // 岗位名称数组
    levelDisplayNames: [], // 等级名称数组

    // 下拉选择索引
    positionNameIndex: null,
    positionLevelIndex: null,
    genderIndex: 'male',
    departmentIndex: null, // 新增部门索引

    // 表单验证状态
    isValid: false,

    // 加载状态
    isLoading: false,
    isLoadingEnterprise: false,

    // 企业信息
    enterpriseInfo: {},
    adminAccessUrl:'',
    nextPath:'/pages/profile/index'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化页面
    this.initPage();
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 可以在这里预加载一些数据
    console.log('申请加入企业页面初始化');

  },

  /**
   * 输入企业邀请码
   */
  onInviteCodeInput(e) {
    const inviteCode = e.detail.value.trim();
    this.setData({
      'formData.enterpriseInviteCode': inviteCode,
      'formData.enterpriseId': '' // 清空企业名称
    });

    // 如果邀请码不为空，自动获取企业信息
    if (inviteCode) {
      this.getEnterpriseInfo(inviteCode);
    } else {
      // 清空相关数据
      this.setData({
        enterpriseInfo: null,
        positionNames: [],
        positionLevels: [],
        positionNameIndex: -1,
        positionLevelIndex: -1,
        'formData.positionId': '',
        'formData.levelId': ''
      });
    }

    this.validateForm();
  },

  /**
   * 根据邀请码获取企业信息
   */
  async getEnterpriseInfo(inviteCode) {
    try {
      this.setData({ isLoadingEnterprise: true });

      const result = await getEnterpriseByInviteCode(inviteCode);
      console.log(result)

      if (result) {
        // 获取adminAccessUrl
        const adminAccessUrl = result.adminAccessUrl || "https://cankao-admin.dev1.lingmiaoai.com";

        this.setData({
          'formData.enterpriseId': result.uuid,
          enterpriseInfo: result,
          adminAccessUrl: adminAccessUrl
        });

        // 存储adminAccessUrl到全局配置和本地存储
        request.setAdminAccessUrl(adminAccessUrl);

        // 获取该企业的岗位列表
        this.getPositionsList();
        this.getDepartmentList()
      } else {
        this.setData({
          'formData.enterpriseId': '',
          enterpriseInfo: null
        });
      }
    } catch (error) {
      console.error('获取企业信息失败:', error);
      this.setData({
        'formData.enterpriseId': '',
        enterpriseInfo: null
      });
    } finally {
      this.setData({ isLoadingEnterprise: false });
    }
  },

  /**
   * 获取部门列表
   */
  async getDepartmentList() {
    try {
      const result = await getTopLevelDepartments(
        this.data.adminAccessUrl
      );
      console.log(result)
      if (result && Array.isArray(result)) {
        this.setData({
          departmentList: result,
          departmentIndex: null,
          'formData.departmentId': '',
          departmentNames: result.map(department => department.name)
        });
      }
    } catch (error) {
      console.error('获取部门列表失败:', error);
    }
  },
   /**
   * 获取岗位名称列表
   */
   async getPositionsList() {
    try {
      const result = await getPositionWithLevels(
        this.data.adminAccessUrl
      );
      if (result && Array.isArray(result)) {
        this.setData({
          positionNames: result,
          positionNameIndex: null,
          'formData.positionId': '',
          positionDisplayNames: result.map(position => position.positionName)
        });
      }
    } catch (error) {
    }
  },

  /**
   * 获取岗位等级列表
   */
  async getPositionLevelsList(positionName) {
    try {
      const result = await getPositionLevels(positionName, this.data.adminAccessUrl);
      if (result && Array.isArray(result)) {
        this.setData({
          positionLevels: result,
          positionLevelIndex: null,
          'formData.levelId': '',
          levelDisplayNames: result.map(level => level.name)
        });
      }
    } catch (error) {
      console.error('获取岗位等级列表失败:', error);
    }
  },

  /**
   * 输入姓名
   */
  onRealNameInput(e) {
    this.setData({
      'formData.realName': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 输入手机号
   */
  onPhoneInput(e) {
    const phone = e.detail.value.trim();
    this.setData({
      'formData.phone': phone
    });

    // 清除之前的定时器
    if (this.phoneValidationTimer) {
      clearTimeout(this.phoneValidationTimer);
    }

    // 延迟验证，避免用户输入过程中频繁提示
    this.phoneValidationTimer = setTimeout(() => {
      // 只有当手机号长度达到11位时才验证格式
      if (phone && phone.length >= 11 && !this.validatePhone(phone)) {
        wx.showToast({
          title: '请输入正确的手机号格式',
          icon: 'none',
          duration: 2000
        });
      }
    }, 500); // 500ms延迟

    this.validateForm();
  },

  /**
   * 输入身份证号
   */
  onidCardInput(e) {
    const idCard = e.detail.value.trim();
    this.setData({
      'formData.idCard': idCard
    });

    // 清除之前的定时器
    if (this.idCardValidationTimer) {
      clearTimeout(this.idCardValidationTimer);
    }

    // 延迟验证，避免用户输入过程中频繁提示
    this.idCardValidationTimer = setTimeout(() => {
      // 只有当身份证号长度达到15位或18位时才验证格式
      if (idCard && (idCard.length >= 15) && !this.validateidCard(idCard)) {
        wx.showToast({
          title: '请输入正确的身份证号格式',
          icon: 'none',
          duration: 2000
        });
      }
    }, 500); // 500ms延迟

    this.validateForm();
  },

  /**
   * 输入归属部门
   */
  onDepartmentInput(e) {
    this.setData({
      'formData.departmentId': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 选择性别
   */
  onGenderChange(e) {
    const index = parseInt(e.detail.value);
    const gender = index==0?'male':'female';
    this.setData({
      genderName:this.data.genderOptions[index],
      genderIndex: index,
      'formData.gender': gender
    });

    this.validateForm();
  },

  /**
   * 选择入职时间
   */
  onentryTimeChange(e) {
    this.setData({
      'formData.entryTime': e.detail.value
    });
    this.validateForm();
  },

  /**
   * 选择岗位名称
   */
  onPositionNameChange(e) {
    const index = parseInt(e.detail.value);
    const selectedPosition = this.data.positionNames[index];

    if (selectedPosition) {
      // 设置岗位相关信息
      this.setData({
        positionNameIndex: index,
        'formData.positionId': selectedPosition.positionId,
        'formData.positionTypeId': selectedPosition.positionTypeId
      });

      // 从岗位数据中获取等级列表
      const levels = selectedPosition.levels || [];
      this.setData({
        positionLevels: levels,
        levelDisplayNames: levels.map(level => level.levelName),
        positionLevelIndex: null,
        'formData.levelId': ''
      });
    } else {
      this.setData({
        positionNameIndex: index,
        'formData.positionId': '',
        'formData.positionTypeId': '',
        positionLevels: [],
        levelDisplayNames: [],
        positionLevelIndex: null,
        'formData.levelId': ''
      });
    }

    this.validateForm();
  },

  /**
   * 选择岗位等级
   */
  onPositionLevelChange(e) {
    const index = parseInt(e.detail.value);
    const selectedLevel = this.data.positionLevels[index];

    this.setData({
      positionLevelIndex: index,
      'formData.levelId': selectedLevel ? selectedLevel.levelId : ''
    });

    this.validateForm();
  },

  /**
   * 选择部门
   */
  onDepartmentChange(e) {
    const index = parseInt(e.detail.value);
    const selectedDepartment = this.data.departmentList[index];

    this.setData({
      departmentIndex: index,
      'formData.departmentId': selectedDepartment ? selectedDepartment.id : ''
    });

    this.validateForm();
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { formData } = this.data;

    // 验证必填字段
    const isValid = formData.enterpriseInviteCode.trim() !== '' &&
                   formData.enterpriseId.trim() !== '' &&
                   formData.realName.trim() !== '' &&
                   formData.phone.trim() !== '' &&
                   formData.idCard.trim() !== '' &&
                   formData.gender.trim() !== '' &&
                   formData.entryTime.trim() !== '' &&
                   formData.departmentId !== '' &&
                   formData.positionId !== '' &&
                   formData.levelId !== '' &&
                   this.validatePhone(formData.phone) &&
                   this.validateidCard(formData.idCard);

    this.setData({ isValid });

    // 调试信息
    console.log('表单验证结果:', {
      isValid,
      formData,
      phoneValid: this.validatePhone(formData.phone),
      idCardValid: this.validateidCard(formData.idCard)
    });
  },

  /**
   * 验证手机号格式
   */
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  /**
   * 验证身份证号格式
   */
  validateidCard(idCard) {
    const idRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idRegex.test(idCard);
  },

  /**
   * 详细验证表单并返回具体错误信息
   */
  validateFormWithDetails() {
    const { formData } = this.data;
    console.log(formData)

    // 检查必填字段
    if (!formData.enterpriseInviteCode.trim()) {
      return { isValid: false, message: '请输入企业邀请码' };
    }

    if (formData.enterpriseId=='') {
      return { isValid: false, message: '请先输入有效的企业邀请码' };
    }

    if (!formData.realName.trim()) {
      return { isValid: false, message: '请输入姓名' };
    }

    if (!formData.phone.trim()) {
      return { isValid: false, message: '请输入手机号' };
    }

    // 验证手机号格式
    if (!this.validatePhone(formData.phone)) {
      return { isValid: false, message: '请输入正确的手机号格式' };
    }

    if (!formData.idCard.trim()) {
      return { isValid: false, message: '请输入身份证号' };
    }

    // 验证身份证号格式
    if (!this.validateidCard(formData.idCard)) {
      return { isValid: false, message: '请输入正确的身份证号格式' };
    }

    if (!formData.gender.trim()) {
      return { isValid: false, message: '请选择性别' };
    }

    if (!formData.entryTime.trim()) {
      return { isValid: false, message: '请选择入职时间' };
    }

    if (formData.departmentId=='') {
      return { isValid: false, message: '请输入归属部门' };
    }

    if (formData.positionId=='') {
      return { isValid: false, message: '请选择岗位名称' };
    }

    if (formData.levelId=='') {
      return { isValid: false, message: '请选择岗位等级' };
    }

    return { isValid: true, message: '' };
  },

  /**
   * 提交申请
   */
  async onSubmit() {
    // 详细验证并给出具体提示
    const validationResult = this.validateFormWithDetails();
    if (!validationResult.isValid) {
      wx.showToast({
        title: validationResult.message,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    try {
      this.setData({ isLoading: true });


      await applyToEnterprise(this.data.formData, this.data.adminAccessUrl).then(res=>{
        if(res && res.success){
          wx.showToast({
            title: '申请提交成功',
            icon: 'success',
            duration: 2000
          });
            // 延迟返回上一页
          setTimeout(() => {
            // TODO:
            // wx.navigateBack();
            this.onVerifySuccess();
          }, 1000);
        }
      })



    } catch (error) {
      console.error('提交申请失败:', error);
      this.setData({ isLoading: false });

      wx.showToast({
        title: error.message || '提交申请失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理定时器，防止内存泄漏
    if (this.phoneValidationTimer) {
      clearTimeout(this.phoneValidationTimer);
      this.phoneValidationTimer = null;
    }

    if (this.idCardValidationTimer) {
      clearTimeout(this.idCardValidationTimer);
      this.idCardValidationTimer = null;
    }
  }
});
