// pages/profile/join-enterprise/join-enterprise.js
import { 
  getEnterpriseByInviteCode, 
  getPositions, 
  getPositionLevels, 
  submitIdentity 
} from '../../../api/user';
const authBehavior = require('../../../behaviors/auth-behavior')
Page({
  behaviors: [authBehavior],
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      inviteCode: '',
      enterpriseName: '',
      realName: '',
      phone: '',
      idNumber: '',
      department: '',
      positionName: '',
      positionLevel: ''
    },
    
    // 下拉选择数据
    positionNames: [],
    positionLevels: [],
    
    // 下拉选择索引
    positionNameIndex: -1,
    positionLevelIndex: -1,
    
    // 表单验证状态
    isValid: false,
    
    // 加载状态
    isLoading: false,
    isLoadingEnterprise: false,
    
    // 企业信息
    enterpriseInfo: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化页面
    this.initPage();
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 可以在这里预加载一些数据
    console.log('申请加入企业页面初始化');
  },

  /**
   * 输入企业邀请码
   */
  onInviteCodeInput(e) {
    const inviteCode = e.detail.value.trim();
    this.setData({
      'formData.inviteCode': inviteCode,
      'formData.enterpriseName': '' // 清空企业名称
    });
    
    // 如果邀请码不为空，自动获取企业信息
    if (inviteCode) {
      this.getEnterpriseInfo(inviteCode);
    } else {
      // 清空相关数据
      this.setData({
        enterpriseInfo: null,
        positionNames: [],
        positionLevels: [],
        positionNameIndex: -1,
        positionLevelIndex: -1,
        'formData.positionName': '',
        'formData.positionLevel': ''
      });
    }
    
    this.validateForm();
  },

  /**
   * 根据邀请码获取企业信息
   */
  async getEnterpriseInfo(inviteCode) {
    try {
      this.setData({ isLoadingEnterprise: true });
      
      const result = await getEnterpriseByInviteCode(inviteCode);
      
      if (result && result.enterpriseName) {
        this.setData({
          'formData.enterpriseName': result.enterpriseName,
          enterpriseInfo: result
        });
        
        // 获取该企业的岗位列表
        this.getPositionsList(result.id);
        
        wx.showToast({
          title: '企业信息获取成功',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: '无效的企业邀请码',
          icon: 'none',
          duration: 2000
        });
        this.setData({
          'formData.enterpriseName': '',
          enterpriseInfo: null
        });
      }
    } catch (error) {
      console.error('获取企业信息失败:', error);
      wx.showToast({
        title: error.message || '获取企业信息失败',
        icon: 'none',
        duration: 2000
      });
      this.setData({
        'formData.enterpriseName': '',
        enterpriseInfo: null
      });
    } finally {
      this.setData({ isLoadingEnterprise: false });
    }
  },

  /**
   * 获取岗位名称列表
   */
  async getPositionsList(enterpriseId) {
    try {
      const result = await getPositions(enterpriseId);
      if (result && Array.isArray(result)) {
        this.setData({
          positionNames: result,
          positionNameIndex: -1,
          'formData.positionName': ''
        });
      }
    } catch (error) {
      console.error('获取岗位列表失败:', error);
    }
  },

  /**
   * 获取岗位等级列表
   */
  async getPositionLevelsList(positionName) {
    try {
      const result = await getPositionLevels(positionName);
      if (result && Array.isArray(result)) {
        this.setData({
          positionLevels: result,
          positionLevelIndex: -1,
          'formData.positionLevel': ''
        });
      }
    } catch (error) {
      console.error('获取岗位等级列表失败:', error);
    }
  },

  /**
   * 输入姓名
   */
  onRealNameInput(e) {
    this.setData({
      'formData.realName': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 输入手机号
   */
  onPhoneInput(e) {
    this.setData({
      'formData.phone': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 输入身份证号
   */
  onIdNumberInput(e) {
    this.setData({
      'formData.idNumber': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 输入归属部门
   */
  onDepartmentInput(e) {
    this.setData({
      'formData.department': e.detail.value.trim()
    });
    this.validateForm();
  },

  /**
   * 选择岗位名称
   */
  onPositionNameChange(e) {
    const index = parseInt(e.detail.value);
    const positionName = this.data.positionNames[index];
    
    this.setData({
      positionNameIndex: index,
      'formData.positionName': positionName || ''
    });
    
    // 获取对应的岗位等级列表
    if (positionName) {
      this.getPositionLevelsList(positionName);
    } else {
      this.setData({
        positionLevels: [],
        positionLevelIndex: -1,
        'formData.positionLevel': ''
      });
    }
    
    this.validateForm();
  },

  /**
   * 选择岗位等级
   */
  onPositionLevelChange(e) {
    const index = parseInt(e.detail.value);
    const positionLevel = this.data.positionLevels[index];
    
    this.setData({
      positionLevelIndex: index,
      'formData.positionLevel': positionLevel || ''
    });
    
    this.validateForm();
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { formData } = this.data;
    
    // 验证必填字段
    const isValid = formData.inviteCode.trim() !== '' &&
                   formData.enterpriseName.trim() !== '' &&
                   formData.realName.trim() !== '' &&
                   formData.phone.trim() !== '' &&
                   formData.idNumber.trim() !== '' &&
                   formData.department.trim() !== '' &&
                   formData.positionName.trim() !== '' &&
                   formData.positionLevel.trim() !== '' &&
                   this.validatePhone(formData.phone) &&
                   this.validateIdNumber(formData.idNumber);
    
    this.setData({ isValid });
  },

  /**
   * 验证手机号格式
   */
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  /**
   * 验证身份证号格式
   */
  validateIdNumber(idNumber) {
    const idRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idRegex.test(idNumber);
  },

  /**
   * 提交申请
   */
  async onSubmit() {
    if (!this.data.isValid) {
      wx.showToast({
        title: '请完善所有必填信息',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    try {
      this.setData({ isLoading: true });
      

      await submitIdentity(this.data.formData).then(res=>{
        if(res && res.success){
          wx.showToast({
            title: '申请提交成功',
            icon: 'success',
            duration: 2000
          });
          // 设置认证状态
          wx.setStorageSync('isIdentityVerified', true);
            // 延迟返回上一页
          setTimeout(() => {
            // TODO:
            // wx.navigateBack();
            this.onVerifySuccess();
          }, 2000);
        }
      })
      
    
      
    } catch (error) {
      console.error('提交申请失败:', error);
      wx.showToast({
        title: error.message || '提交申请失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ isLoading: false });
    }
  }
});
