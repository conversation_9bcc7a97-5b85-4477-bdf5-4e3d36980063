.card {
  background-color: #fff;
  border-radius: 12px;
  margin: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.profile-card {
  background-color: #fff;
  border-radius: 12px;
  margin: 20rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  padding: 0;
}

.profile-banner {
  background:linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  height: 150rpx;
  position: relative;
}

.profile-content {
  padding: 0 20rpx 20rpx;
}

.profile-header {
  display: flex;
  margin-bottom: 20rpx;
  position: relative;
}

.profile-avatar {
  width:150rpx;
  height: 150rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 48rpx;
  margin-right: 20rpx;
  border: 4rpx solid white;
  margin-top: -45px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-info {
  flex: 1;
  padding-top: 20rpx;
}

.profile-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.profile-detail-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.profile-detail {
  color: #666;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  padding-right: 20rpx;
  border-right: 2rpx solid #eee;
}

.profile-detail:last-child {
  border-right: none;
}


.profile-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.stat-block {
  background-color: #f9f7ff;
  border-radius: 20rpx;
  padding: 20rpx;
}

.stat-label {
  color: #666;
  font-size: 24rpx;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
}

.profile-current {
  background-color: #f9f7ff;
  border-radius: 8px;
  padding: 20rpx;
  margin-top: 10rpx;
}

.profile-current-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.profile-current-title svg {
  margin-right: 5px;
}

.profile-current-company {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.profile-current-position {
  color: #666;
  font-size: 24rpx;
  display: flex;
  align-items: center;
}

.profile-current-position .position-badge {
  display: inline-block;
  padding: 1px 6px;
  border-radius: 4px;
  background-color: #e7e2fa;
  color: var(--primary-color);
  font-size: 24rpx;
  margin-left: 20rpx;
}

.section-title {
  font-weight: bold;
  margin: 10rpx 0;
  display: flex;
  align-items: center;
}


.company-card {
  background-color: #fff;
  border-radius: 24rpx;
  margin: 20rpx 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #eaeaea;
}

.company-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f9f7ff;
}

.company-name {
  font-weight: bold;
  font-size: 28rpx;
  color: var(--primary-color);
}

.company-period {
  color: #666;
  font-size: 13px;
}

.company-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
  padding: 20rpx;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item {
  flex: 1;
  padding: 0 10rpx;
}

.company-footer {
  padding: 12px 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
}

.company-positions {
  font-size: 26rpx;
  color: #666;
}

.company-arrow {
  color: #999;
}

.badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 20rpx;
  background-color: #e7e2fa;
  color: var(--primary-color);
  font-size: 12px;
  margin-right: 5px;
  margin-top: 5px;
}

.iconfont{
  color :var(--primary-color);
  margin-right:15rpx;
  font-size:26rpx;
  display:inline-block;
}