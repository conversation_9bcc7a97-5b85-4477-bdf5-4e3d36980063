/* pages/profile/company-resume.wxss */

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  background-color: #ffffff;
}

/* 导航内容 */
.nav-content {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-sizing: border-box;
  position: relative;
}

.nav-left {
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.nav-left .iconfont {
  font-size: 40rpx;
  color: #333;
}

.nav-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 45%;
  overflow: hidden;
}

.nav-title {
  font-size: 34rpx;
  color: #333;
  font-weight: bold;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-subtitle {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
  margin-top: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-right {
  width: 120rpx;
  text-align: right;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx;
}

/* 履历头部 */
.resume-header {
  background: linear-gradient(135deg, #a18cd1, #fbc2eb);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  color: #ffffff;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(161, 140, 209, 0.2);
}

.company-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.header-info {
  flex: 1;
}

.company-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.join-date {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 信息区域 */
.info-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.title-icon {
  width: 40rpx;
  height: 40rpx;
  color: #a18cd1;
  font-size: 36rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title text:nth-child(2) {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

/* 信息列表 */
.info-list {
  padding: 10rpx 0;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}

.info-item:last-child {
  border-bottom: none;
}

.item-label {
  width: 150rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.item-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
}

/* 时间线 */
.timeline {
  position: relative;
  padding: 20rpx 0 10rpx 30rpx;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 10rpx;
  top: 0;
  bottom: 0;
  width: 2rpx;
  background-color: #e0e0e0;
}

.timeline-item {
  position: relative;
  padding-bottom: 40rpx;
}

.timeline-dot {
  position: absolute;
  left: -30rpx;
  top: 6rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #a18cd1;
  border-radius: 50%;
  z-index: 1;
}

.timeline-content {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.timeline-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.timeline-date {
  font-size: 24rpx;
  color: #999;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.empty-timeline {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 电子简历 */
.resume-file {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 20rpx;
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f5e9f7;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.file-icon .iconfont {
  font-size: 40rpx;
  color: #a18cd1;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.file-size {
  font-size: 24rpx;
  color: #999;
}

.file-action {
  width: 80rpx;
  text-align: right;
}

.file-action .iconfont {
  font-size: 36rpx;
  color: #a18cd1;
}

.upload-resume {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 40rpx 0;
  margin-top: 20rpx;
}

.upload-resume .iconfont {
  font-size: 60rpx;
  color: #a18cd1;
  margin-bottom: 16rpx;
}

.upload-resume text:last-child {
  font-size: 28rpx;
  color: #666;
} 