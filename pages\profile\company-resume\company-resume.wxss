.card {
  background-color: #fff;
  border-radius: 12px;
  margin: 15px;
  padding: 20rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.profile-card {
  background-color: #fff;
  border-radius: 12px;
  margin: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  padding: 0;
}

.profile-banner {
  background: linear-gradient(135deg, #8a6dea 0%, #a18dff 100%);
  height: 100px;
  position: relative;
}

.profile-content {
  padding: 0 20rpx 20rpx;
}

.profile-header {
  display: flex;
  margin-bottom: 20rpx;
  position: relative;
}

.profile-avatar {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background-color: #8a6dea;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  margin-right: 15px;
  border: 4px solid white;
  margin-top: -45px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-info {
  flex: 1;
  padding-top: 15px;
}

.profile-name {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 8px;
}

.profile-detail-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.profile-detail {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  margin-right: 15px;
  padding-right: 15px;
  border-right: 1px solid #eee;
}

.profile-detail:last-child {
  border-right: none;
}

.profile-detail svg {
  margin-right: 5px;
  width: 16px;
  height: 16px;
}

.profile-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.stat-block {
  background-color: #f9f7ff;
  border-radius: 8px;
  padding: 12px;
}

.stat-label {
  color: #666;
  font-size: 13px;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #5a3eb8;
}

.profile-current {
  background-color: #f9f7ff;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.profile-current-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.profile-current-title svg {
  margin-right: 5px;
}

.profile-current-company {
  font-size: 16px;
  font-weight: bold;
  color: #5a3eb8;
  margin-bottom: 5px;
}

.profile-current-position {
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.profile-current-position .position-badge {
  display: inline-block;
  padding: 1px 6px;
  border-radius: 4px;
  background-color: #e7e2fa;
  color: #8a6dea;
  font-size: 12px;
  margin-left: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0;
  display: flex;
  align-items: center;
}

.section-title svg {
  margin-right: 5px;
  color: #8a6dea;
}

.company-card {
  background-color: #fff;
  border-radius: 12px;
  margin: 15px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #eaeaea;
}

.company-header {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f9f7ff;
}

.company-name {
  font-weight: bold;
  font-size: 16px;
  color: #5a3eb8;
}

.company-period {
  color: #666;
  font-size: 13px;
}

.company-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
  padding: 15px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item {
  flex: 1;
  padding: 0 10px;
}

.company-footer {
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
}

.company-positions {
  font-size: 14px;
  color: #666;
}

.company-arrow {
  color: #999;
}

.badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 15px;
  background-color: #e7e2fa;
  color: #8a6dea;
  font-size: 12px;
  margin-right: 5px;
  margin-top: 5px;
}

.certificate-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.certificate-icon {
  background-color: #ffe7e3;
  color: #ff6b6b;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.certificate-info {
  flex: 1;
}

.certificate-name {
  font-weight: bold;
  margin-bottom: 2px;
}

.certificate-date {
  font-size: 12px;
  color: #999;
}