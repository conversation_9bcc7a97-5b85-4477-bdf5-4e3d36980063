<view class="container">
  <!-- 成长路径部分 -->
   <view class="promotion-title">
     <view class="section-title">
        <text class="iconfont icon-rocket"></text>
        <text class="title-text">成长路径</text>
      </view>
     <button class="filter-button" bindtap="showPositionDrawer">
      <text class="iconfont icon-user"></text>
      <text>岗位</text>
    </button>
   </view>
 

  <view class="career-path-container">
    <!-- 当前岗位卡片 -->
    <view class="career-path-card current">
      <view class="career-path-header">
        <view class="career-info">
          <view class="career-path-title">
            <text>{{employeeInfo.position}}</text>
            <view class="level-badge l1">{{employeeInfo.level}}</view>
            <view class="career-status current">
              <text class="iconfont icon-check"></text>
              <text>当前岗位</text>
            </view>
          </view>
          <view class="career-date">
            <text class="iconfont icon-rili"></text>
            <text>等级时间：{{employeeInfo.promotionTime}}</text>
          </view>
        </view>
        <view class="career-path-icon current">
          <text class="iconfont icon-user"></text>
        </view>
      </view>
    </view>

    <!-- 连接箭头 -->
    <view class="path-connector">
      <view class="path-arrow">
        <text class="iconfont icon-down"></text>
      </view>
    </view>

    <!-- 下一岗位卡片 -->
    <view class="career-path-card next">
      <view class="career-path-header">
        <view class="career-info">
          <view class="career-path-title">
            <text>{{employeeInfo.position}}</text>
            <view class="level-badge l2">{{employeeInfo.nextLevel}}</view>
            <view class="career-status next">
              <text class="iconfont icon-down"></text>
              <text>晋升等级</text>
            </view>
          </view>
          <view class="career-date">
            <text class="iconfont icon-rili"></text>
            <text>证书数量：{{employeeInfo.certificationProgress}}</text>
          </view>
        </view>
        <view class="career-path-icon next">
          <text class="iconfont icon-user"></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 考试模块部分 -->
  <view class="exam-container">
    <view class="exam-title">
      <text class="iconfont icon-award"></text>
      <text>快来申请证书吧</text>
    </view>

    <view class="certificate-cards" wx:if="{{examModules.length > 0}}">
       <block wx:for="{{examModules}}" wx:key="id">
        <exam-module-card
          module="{{item}}"
          data-id="{{item.id}}"
          bindtap="handleButtonClick"
        />
      </block>

    </view>
     <view class="" wx:else>
      <empty-tip text="暂无证书内容" />
    </view>
  </view>

  <!-- 申请考试弹窗 -->
  <view class="popup-mask" wx:if="{{showApplyPopup}}">
    <view class="popup-content">
      <view class="popup-title">申请考试</view>
      <view class="popup-icon">
        <text class="iconfont icon-chat_conversation"></text>
      </view>
      <view class="popup-desc">
        准备好参加【{{currentModule.title}}】模块的考试了吗？
      </view>
      <view class="popup-buttons">
        <button class="popup-btn confirm" bindtap="confirmApply">确认申请</button>
        <button class="popup-btn cancel" bindtap="closePopup">取消</button>
      </view>
    </view>
  </view>

   <!-- 岗位选择抽屉 -->
  <view class="drawer-mask" wx:if="{{positionDrawerVisible}}" bindtap="closePositionDrawer" catchtouchmove="catchTouchMove"></view>
  <view class="drawer position-drawer {{positionDrawerVisible ? 'drawer-show' : ''}}" catchtouchmove="catchTouchMove">
    <view class="drawer-header">
      <text class="drawer-title">当前岗位：{{currentPosition}}</text>
      <text class="iconfont icon-close" bindtap="closePositionDrawer"></text>
    </view>
    <view class="drawer-body" wx:if="{{positions.length > 0}}">
      <view
        class="selector-item {{item.id === positionValue ? 'selector-item-active' : ''}}"
        wx:for="{{positions}}"
        wx:key="value"
        bindtap="selectPosition"
        data-position="{{item}}"
      >
        <view class="selector-icon">
          <text class="iconfont icon-user"></text>
        </view>
        <view class="selector-info">
          <text class="selector-title">{{item.name}}</text>
          <!-- <text class="selector-subtitle">{{item.desc}}</text> -->
        </view>
        <view class="selector-check" wx:if="{{item.id === positionValue}}">
          <text class="iconfont icon-check"></text>
        </view>
      </view>
    </view>
    <view class="drawer-body" wx:else>
      <empty-tip text="暂无岗位" />
    </view>
  </view>
  
</view>

<auth-popup
  visible="{{showAuthPopup}}"
  nextPath="{{nextPath}}"
  bindauthsuccess="onAuthSuccess"
  bindclose="onAuthClose"
/>
<!-- 身份认证弹窗组件 -->
  <identity-verify-popup
    visible="{{showAuthModal}}"
    nextPath="{{nextPath}}"
    bindverifysuccess="onVerifySuccess"
    bindclose="onVerifyClose"
  />
   <!-- 认证成功 -->
  <success-popup 
    visible="{{showSuccessModal}}"  
  />
